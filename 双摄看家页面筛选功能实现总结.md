# 双摄看家页面筛选功能实现总结

## 1. 已完成的功能

### 1.1 UI界面实现 ✅

#### 筛选项设计
- **双摄画面**：显示双摄像头合成画面的视频
- **固定画面**：显示枪机（固定摄像头）画面的视频  
- **云台画面**：显示球机（云台摄像头）画面的视频

#### 样式特点
- 与"全部事件"筛选项保持一致的字号和样式
- 支持选中状态的视觉反馈（蓝色背景）
- 响应式布局，适配不同屏幕尺寸

### 1.2 设备检测逻辑 ✅

```javascript
// 检测是否为双摄设备
const cameraNumber = DeviceTemplatesUtils.getConfigProject('camera', LetDevice.model).cameraNumber || '1';
const isDualCamera = cameraNumber === '2';
```

- 只有双摄设备（cameraNumber === '2'）才显示摄像头类型筛选
- 单摄设备保持原有界面不变

### 1.3 国际化支持 ✅

#### 中文文案
```javascript
dual_camera_view: '双摄画面',
fixed_camera_view: '固定画面', 
ptz_camera_view: '云台画面',
```

#### 英文文案
```javascript
dual_camera_view: 'Dual Camera View',
fixed_camera_view: 'Fixed Camera View',
ptz_camera_view: 'PTZ Camera View',
```

### 1.4 组件架构 ✅

#### 修改的文件
1. **AlarmTopSelectBar.js** - 添加摄像头类型筛选UI组件
2. **AlarmListPlayerComponent.js** - 集成摄像头类型筛选功能
3. **cn.js / en.js** - 添加国际化文案

#### 新增方法
- `getCameraTypeSelectView()` - 渲染摄像头类型选择界面
- `_onCameraTypePress()` - 处理摄像头类型选择事件

## 2. 技术实现细节

### 2.1 数据结构

#### 摄像头类型选项
```javascript
const cameraTypes = [
  { name: stringsTo('dual_camera_view'), value: 'dual' },
  { name: stringsTo('fixed_camera_view'), value: 'fixed' },
  { name: stringsTo('ptz_camera_view'), value: 'ptz' }
];
```

#### 初始化数据
```javascript
// 双摄设备的初始化数据包含三个筛选栏
let initData = [dayTitle, stringsTo('all_events_str')];
if (isDualCamera) {
  initData.push(stringsTo('dual_camera_view')); // 默认选中双摄画面
}
```

### 2.2 回调机制

#### 回调函数签名
```javascript
onCameraTypePress: (cameraType, index) => {
  // cameraType: { name: string, value: string }
  // index: 0=双摄画面, 1=固定画面, 2=云台画面
}
```

#### 当前实现状态
- ✅ UI交互完成
- ✅ 回调函数定义完成
- ⚠️ 数据筛选逻辑待实现

## 3. 待实现的功能

### 3.1 数据筛选逻辑 🔄

#### 需要实现的功能
```javascript
onCameraTypePress={(cameraType, index) => {
  // TODO: 根据选择的摄像头类型筛选视频数据
  switch(index) {
    case 0: // 双摄画面
      // 显示所有视频（双摄合成画面）
      break;
    case 1: // 固定画面  
      // 只显示枪机（sensor1）的视频
      break;
    case 2: // 云台画面
      // 只显示球机（sensor0）的视频
      break;
  }
  
  // 重新获取筛选后的数据
  this.setState({cameraTypeIndex: index}, () => {
    this.keepLoad = false;
    this._getAlarmListData();
  });
}}
```

### 3.2 API接口扩展 🔄

#### 需要扩展的接口
1. **视频列表获取接口**
   - 添加摄像头类型参数
   - 支持按sensor类型筛选

2. **数据结构扩展**
   - 视频数据中需要包含sensor信息
   - 区分sensor0（球机）和sensor1（枪机）的视频

### 3.3 状态管理 🔄

#### 需要添加的状态
```javascript
this.state = {
  // 现有状态...
  cameraTypeIndex: 0, // 当前选中的摄像头类型索引
  // 其他状态...
}
```

## 4. 使用方式

### 4.1 用户操作流程
1. 进入看家页面
2. 如果是双摄设备，会看到三个筛选栏：日期、事件类型、摄像头类型
3. 点击摄像头类型筛选栏，选择想要查看的画面类型
4. 系统根据选择筛选并显示对应的视频列表

### 4.2 默认行为
- 双摄设备默认选中"双摄画面"
- 单摄设备不显示摄像头类型筛选
- 保持向后兼容性

## 5. 测试要点

### 5.1 功能测试
- [ ] 双摄设备显示摄像头类型筛选
- [ ] 单摄设备不显示摄像头类型筛选
- [ ] 筛选项点击响应正常
- [ ] 选中状态视觉反馈正确

### 5.2 兼容性测试
- [ ] 不同型号双摄设备测试
- [ ] 横竖屏切换测试
- [ ] 多语言切换测试

### 5.3 数据测试（待实现后）
- [ ] 双摄画面数据正确
- [ ] 固定画面数据筛选正确
- [ ] 云台画面数据筛选正确

## 6. 关键文件路径

```
imilab-modules/com.chuangmi.camera.moudle/components/camera/alarm/
├── AlarmTopSelectBar.js                    # 筛选栏组件（已修改）

projects/com.chuangmi.camera/src/alarmList/
├── AlarmListPlayerComponent.js             # 视频列表组件（已修改）

globalization/string/
├── cn.js                                   # 中文国际化（已添加）
├── en.js                                   # 英文国际化（已添加）
```

## 7. 下一步工作

1. **实现数据筛选逻辑**
   - 根据摄像头类型筛选视频数据
   - 扩展API接口支持sensor类型参数

2. **完善状态管理**
   - 添加摄像头类型状态管理
   - 处理筛选条件变更

3. **测试验证**
   - 在真实双摄设备上测试功能
   - 验证数据筛选的准确性

4. **性能优化**
   - 优化筛选切换的响应速度
   - 缓存不同类型的视频数据

## 8. 注意事项

- 确保只在双摄设备上显示摄像头类型筛选
- 保持与现有筛选项的UI一致性
- 注意国际化文案的准确性
- 考虑不同设备型号的兼容性
