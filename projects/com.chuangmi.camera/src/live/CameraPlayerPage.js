import React from 'react';
import {
  Image,
  View,
  Dimensions,
  ImageBackground,
  TouchableOpacity,
  Text,
  StatusBar,
  StyleSheet,
  DeviceEventEmitter,
  TouchableHighlight,
  LogBox,
  SafeAreaView,
  TextInput,
  ScrollView,
  KeyboardAvoidingView,
  Keyboard,
} from 'react-native';

LogBox.ignoreLogs(['Require cycle:', '`new NativeEventEmitter()`', 'Remote debugger']);

const {width, height} = Dimensions.get('window');
let SCREEN_WIDTH = height > width ? width : height;
let IphoneXSeries = isIphoneXSeries() ? 14 : 0; //ios底部按钮黑线遮挡

const paddingBottom = (SCREEN_WIDTH * 76) / 360.0 + IphoneXSeries;
const maxPaddingBottom = 110 + IphoneXSeries;
//底部菜单按钮容器的高度
const BottomLayoutContainerHeight = paddingBottom > maxPaddingBottom ? maxPaddingBottom : paddingBottom;

let cur_direction = -1; //当前云台转动方位
let ptz_pressed = false; //当前是否在操作云台
let ptz_pressedPanoramic = false; //当前是否在操作全景云台

import {colors, RNLine, showLoading, showToast, RoundedButtonView, imiThemeManager} from '../../../../imilab-design-ui';
import I18n, {locales, stringsTo} from '../../../../globalization/Localize';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import Orientation from 'react-native-orientation';
import HomePageLivePlayerComponent from '../../../../imi-rn-commonView/HomePageLivePlayerComponent/HomePageLivePlayerComponent';
import HomePageLiveDualPlayerComponent from '../../../../imi-rn-commonView/HomePageLivePlayerComponent/HomePageLiveDualPlayerComponent';

import ImageButton from '../../../../imi-rn-commonView/ImageButton/ImageButton';
import WaveView from '../../../../imilab-design-ui/src/widgets/WaveView';
import {
  LetDevice,
  LetUserManger,
  IMIGotoPage,
  BaseDeviceComponent,
  imiAlarmEventCloudApi,
  IMIPackage,
  LetIMIIotRequest, PlayerClass,
} from '../../../../imilab-rn-sdk';
import JoystickControlView from '../../../../imilab-modules/com.chuangmi.camera.moudle/components/camera/ui/JoystickControlView/JoystickControlView';
import Toast from 'react-native-root-toast';
import PanoramicView from './PanoramicView';

import {XImage, XText, XView} from 'react-native-easy-app';
import IMIPermission from '../../../../imilab-rn-sdk/native/local-kit/IMIPermission';
import {isAndroid, isIos, isIphoneXSeries} from '../../../../imilab-rn-sdk/utils/Utils';
import {isWait} from '../utils/FastClickUtils';
import {GotoPageInNative, IMIStorage, LetIProperties} from '../../../../imilab-rn-sdk';
import NetInfo from '@react-native-community/netinfo';
import {IMINativeLifeCycleEvent} from '../../../../imilab-rn-sdk/native/local-kit/IMINativeLifeCycle';
import moment from 'moment';
import GiftDialog from '../../../../imilab-design-ui/src/widgets/settingUI/GiftDialog';

import AlertDialog from '../../../../imilab-design-ui/src/widgets/settingUI/AlertDialog';

import DeviceTemplatesUtils from '../../../../imilab-rn-sdk/core/device/DeviceTemplatesUtils';
import IMIHost from '../../../../imilab-rn-sdk/native/local-kit/IMIHost';

import NewGiftDialog from '../../../../imilab-design-ui/src/widgets/settingUI/NewGiftDialog';
import IMILogUtil, {LetLogUtil} from '../../../../imilab-rn-sdk/native/local-kit/IMILogUtil';
import IMILog from '../../../../imilab-rn-sdk/native/local-kit/IMILog';
import DeviceHotSpotUtils from '../utils/DeviceHotSpotUtils';
import VersionUtils from '../utils/VersionUtils';
import CommonUtils from '../utils/CommonUtils';
import {cloudDeviceService} from '../../../../imilab-rn-sdk/native/iot-kit/IMIDeviceCloudService';
import {customJsonParse} from '../utils/GenericUtils';
import IMP2pClient from '../../../../imilab-rn-sdk/native/local-kit/IMP2pClient';
import MessageDialog from '../../../../imilab-design-ui/src/widgets/settingUI/MessageDialog';
import PropTypes from 'prop-types';
import {CAMERA_CONTROL, WHITE_LIGHT} from "../../constants/Spec";
let audioNeedRecovery = true; //通话结束是否需要恢复监听状态

const tag = 'CameraPlayerPage';
const deviceHotSpotUtil = DeviceHotSpotUtils.getInstance();
let isCheckingPermission = false;
const AddFrequentlyLocationPhotoKey= LetDevice.deviceID + 'addFrequentlyLocationPhoto'
const MAX_WIDTH =Math.min(Dimensions.get('window').width,Dimensions.get('window').height) ;
const TAG = 'CameraPlayerPage';
export default class CameraPlayerPage extends BaseDeviceComponent {
  constructor(props, context) {
    super(props, context);
    this.state = {
      currentStatus: HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PREPARED,
      isFullScreen: false,
      isCalling: false, //是否正在通话
      isCallingStatue: false, //点击通话后延迟2秒
      isSleep: false, //设备是否休眠
      spotlightSliderVisible: false, //控制聚光灯亮度滑动条的隐现
      spotlightBrightness: 0,
      isPanoramic: false,
      sdCardStatus: -1, //默认为一个不存在的值，可以避免未获取到物模型也能进入回看的问题
      initStatus: 0,
      isDataUsage: false, //流量保护是否开启
      isWifi: false,
      vipState: -1,
      storageType: 0,
      panoramicType: 3,
      overAllImgStoreUrl: null,
      leftValue: 1,
      rightValue: 90,
      lensCorrect: false,
      isMoveStatus: false, //全屏长按触摸判断
      step: 0,
      isOnline: LetDevice.isOnline,
      alarmSwitch: false, //看家助手报警总开关
      oneKeySwitchValue: false, //一键警告
      spotlightMode: 1, //一键警告模式
      spotTime: 10, //一键警告持续时间
      countTime: 10, //一键警告递减时间
      showSpotlightTimer: false, //是否显示倒计时
      showOneKeyAlert: false, //未设置一键报警弹窗显示
      stateType: undefined,
      netConnected: false,
      oSDSwitch: false, //时间水印是否打开
      showFreeCloud: false, //免费云存储选择
      giftDialog: false, //免费云存领取提示
      expiredDialog: false, //云存储即将过期的弹窗提醒
      cloudExpireRemainDays: null,
      cloudExpireRemainDaysLocal: null, //本地存储的云存到期天数
      giftName: '',
      currency: 1, //人民币
      giftPrice: 0,
      giftId: null,
      deviceName: '',
      SDCardAbnormalDialog: false, //SD卡异常提示对话框
      showTopTips: false, //点击暂不后，在屏幕顶部提示
      showRTCView: true, // 因为直播播放器离开本页面后要销毁，但是销毁后要重新初始化才能播放，所以要手动控制卸载
      p2pChannelStatus: false, // p2p 通道状态，只有是通过了才能进入回看页面
      planId: null, //云存储id
      needForce: true,
      uniqueKey: 1,
      firstIn: true,
      isFinishFormat: false,
      needFormat: false,
      needFormatTip: false,
      needUpgrade: false,
      hasUpgrade: false,
      firmwareInfo: {},
      sleepRecord: false,
      stopSleep: false,
      canPlay: false,
      addFrequentlyLocation:false, //添加常用位置
      frequentlyLocationPhotoList:[], //常用位置的图片列表
      frequentlyLocationName:'',//常用位置的名字
      frequentlyLocationNameDialog:false, //常用位置的名字的弹窗
      frequentlyLocationPhotoListDelete:false, //常用位置的图片列表删除弹窗
      isAddFrequentlyLocation:false,
      frequentlyLocationNameError:'',
      keyBoardHeight:0,
      isWhiteLightOn: false, // 是否开启白光补光灯
      isAlarmPromptOn: false, // 是否开启声光报警
      whiteLightBtnEnable: true, // 白光补光灯按钮是否可点
      alarmPromptBtnEnable: true, // 声光报警按钮是否可点
    };
    this.isFirstLoad = true;
    this.isForegroundPage = true;
    this.isFcous = true;
    this.isForm = this.props?.route?.params ? (this.props.route.params.isForm ? true : false) : false;
    this.isNewFullScreen = false;
    this.closeMotorPositon = false;
    this.leaveMute = true; // 默认声音关闭
    this.userCloseHint = false; //用户手动关闭了SD卡异常提示
    this.showPlayBackGridEvent = 0; //回看是否支持宫格事件
    this.targetpushItem = null;
    this.needSyncEventPushValue = true; //进入插件需要根据看家总开关同步一下各个push的开关
    this.isPrepare = false; // prepare只能调用一回，用这个记录状态
    this.isSleepButtonDisable = false; // 休眠按钮的禁用状态
    this.p2pStatusSuccess = false; // 建立p2p通道成功
    this.msg_id = 0; // 发送p2p 消息的id,每次进来都要清空，然后依次加一
    this.operationFileInterval = null;
    this._unsubscribe = null;
    this.leaveFlag = false;
    this.areadyClick = false;
    this.handleOk = false;
    this.backFlag = false;
    this.currentNet = false;
    this.offLineTime = null;
    this.ptz_position={};
    this.currentPtz_position={};
    this.lastClickSnapPhoto = 0; //上一次点击截图的时间
    this.initFrequentlyLocationPhotoList=[]; //常用位置的图片列表标准值
  }
static propTypes = {
    showBottomLayout:  PropTypes.bool,
    showPtzCenterControl: PropTypes.bool,
  };
   static defaultProps ={
    showBottomLayout: true,
    showPtzCenterControl:false
   }
  _getPropertyFromCloud(tryOnce = true) {
    Promise.all([LetDevice.getSingleProperty('10001'), LetDevice.getSingleProperty('10004')])
      .then(data => {
        console.log('_getPropertyFromCloud:----', data);
        let stateProps = {};
        data?.forEach(item => {
          if (`${item?.thingId}` === `${10001}`) {
            console.log('10001 ', item);
            //false 代表休眠，但是我们这边isSleep ===true 代表休眠，所以要去反值
            stateProps.isSleep = !item?.value?.value;
          }
          if (`${item?.thingId}` === `${10004}`) {
            console.log('10004 ', item);
            stateProps.oSDSwitch = item?.value?.value;
          }
          this.setState(stateProps);
        });
        // console.log('updateAllPropertyCloud:', JSON.parse(data));
        // let dataObject = JSON.parse(data);
        // let stateProps = {};
        // //是否休眠
        // if (dataObject.hasOwnProperty('SleepStatus')) {
        //   stateProps.isSleep = parseInt(dataObject.SleepStatus.value) == 0;
        // }
        // //时间水印
        // if (dataObject.hasOwnProperty('OSDSwitch')) {
        //   stateProps.oSDSwitch = dataObject.OSDSwitch.value;
        // }
        // //SD卡相关
        // if (dataObject.hasOwnProperty('StorageStatus')) {
        //   let value = parseInt(dataObject.StorageStatus.value);
        //   stateProps.sdCardStatus = value;
        //   if (value == 2 || value == 5) {
        //     //未格式化
        //     if (!this.userCloseHint) {
        //       stateProps.SDCardAbnormalDialog = true;
        //       this.targetpushItem = 'error';
        //     }
        //   } else {
        //     stateProps.SDCardAbnormalDialog = false;
        //     stateProps.showTopTips = false;
        //   }
        // }
        // //新SD卡方案，需要格式化的状态
        // if (dataObject.hasOwnProperty('NeedFormatStorageMedia')) {
        //   let value = parseInt(dataObject.NeedFormatStorageMedia.value);
        //   console.log('NeedFormatStorageMedia----------', value);
        //   if ((value == 1 && stateProps.sdCardStatus == 2) || (value == 2 && stateProps.sdCardStatus == 1)) {
        //     if (!this.userCloseHint) {
        //       stateProps.needFormatMessage =
        //         value == 1
        //           ? stringsTo('sd_need_format_no_index_massage')
        //           : stringsTo('sd_need_format_has_old_index_massage');
        //       this.targetpushItem = value == 1 ? 'noIndex' : 'oldIndex';
        //       stateProps.SDCardAbnormalDialog = true;
        //     }
        //   } else if (stateProps.sdCardStatus != 2 && stateProps.sdCardStatus != 5) {
        //     stateProps.SDCardAbnormalDialog = false;
        //     stateProps.showTopTips = false;
        //   }
        // }
        // //侦测时间
        // if (dataObject.hasOwnProperty('PanoramStartMotorPositon')) {
        //   let ary = dataObject.PanoramStartMotorPositon.value.split(',');
        //   stateProps.leftValue = parseInt(ary[0].substr(1));
        // }
        // if (dataObject.hasOwnProperty('PanoramEndMotorPositon')) {
        //   let ary = dataObject.PanoramEndMotorPositon.value.split(',');
        //   stateProps.rightValue = parseInt(ary[0].substr(1));
        // }
        // //报警总开关
        // if (dataObject.hasOwnProperty('AlarmSwitch')) {
        //   if (typeof dataObject.AlarmSwitch.value === 'number') {
        //     stateProps.alarmSwitch = dataObject.AlarmSwitch.value == 1;
        //   } else {
        //     stateProps.alarmSwitch = dataObject.AlarmSwitch.value;
        //   }
        //   //看家开关关闭，则关闭所有的push开关
        //   if (!stateProps.alarmSwitch && this.needSyncEventPushValue) {
        //     this.batchCloseEventNotice();
        //   }
        // }
        // if (dataObject.hasOwnProperty('OneKeyAlarm')) {
        //   //一键警告
        //   // console.log('一键警告有值',dataObject.OneKeyAlarm,dataObject.OneKeyAlarm.value,dataObject.OneKeyAlarm.value.length);
        //   if (dataObject.OneKeyAlarm.value.length == 0) {
        //     //字符串为空  设备第一次绑定成功之后
        //     stateProps.oneKeySwitchValue = false;
        //   } else {
        //     let tempStr = JSON.parse(dataObject.OneKeyAlarm.value);
        //     stateProps.oneKeySwitchValue = tempStr.one_key_alarm_switch; // 一键警告开关
        //     stateProps.spotlightMode = tempStr.mode; // 一键警告模式
        //   }
        // } else {
        //   stateProps.oneKeySwitchValue = false;
        //   // console.log('一键警告开关无值');
        // }
        // if (stateProps.SDCardAbnormalDialog) {
        //   //如果SD卡异常，需要判断显示居中对话框还是视频顶部小tips
        //   this.dealSDAndShow().then(isShowedDialog => {
        //     stateProps.SDCardAbnormalDialog = !isShowedDialog;
        //     stateProps.showTopTips = isShowedDialog; //之前显示过对话框，则需要显示视频顶部小tips
        //     this.setState(stateProps);
        //   });
        //   return;
        // }
        // this.setState(stateProps);
      })
      .catch(e => {
        console.log('erroer10001', JSON.stringify(e));

        if (tryOnce) {
          this._getPropertyFromCloud(false);
        } //IPC062直连模式容易失败，需要重试
      });
  }

  dealSDAndShow() {
    return new Promise((resolve, reject) => {
      IMIStorage.load({
        key: (LetDevice.deviceID + 'showSDAbnormalDialog').replace(/_/g, ''),
        autoSync: true,
        syncInBackground: true,
      })
        .then(res => {
          //布尔类型
          console.log('dealSDAndShow', res.showSdDialog);
          resolve(res.showSdDialog);
        })
        .catch(_ => {
          resolve(false);
        });
    });
  }

  // 获取休眠记录状态
  loadSleepRecord() {
    return new Promise((resolve, reject) => {
      IMIStorage.load({
        key: (LetDevice.deviceID + 'showSleepDialog'),
        autoSync: true,
        syncInBackground: true,
      })
        .then(res => {
          //布尔类型
          console.log('showSleepDialog', res.showSleepDialog);
          resolve(res.showSleepDialog);
        })
        .catch(_ => {
          resolve(false);
        });
    });
  }

  componentDidMount() {

    console.log('获取当前RN获取到的宽高', JSON.stringify({width: width, height: height}))
    IMILog.logI('获取当前RN获取到的宽高', JSON.stringify({width: width, height: height}));
    this.setState({
      firstIn: true,
    })
    this.directionTime = 0;
    /*  imiAlarmEventCloudApi
      .getSlsState(LetDevice.model)
      .then(res => {
        let data = JSON.parse(res);
        console.log('jeff data = ', data);
        LetLogUtil.setSlsLog(data);
      })
      .catch(error => {
        console.log('jeff ----error:', JSON.stringify(error));
        LetLogUtil.setSlsLog(false);
      }); */

    this._subscribe_focus = this.props.navigation.addListener('focus', () => {
      this._onResume()
      if (!LetDevice.isShareUser) {
        this.confirmSDCardState();
        this.getFirmwareInfo();
      }
      this.areadyClick = false
      this.leaveFlag = false;
      this.handleOk = false;
      this.setState({showRTCView: true, initStatus: 0});
      // console.log('进来了 camera --', this.IMIVideoView);
      this.isFcous = true;
      this.isForegroundPage = true;
      this.homePageLivePlayerComponent && this.homePageLivePlayerComponent.setIsForegroundPage(this.isForegroundPage); //设置是否在前台
      if (this.state.isOnline) {
        this.getNetWork(Boolean(this.state.isConnected));
      }
      this._getPropertyFromCloud();
      this._getVipState();
      this._getLocalStorage();
      
      console.log('默认声音为关闭---全屏状态', this.leaveMute, this.isNewFullScreen);
      // 进来默认关闭声音
      // this.homePageLivePlayerComponent && this.homePageLivePlayerComponent.setMute(true);
      // if (!this.leaveMute) {
      //   // 打开声音
      //   console.log('走这里打开声音');
      //   this.homePageLivePlayerComponent && this.homePageLivePlayerComponent.setMute(false);
      // }
      // this.homePageLivePlayerComponent && this.homePageLivePlayerComponent.quitFullScreen();
      NavigationBar.setBarStyle('light-content');

      // this.homePageLivePlayerComponent&&this.homePageLivePlayerComponent.setIsNewFullScreen(this.isNewFullScreen);

      // if (LetDevice.model == 'a1Od0SjKPGt') {
      //   // 052项目 每次进入/返回 实时流页面需要获取看家助手和一键警告开关状态
      //   if (this.state.showSpotlightTimer) {
      //     this.enterTime = Date.parse(new Date());
      //     let tempTime = (this.enterTime - this.levaveTime) / 1000;
      //     // console.log('离开时间--',tempTime,this.lastTime);
      //     if (this.lastTime > tempTime) {
      //       let showTime = this.lastTime - tempTime;
      //       // console.log('显示剩余时间--',showTime);
      //       this.setState({countTime: showTime});
      //       // this.setState({spoƒtTime:showTime}) spotTime:10,//一键警告持续时间
      //       //     countTime:10,//一键警告递减时间
      //     } else {
      //       this.setState({showSpotlightTimer: false});
      //     }
      //     // console.log('当前进入时间戳',this.levaveTime,this.enterTime,this.state.showSpotlightTimer);
      //   }
      // }
      // 增加ios启用云台转动监听
      if(isIos()) {
        IMP2pClient.onFileOperateAddListener(data => {});
      }
      //每次进来都要获取一下连接状态
      IMP2pClient.getChannelState(data => {
        if (parseInt(data, 10) === 1) {
          console.log('p2p连接状态');

          this.p2pStatusSuccess = true;
        }
        // 每次进来先清空置定时器避免多次调用
        // this.operationFileInterval && clearInterval(this.operationFileInterval);
        // if (parseInt(data, 10) === 1) {
        //   this.operationFileInterval = setInterval(() => {
        //     IMP2pClient.operationFile('0', []);
        //   }, 10000);
        // }
      });
      const setLocationList=(res1,data)=>{

const res=res1
            let list =data.value.value? JSON.parse(data.value.value):[]
            const mapList = new Map();
            list.forEach((item) => {
              mapList.set(item.createTime, item)
            })
               this.initFrequentlyLocationPhotoList=list.slice(0,6)
            const list2 = JSON.parse(res)
            list2.forEach((item) => {
              if(mapList.has(item.createTime)){

                     mapList.delete(item.createTime)
                mapList.set(item.createTime, item)
              }

            })
            const newList = Array.from(mapList.values())


            const needList=newList.slice(0,6)

            this.setState({ frequentlyLocationPhotoList: needList })
      }
    LetDevice.getSingleProperty('10028').then((data)=>{

 console.log("常看位置的原始数据",data);


      if (data.value?.code == 0) {
        IMIStorage.load({
          key: AddFrequentlyLocationPhotoKey,
          autoSync: true,
          syncInBackground: true,
        }).then(res1 => {

          setLocationList(res1,data)
        }).catch((e)=>{

 setLocationList(JSON.stringify([]),data)
        })
      }
    }).catch((e)=>{
console.log("常看位置错误",e);

    })
      if (!this.state.needForce) {
        this.leaveMute = this.homePageLivePlayerComponent && this.homePageLivePlayerComponent.getCurrentMute();
        console.log('======================我强制更新了')
        this.setState({
          uniqueKey: this.state.uniqueKey + 1,
          needForce: true
        })
        return;
      }
    });

    this._subscribe_blur = this.props.navigation.addListener('blur', e => {
      this.isFcous = false;
      this.isForm = false;
      this.isForegroundPage = false;
      this.backFlag = true
      // this.leaveMute = this.homePageLivePlayerComponent && this.homePageLivePlayerComponent.getMute();
      // console.log('blur--离开当前实时页面--声音---全屏状态',this.leaveMute,this.homePageLivePlayerComponent.getFullscreen());

      // if (!this.leaveMute) {
        // 离开页面时关闭声音 重新进来时打开声音
        console.log('走这里---关闭声音');
        this.homePageLivePlayerComponent && this.homePageLivePlayerComponent.setMute(true);
      // }
      this.homePageLivePlayerComponent.setIsForegroundPage(this.isForegroundPage); //设置是否在前台

      if (this.state.showSpotlightTimer) {
        this.levaveTime = Date.parse(new Date());
        this.lastTime = this.state.countTime;
        console.log('当前离开时间戳', this.levaveTime, this.state.showSpotlightTimer, this.lastTime);
      }
      console.log('离开了', e);
      //this.IMIVideoView?.destroy && this.IMIVideoView?.destroy();
      // this.IMIVideoView = null;
      this.setState({showRTCView: false, firstIn: false});
      this.msg_id = 0;
      this.intervalQuerySD && clearInterval(this.intervalQuerySD);
      this.operationFileInterval && clearInterval(this.operationFileInterval);
    });

    this._unsubscribe = this.props.navigation.addListener('state', e => {
      const routes = e.data?.state?.routes;
      // 只有进入回看页面才不停止播放，不然就停止播放
      // if (routes?.length > 1 && routes[1]?.name !== 'PlayBackPage') {
        console.log('停止了e.data.state');
        let needStop = true;
        routes.map(res => {
          if (res.name === 'PositionSleepPage'||res.name === 'AddCommonAngles') {
            needStop = false;
            this.setState({
              needForce: needStop,
            })
          }

        })
        needStop && this.IMIVideoView?.stop && this.IMIVideoView?.stop();
      // }
    });
    this._enterBackground = IMINativeLifeCycleEvent.enterBackgroundListener.addListener(() => {
      if (this.leaveFlag) {
        return
      }
      this.leaveFlag = true;
      this.isForegroundPage = false;
      this.isForm = false;
      this.homePageLivePlayerComponent.setIsForegroundPage(this.isForegroundPage); //设置是否在前台
      //获取录屏状态,锁屏缩略图黑屏问题
      this._dealWithErrorOrPause();
      this.isNewFullScreen = this.homePageLivePlayerComponent.getFullscreen();
      console.log('首页进入后台---全屏--', this.homePageLivePlayerComponent.getFullscreen());
      console.log('后台--新的全屏状态--', this.isNewFullScreen);
      this.intervalQuerySD && clearInterval(this.intervalQuerySD);
    });

    // ios开声音进入云存购买
    this._onPauseListener = IMINativeLifeCycleEvent.onPauseListener.addListener(() => {
      if (isAndroid()) {
        return
      }
      if (this.leaveFlag) {
        return
      }
      this.leaveFlag = true;
      this.isForegroundPage = false;
      this.isForm = false;
      this.homePageLivePlayerComponent.setIsForegroundPage(this.isForegroundPage); //设置是否在前台
      //获取录屏状态,锁屏缩略图黑屏问题
      this._dealWithErrorOrPause();
      this.isNewFullScreen = this.homePageLivePlayerComponent.getFullscreen();
      console.log('首页进入后台---全屏--', this.homePageLivePlayerComponent.getFullscreen());
      console.log('后台--新的全屏状态--', this.isNewFullScreen);
      this.intervalQuerySD && clearInterval(this.intervalQuerySD);
    });

    this._enterForeground = IMINativeLifeCycleEvent.enterForegroundListener.addListener(() => {
      this.areadyClick = false
      console.log('首页进入前台--全屏--', this.homePageLivePlayerComponent.getFullscreen());
      console.log('前台----新的全屏状态--', this.isNewFullScreen);
      this.isForm = false;
      if (!this.isFcous) {
        return;
      }
      // bug1041  ios不走onResumeListener故isForegroundPage一直为false
      if (!this.isForegroundPage && isAndroid()) {
        return;
      }
      this.leaveFlag = false;
      this.isForegroundPage = true;
      this.homePageLivePlayerComponent.setIsForegroundPage(this.isForegroundPage); //设置是否在前台
      this.homePageLivePlayerComponent.setIsNewFullScreen(this.isNewFullScreen);
      console.log('这里设置全屏', this.isNewFullScreen);
      // if (this.isNewFullScreen) {
      //   // 设置全屏
      //   setTimeout(() => {
      //     this.homePageLivePlayerComponent.enterFullScreen();
      //   }, 100);
      // } else {
      //   // 设置竖屏
      //   setTimeout(() => {
      //     this.homePageLivePlayerComponent.quitFullScreen();
      //   }, 100);
      // }
      this._getPropertyFromCloud();
      if (this.state.isOnline) {
        this.getNetWork(Boolean(this.state.isConnected));
      }
      //如果显示云存过期提醒，则回到此页面刷新一下，防止购买云存回来没有移除提示页面
      if (this.state.cloudExpireRemainDays != null && this.state.cloudExpireRemainDays < 0) {
        this._getCloudExpiredRemainDays();
      }
    });

    // ios开声音进入云存购买后返回
    this._onResumeListenerIos = IMINativeLifeCycleEvent.onResumeListener.addListener(() => {
      if (isAndroid()) {
        return
      }
      this.areadyClick = false
      console.log('首页进入前台--全屏--', this.homePageLivePlayerComponent.getFullscreen());
      console.log('前台----新的全屏状态--', this.isNewFullScreen);
      this.isForm = false;
      if (!this.isFcous) {
        return;
      }
      // bug1041  ios不走onResumeListener故isForegroundPage一直为false
      if (!this.isForegroundPage && isAndroid()) {
        return;
      }
      this.leaveFlag = false;
      this.isForegroundPage = true;
      this.homePageLivePlayerComponent.setIsForegroundPage(this.isForegroundPage); //设置是否在前台
      this.homePageLivePlayerComponent.setIsNewFullScreen(this.isNewFullScreen);
      console.log('这里设置全屏', this.isNewFullScreen);
      // if (this.isNewFullScreen) {
      //   // 设置全屏
      //   setTimeout(() => {
      //     this.homePageLivePlayerComponent.enterFullScreen();
      //   }, 100);
      // } else {
      //   // 设置竖屏
      //   setTimeout(() => {
      //     this.homePageLivePlayerComponent.quitFullScreen();
      //   }, 100);
      // }
      this._getPropertyFromCloud();
      if (this.state.isOnline) {
        this.getNetWork(Boolean(this.state.isConnected));
      }
      //如果显示云存过期提醒，则回到此页面刷新一下，防止购买云存回来没有移除提示页面
      if (this.state.cloudExpireRemainDays != null && this.state.cloudExpireRemainDays < 0) {
        this._getCloudExpiredRemainDays();
      }
    });

    // this._onPauseListener = IMINativeLifeCycleEvent.onPauseListener.addListener(() => {
    //   this.isForegroundPage = false;
    //   this.isForm = false;
    //   this.homePageLivePlayerComponent.setIsForegroundPage(this.isForegroundPage); //设置是否在前台
    //   this._dealWithErrorOrPause();
    // });

    this._onResumeListener = IMINativeLifeCycleEvent.onResumeListener.addListener(() => {
      //20220323@byh 如果无焦点，后面的resume不在执行，解决开启声音，去到非rn页面返回，不在首页能听到首页声音
      this.isForm = false;
      if (!this.isFcous) {
        return;
      }
      if (isIos()) {
        //ios相册返回颜色状态栏出现错误
        // StatusBar.setBarStyle('light-content');
      }
      this.isForegroundPage = true;
      this.homePageLivePlayerComponent.setIsForegroundPage(this.isForegroundPage); //设置是否在前台
      this._getLocalStorage();
      this._getVipState();
      if (this.state.isOnline) {
        this.getNetWork(Boolean(this.state.isConnected));
      }
    });

    // Subscribe
    this.unsubscribe = NetInfo.addEventListener(state => {
      if (this.state.stateType !== state.type) {
        //切换网络类型
        this.setState({stateType: state.type});
        this.getNetWork(state.isConnected);
        if (this.homePageLivePlayerComponent && this.homePageLivePlayerComponent.getRecordStatus()) {
          if (isAndroid() && state.type == 'wifi') {
            this.homePageLivePlayerComponent._stopRecord();
          } /*else{
                        this.homePageLivePlayerComponent._saveVideoToPhotosAlbum();
                    }*/
        }
        if (state.isConnected) {
          LetDevice.getSingleProperty('10001').then(data => {
           this.setState({
            isSleep: !data?.value?.value
           })
         })
        }
      }
      this.currentNet = state.isConnected;
      //监听防止通话未挂断
      if (state.isConnected == false) {
        this._doCallClose(true);
        this.isFirstLoad = false;
        setTimeout(() => {
          this.setState({
            netConnected: this.currentNet,
          })
          if (this.homePageLivePlayerComponent.getRecordStatus()) {
            this.homePageLivePlayerComponent._stopRecord();
          }
          if (!this.currentNet) {
            this.homePageLivePlayerComponent.setLoadingView(false);
            this.homePageLivePlayerComponent.setPauseView(false);
          }
        }, 2000)
      } else {
        this.setState({
          netConnected: true,
        });
      }

      if (!state.isConnected) {
        this.homePageLivePlayerComponent.hanldeLoading()
      }

      console.log('Is connected?', state.isConnected);
    });

    // 获取云存储相关信息
    // this.getCloudInfo();

    // LetDevice.registerDeviceEventChangeListener(data => {
    //   console.log('registerDeviceEventChangeListener-----', data);
    //   let {iotId, identifier, value} = JSON.parse(data);
    //   if (iotId == LetDevice.deviceID) {
    //     if (identifier == 'PanoramaCompoundFinishE') {
    //       let {isSuccess} = value;
    //       this.intervalID && clearInterval(this.intervalID); //清除
    //       DeviceEventEmitter.emit('PanoramaCompoundFinishE', isSuccess);
    //       if (parseInt(isSuccess)) {
    //         this.getPanoramaProperty();
    //         showToast(stringsTo('panoramicSuccess'));
    //         imiAlarmEventCloudApi
    //           .panoramaGetPhotoInfo(LetDevice.deviceID)
    //           .then(res => {
    //             showLoading(false);
    //             this.setState({
    //               panoramicType: parseInt(res.status),
    //               overAllImgStoreUrl: res.url,
    //             });
    //           })
    //           .catch(error => {
    //             showLoading(false);
    //           });
    //       } else {
    //         if (this.state.panoramicType != 3) {
    //           showToast(stringsTo('panoramicError'));
    //         }
    //         this.setState({panoramicType: 3});
    //       }
    //     }
    //     if (identifier == 'PTZUpdateCoordinates'){
    //         let {coordinates} = value;
    //         if (coordinates.substr(0,3)=='-1,'){
    //             showToast(stringsTo('direction_end_009'));
    //         }
    //         // alert(coordinates);

    //     }
    //   }
    // });

    this.devicePropertyListener = LetIProperties.addPropertyChangeListener(event => {
      console.log('event-----------------', event);
      let data = typeof event === 'object' ? event : JSON.parse(event);
      //监听清晰度回调
      // if (data.StreamVideoQuality != undefined) {
      //   console.log('清晰度回调---', data.StreamVideoQuality);
      //   if (data.StreamVideoQuality == '1') {
      //     this.homePageLivePlayerComponent.setQualityIndex(0);
      //   } else if (data.StreamVideoQuality == '2') {
      //     this.homePageLivePlayerComponent.setQualityIndex(1);
      //   } else if (data.StreamVideoQuality == '3') {
      //     this.homePageLivePlayerComponent.setQualityIndex(2);
      //   }
      // }
      IMILog.logI('addPropertyChangeListener监听', JSON.stringify({data: JSON.stringify(data), deviceId: LetDevice.deviceID}));
      if (data.iotId == LetDevice.deviceID) {
        if (data.thingid == '10001') {
          console.log('监听设备状态---', data);
          this.homePageLivePlayerComponent?.setPositionShow(false)
          this.setState({isSleep: !data.value.value});
          if (!data.value.value) {
            this.setStopSleep(false)
          }
          else {
            if (this.homePageLivePlayerComponent?.getErrorStatus()) {
              this.setStopSleep(true)
            }
          }
          if (!data.value.value && this.state.isCalling) {
            this._doCallClose(true);
          }
        }
      }

      //监听SD卡状态，处理用户的插拔卡操作
      if (data.StorageStatus != undefined) {
        if (data.StorageStatus == 0 || data.StorageStatus == 4) {
          //未插卡、SD卡已弹出 需隐藏和SD卡异常相关的提示
          this.setState({
            SDCardAbnormalDialog: false,
            showTopTips: false,
          });
        } else if (data.StorageStatus == 2 || data.StorageStatus == 1) {
          //SD卡异常或者正常，需要走_getPropertyFromCloud逻辑，确认是否显示提示
          this._getPropertyFromCloud();
        }
      }

      /**
       * s首页无法监听滑动到底
       * this.isForegroundPage防止在后台时，监听到改变，导致出现提示
       */
      if (data.MotorPositon != undefined && data.MotorPositon != 'undefined' && this.isForegroundPage) {
        let arrData = data.MotorPositon.split(',');
        // console.log('监听云台方向--',data,arrData);
        if (arrData.length > 2) {
          let isMoveError = Number(arrData[0]);
          if (isMoveError < 0) {
            this.closeMotorPositon = true;
            if (isIos()) {
              if (this.isForegroundPage) {
                showToast(stringsTo('direction_end_009'));
              }
            } else {
              showToast(stringsTo('direction_end_009'));
            }
          }
        }
      }
    });

    this.deviceInfoListener = LetDevice.addInfoChangeListener(info => {
      let data = typeof info === 'object' ? info : JSON.parse(info);
      console.log('info----------' + JSON.stringify(data));
      // if (data.key == 'isOnline') {
      //   const value = JSON.parse(data?.value)
      //   if (value?.value?.online == false) {
      //     LetDevice.setIsOnline(value?.value?.online);
      //     LetDevice.setOfflineTime(moment(value?.value?.timestamp)?.format('YYYY-MM-DD HH:mm'))
      //     // 增加延时，防止后面拿到的离线时间不是实时的
      //     setTimeout(() => {
      //       this.setState({isOnline: value?.value?.online});
      //       // 暂定接收到离线后手动调用播放器暂停 bug1172
      //       if (!data.value.value) {
      //         this.IMIVideoView?.stop && this.IMIVideoView?.stop()
      //       }
      //       if (!data.value.value && this.state.isCalling) {
      //         this._doCallClose(true);
      //       }
      //     }, 100)
      //   }
      //   if (value?.value?.online) {
      //     // setTimeout(() => {
      //       LetDevice.setIsOnline(value?.value?.online);
      //       this.setState({isOnline: value?.value?.online});

      //       // this.setState({
      //       //   uniqueKey: this.state.uniqueKey + 1,
      //       //   needForce: true
      //       // }, () => {
      //         this.homePageLivePlayerComponent.updateErrStatus();
      //         // 重连后需要手动prepare
      //         this.IMIVideoView && this.IMIVideoView?.prepare();
      //       // })
      //     // }, 100)
      //   }
      // }
      if (data.iotId == LetDevice.deviceID) {
        IMILog.logI('infoChange监听', JSON.stringify(data));
        // 上下线
        if (data.thingid == 4) {
          if (data?.value?.online == false) {
            LetDevice.setIsOnline(data?.value?.online);
            LetDevice.setOfflineTime(moment(data?.value?.timestamp)?.format('YYYY-MM-DD HH:mm'))
            // 增加延时，防止后面拿到的离线时间不是实时的
            this.offLineTime && clearTimeout(this.offLineTime);
            this.offLineTime = setTimeout(() => {
              this.setState({isOnline: data?.value?.online});
              if (!data.value.online && this.homePageLivePlayerComponent.getRecordStatus()) {
                this.homePageLivePlayerComponent._stopRecord();
              }
              // 暂定接收到离线后手动调用播放器暂停 bug1172
              if (!data.value.online) {
                this.IMIVideoView?.stop && this.IMIVideoView?.stop()
              }
              if (!data.value.online && this.state.isCalling) {
                this._doCallClose(true);
              }
            }, 100)
          }
          if (data?.value?.online) {
              this.offLineTime && clearTimeout(this.offLineTime);
              LetDevice.setIsOnline(data?.value?.online);
              this.setState({isOnline: data?.value?.online});
              this.homePageLivePlayerComponent.updateErrStatus();
              this.IMIVideoView && this.IMIVideoView?.prepare();
          }
        } 
        // 解绑、取消分享
        if (data.thingid == 5) {
          if(LetUserManger.identityId == data?.value?.disconnectUserId || !data?.value?.disconnectUserId) {
            IMIGotoPage.exit();
          }
        }
      }
    });

    /**
     * 监听是否再云台校准中
     */
    this.isCalibration = DeviceEventEmitter.addListener('isCalibration', isCalibration => {
      //  showToast(stringsTo('is_the_calibration'));
      let calibrationTimes = 25000;
      if (IMIHost.serverCode == 0) {
        if (LetDevice.model == 'a1FKrifIRwH') {
          // 026 云台校准都改为到实时页
          calibrationTimes = 40000;
        } else {
          calibrationTimes = 32000;
        }
      } else {
        if (LetDevice.model == 'a1Ikkj5vsiK' || LetDevice.model == 'a1znn6t1et8') {
          // 036 021e01 都是海外服务器 云台校准都改为到实时页
          calibrationTimes = 30000;
        }
      }

      this.calibrationTimer && clearTimeout(this.calibrationTimer);
      this.calibrationTimer = setTimeout(() => {
        showLoading(stringsTo('is_the_calibration'), false);
        showToast(I18n.t('calibration_completed'));
      }, calibrationTimes);
    });

    /**
     * 每次进入页面弹出
     */
    GotoPageInNative.addStarNativeGoToPage(this.props.navigation);

    //showToast('version  '+configCameraProjectDefault.versionCode);

    this.navigationBarHeight = this.navigationBar?.getNavigationBarHeight()||this.props.navBarAddCommonAnglesBarHeight ; //页面加载后，获取当前状态栏的高度




    this.getFirmwareSupportGridEvent(); //获取当前固件是否支持回看宫格事件
    //建立连接 connect
    // IMP2pClient.connect();
  }

  _onResume() {
     this._initDeviceProp()
  }

  async _initDeviceProp() {
    const promises = [
      LetDevice.getSingleProperty(WHITE_LIGHT.ON.PIID),
      LetDevice.getSingleProperty(CAMERA_CONTROL.MANUAL_SOUND_AND_LIGHT_WARNING.PIID),
    ];

    const results = await Promise.allSettled(promises);
    let isWhiteLightOn = false
    let isAlarmPromptOn = false
    results.forEach(item => {
      if (item.status === 'fulfilled') {
        const value = item.value?.value?.value
        console.log(TAG, `thingId: ${item.value?.thingId}, value: ${value}`)
        switch (String(item.value?.thingId)) {
          case WHITE_LIGHT.ON.PIID:
            isWhiteLightOn = value
            break;
          case CAMERA_CONTROL.MANUAL_SOUND_AND_LIGHT_WARNING.PIID:
            isAlarmPromptOn = value
            break;
          default:
            break;
        }
      }
    })
    this.setState({
      isWhiteLightOn, isAlarmPromptOn
    })
  }

  getFirmwareSupportGridEvent() {
    if (LetDevice.model !== 'a1Godgpvr3D') {
      //目前仅ipc056通过固件升级支持了回看宫格事件
      return;
    }
    //请求下是否支持反向数据请求
    CommonUtils.checkIsSDRequestReverse();
    VersionUtils.getInstance()
      .get056FirmwareVersionPlayBackGridStatus()
      .then(res => {
        console.log('get056FirmwareVersionPlayBackGridStatus', res);
        this.showPlayBackGridEvent = res;
      })
      .catch(error => {
        console.log('get056FirmwareVersionPlayBackGridStatus error', error);
      });
  }

  getPanoramaProperty() {
    LetDevice.updateAllPropertyCloud()
      .then(data => {
        let dataObject = JSON.parse(data);
        // let dataObject = data;
        let stateProps = {};
        //侦测时间
        if (dataObject.PanoramStartMotorPositon) {
          let ary = dataObject.PanoramStartMotorPositon.value.split(',');
          stateProps.leftValue = parseInt(ary[0].substr(1));
        }
        if (dataObject.PanoramEndMotorPositon) {
          let ary = dataObject.PanoramEndMotorPositon.value.split(',');
          stateProps.rightValue = parseInt(ary[0].substr(1));
        }
        this.setState(stateProps);
      })
      .catch(error => {});
  }

  getNetWork(connected = true) {
    if (!this.isForegroundPage) {
      //处于后台则不作处理，防止直播流在后台启动
      return;
    }
    if (this.isForm) {
      return;
    }
    IMIStorage.load({
      key: LetDevice.deviceID + 'isDataUsageWarning',
      autoSync: true,
      syncInBackground: true,
    })
      .then(res => {
        this.setState({isDataUsage: res.isDataUsage});
        if (res.isDataUsage) {
          NetInfo.fetch().then(state => {
            //  IMILog.logD("王 是否开启流量保护 getNetWork : ",state.type)
            console.log('王 NetInfo.fetch-----', state.type);
            if (state.type == 'wifi') {
              this.setState({isWifi: true, canPlay: true, initStatus: 1})
              this.homePageLivePlayerComponent.setLoadingView(true);
              this.homePageLivePlayerComponent.setPauseView(false);
              if (!this.isFirstLoad || this.backFlag) {
                this.isFirstLoad = false;
                this.IMIVideoView?.prepare();
                this.homePageLivePlayerComponent && this.homePageLivePlayerComponent.setMute(this.homePageLivePlayerComponent?.getCurrentMute());
              }
            } else {
              this.setState({isWifi: false, canPlay: false, initStatus: 1})
              if (state.type == 'cellular') {
                showToast(stringsTo('isDataUsageTip'));
                this.IMIVideoView?.stop();
                this.homePageLivePlayerComponent.setLoadingView(false); //05-256 修复iOS不隐藏loading
                if (!this.isFirstLoad) {
                  this.homePageLivePlayerComponent.setPauseView(true);
                  this.setState({
                    currentStatus: HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PAUSE,
                  });
                }
                this.isFirstLoad = false;
              }
              // this.IMIVideoView?.stop();
            }
          });
        } else {
          // 不开流量当成wifi处理
          this.setState({isWifi: true, canPlay: true, initStatus: 1})
          if (!this.isPrepare) {
            this.isPrepare = true;
          }
          this.homePageLivePlayerComponent.setLoadingView(true);
          if (!this.isFirstLoad) {
            this.isFirstLoad = false;
            this.IMIVideoView?.prepare();
            this.homePageLivePlayerComponent && this.homePageLivePlayerComponent.setMute(this.homePageLivePlayerComponent?.getCurrentMute());
          }
        }
      })
      .catch(_ => {
        this.setState({isDataUsage: false, initStatus: 1, canPlay: true});
        if (connected && !this.isFirstLoad) {
          this.isFirstLoad = false;
          //断网状态会再次加载
          this.homePageLivePlayerComponent.setLoadingView(true);
          this.IMIVideoView?.prepare();
          this.homePageLivePlayerComponent && this.homePageLivePlayerComponent.setMute(true);
        }
      });
  }
  // 初始化播放器用于直播播放器异常重启
  initRTCView = (flag) => {
    // this.IMIVideoView?.destroy();
    this.IMIVideoView?.stop();
    //this.IMIVideoView = null;
    this.setState({showRTCView: false, firstIn: false}, () => {
      // 因为安卓那边的执行流程和js 这个便的执行流程可能不一样，这个以后要改的
      setTimeout(() => {
        this.setState({showRTCView: true}, () => {
          if (this.state.isDataUsage && flag) {
            this.handleOk = true;
            this.IMIVideoView?.prepare()
          }
        });
      }, 100);
    });
  };

  componentWillUnmount() {
    // 安卓退出插件需要增加destroy，保持与回看一致20250228
    console.log("componentWillUnmount---1111",this.props.isAddCommonAngles);
    if(this.props.isAddCommonAngles){
      // this.IMIVideoView&&this.IMIVideoView.destroy();
    }else{
      if (isAndroid()) {
          this.IMIVideoView&&this.IMIVideoView.destroy();
      }
    }

    this.directionTime = 0;
    this.offLineTime && clearTimeout(this.offLineTime);
    this.homePageLivePlayerComponent && this.homePageLivePlayerComponent.setMute(true); //设置关闭声音
    LetDevice.removeDeviceEventChangeListener();
    // this._subscribe_focus && this._subscribe_focus();
    this._subscribe_blur && this._subscribe_blur();
    this._unsubscribe && this._unsubscribe();
    this._enterBackground && this._enterBackground.remove();
    this._onPauseListener && this._onPauseListener.remove();
    this._enterForeground && this._enterForeground.remove();
    this._onPauseListener && this._onPauseListener.remove();
    this._onResumeListenerIos && this._onResumeListenerIos.remove();
    this._onResumeListener && this._onResumeListener.remove();
    this.devicePropertyListener && this.devicePropertyListener.remove();
    this.deviceInfoListener && this.deviceInfoListener.remove();
    this.timer && clearTimeout(this.timer);
    this.timerCallingStatue && clearTimeout(this.timerCallingStatue);
    this.calibrationTimer && clearTimeout(this.calibrationTimer);
    this.isCalibration && this.isCalibration.remove();
    this.intervalQuerySD && clearInterval(this.intervalQuerySD);
    GotoPageInNative.removeStarNativeGoToPage();
    this.isPrepare = false;
    // Unsubscribe
    this.unsubscribe && this.unsubscribe();
    if (this.state.isFullScreen) {
      Orientation.lockToPortrait();
    }
    this.msg_id = 0;
    this.setState = (state, callback) => {
      return;
    };
    this.keyboardDidShowListener.remove();
    this.keyboardDidHideListener.remove();
  }
UNSAFE_componentWillMount(){
  this.keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', this._keyboardDidShow.bind(this));
    this.keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', this._keyboardDidHide.bind(this));

}
_keyboardDidShow(e) {

    this.setState({
      keyBoardHeight: e.endCoordinates.height,
    });
  }
  _keyboardDidHide() {
    this.setState({
      keyBoardHeight: 0,
      // modalVisible:false
    });
  }
  _onPressBack = isFullScreen => {
    if (isFullScreen) {
      Orientation.lockToPortrait();
      this.props.navigation.setOptions({tabBarVisible: true});
    } else {
      if (!this._canStepInCall()) {
        return true;
      }
      IMIGotoPage.exit();
    }
  };

  _onClickSleepButton = () => {
    //注意：0是休眠 1是关闭休眠
    /*  let params = {
      SleepStatus: this.state.isSleep ? 1 : 0,
    }; */

    if (!this.state.isOnline) {
      showToast(stringsTo('device_offline'));
      return;
    }
    if (!this.state.netConnected) {
      showToast(stringsTo('onlyDoInLive'));
      return;
    }
    if (this._isShareUser()) {
      return;
    }
    if (!this._canStepInCall()) {
      return;
    }
    if (this.isSleepButtonDisable) {
      return;
    }
    // if (this.state.panoramicType==1||this.state.panoramicType==0){
    //     showToast(stringsTo('Panoramic_loading'));
    //     return ;

    // }

    !this.state.isSleep && this.loadSleepRecord().then(res => {
      if (!res) {
        this.setState({
          sleepRecord: true
        })
        
      }
    })

    let params = {value: this.state.isSleep};
    let paramJson = JSON.stringify(params);
    this.isSleepButtonDisable = true;
    IMILog.logI('10001 -_onClickSleepButton-setProperties', paramJson);
    LetDevice.setProperties(true, LetDevice.deviceID, '10001', paramJson)
      .then(res => {
        // this.setState({isSleep: this.state.isSleep}, () => {
          if (this.state.isSleep) {
            this.setState({isPanoramic: false});
            this.intervalID && clearInterval(this.intervalID); //清除
            // this.IMIVideoView.stop();
          } else {
            this.IMIVideoView && this.IMIVideoView.prepare();
          }
        // });
      })
      .catch(err => {
        console.log('休眠', JSON.stringify(err));
      })
      .finally(() => {
        this.isSleepButtonDisable = false;
      });

    /* IMILogUtil.setPropertyCloudWithUploadLog(JSON.stringify(params))
      .then(data => {
        // console.log("设置休眠成功-------",data);
        this.setState({isSleep: !this.state.isSleep}, () => {
          if (this.state.isSleep) {
            this.homePageLivePlayerComponent.setMute(true); //设置关闭声音
            this.setState({isPanoramic: false});
            this.intervalID && clearInterval(this.intervalID); //清除
            this.IMIVideoView.stop();
          } else {
            setTimeout(()=>{
                this.IMIVideoView.prepare();
            },200);
          }
        });
      })
      .catch(err => {
        console.log('设置休眠失败-------', err);
      }); */
  };

  //判断当前是否可以操作
  _canStepIn() {
    if (this.state.currentStatus == HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PLAYING) {
      return true;
    }
    if (!this.state.isOnline) {
      showToast(stringsTo('device_offline'));
      return false;
    }
    if (this.state.isSleep) {
      showToast(stringsTo('power_off'));
      return false;
    }
    if (!this.p2pStatusSuccess) {
      showToast(I18n.t('onlyDoInLive'));
      return false;
    }
    showToast(I18n.t('onlyDoInLive'));
    return false;
  }

  /**
   * 判断是否可通话
   * @returns {boolean}
   * @private
   */
  _canHang() {
    if (this.state.isCalling) {
      //如果是在通话中，允许用户操作挂断
      return true;
    }
    if (this.state.currentStatus == HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PLAYING) {
      return true;
    }
    if (this.state.isSleep) {
      showToast(stringsTo('power_off'));
      return false;
    }
    if (!this.state.isOnline) {
      showToast(stringsTo('device_offline'));
      return false;
    }
    if (!this.p2pStatusSuccess) {
      showToast(I18n.t('onlyDoInLive'));
      return false;
    }
    showToast(I18n.t('onlyDoInLive'));
    return false;
  }

  //判断是否通话中、录像中
  _canStepInCall() {
    if (this.state.isCalling) {
      showToast(stringsTo('imi_speaking_block'));
      return false;
    }
    if (this.homePageLivePlayerComponent.getRecordStatus()) {
      showToast(stringsTo('screen_recording'));
      return false;
    }
    return true;
  }

  _doCall() {
    if (this.state.isSleep) {
      showToast(stringsTo('power_off'));
      return false;
    }
    if (!this.state.isOnline) {
      showToast(stringsTo('device_offline'));
      return false;
    }
    IMILogUtil.uploadClickEventForCount('VoiceCall'); //统计点击通话的情况
    if (this.state.isCalling) {
      //IMP2pClient.stopAudioRecord(); //停止语音通话
      this.IMIVideoView?.stopSpeak();
      audioNeedRecovery && this.homePageLivePlayerComponent.setMute(true);
    } else {
      if (this.homePageLivePlayerComponent.getMute()) {
        //通话前监听是关闭的，则打开监听，并且结束通话后需要再次恢复到静音状态
        audioNeedRecovery = true;
        this.homePageLivePlayerComponent.setMute(false);
      } else {
        audioNeedRecovery = false;
      }

      this.IMIVideoView?.startSpeak(); // ==>开始录音
    }
    this.setState({isCalling: !this.state.isCalling, isCallingStatue: true});
    this.timerCallingStatue = setTimeout(() => {
      this.setState({
        isCallingStatue: false,
      });
    }, 1000);
  }

  _doCallClose(isNetConnected = true, status) {
    if (!status) {
      this.IMIVideoView?.stopSpeak();
    }
    audioNeedRecovery && this.homePageLivePlayerComponent.setMute(true);
    if (isNetConnected == true) {
      this.setState({
        isCalling: false,
        isCallingStatue: false,
        // netConnected: false,
      });
    } else {
      this.setState({isCalling: false, isCallingStatue: false});
      console.log('通话报错---未连接--');
    }
  }
  //顶部导航栏
  _renderNavigationBar(bps, p2pType, isFullScreen, hasUpgrade,cruiseIng) {
    let {showSynchronous056, showSDError, showTopRightSettingTitle} = DeviceTemplatesUtils.getConfigProject(
      'camera',
     LetDevice.model
    );
   const getSubtitle=()=>{
    if(this.state.isSleep){
      return null
    }
    let subtitle='';
    if( bps >= 0){
      subtitle+=`${bps}KB/S`
    }
    if(cruiseIng){
      subtitle+=` ${stringsTo('cruise_ing')}`
    }
    return subtitle
   }
    return (
      <NavigationBar
        ref={component => (this.navigationBar = component)}
        //  type={NavigationBar.TYPE.DARK}
        //  backgroundColor={'transparent'}
        title={LetDevice.devNickName}
        subtitle={getSubtitle()}
        require={hasUpgrade && !LetDevice.isShareUser}
        onLongPress={_ => {
          showToast(
            'version  ' +
              DeviceTemplatesUtils.getConfigProject('camera', LetDevice.model).versionCode +
              '\n isHermes ' +
              !!global.HermesInternal
          );
        }}
        left={[
          {
            key: NavigationBar.ICON.BACK,
            onPress: () => this._onPressBack(isFullScreen),
            accessibilityLabel: 'home_page_back', //返回
          },
        ]}
        right={[
          {
            key: NavigationBar.ICON.CUSTOM, //IPC062客户要求图标换为齿轮，底部还要加上"设置"的文字
            n_source:
              require('../../resources/images/icon_set.png'),
            bottomTitle: showTopRightSettingTitle ? stringsTo('commTitleSettingText') : null,
            onPress: _ => {
              if (isWait()) {
                return;
              } //防止快速点击进入多个设置页面
              // if (!this.state.isOnline) {
              //   showToast(stringsTo('device_offline'));
              //   return true;
              // }
              if (!this._canStepInCall()) {
                return;
              }
              console.log('IMIPackage.minApiLevel', IMIPackage.minApiLevel);
              this.props.navigation.push('CommonSettingPage');
              /* if (IMIPackage.minApiLevel >= 10005) {
                navigation.push('CommonSettingPage');
              } else {
                navigation.push('IMICommSettingPage', {
                  showDelDev: false,
                  defaultStyleRenderItemArray: [
                    {
                      title: [stringsTo('popo_setting_camera_text')],
                      onPress: () => {
                        this.props.navigation.push('IMICameraSettingVC');
                      },
                      accessibilityLabel: 'setting_camera',
                    },
                    {
                      title: [stringsTo('alarmSettingText')],
                      onPress: () => {
                        if (this._isShareUser()) {
                          return;
                        }
                        if (
                          LetDevice.model == 'a1FKrifIRwH' ||
                          LetDevice.model == 'a1Ikkj5vsiK' ||
                          LetDevice.model == 'a1znn6t1et8'
                        ) {
                          // 026 021E01 036 进入新的看家界面 原生项目转RN
                          this.props.navigation.push('HouseKeepOldNativeSetting');
                        } else {
                          this.props.navigation.push('HouseKeepSetting');
                        }
                      },
                      accessibilityLabel: 'setting_housekeeping_assistant',
                    },
                    {
                      title: [stringsTo('popo_setting_storage_text')],
                      onPress: () => {
                        if (this._isShareUser()) {
                          return;
                        }
                        if (showSynchronous056 || showSDError) {
                          this.props.navigation.push('SdCardNewPage');
                        } else {
                          if (this.state.sdCardStatus == 0) {
                            showToast(stringsTo('storage_no_sdcard_please_buy'));
                          } else if (this.state.sdCardStatus == 4) {
                            showToast(stringsTo('sdcard_error_out'));
                          } else {
                            this.props.navigation.push('SdCardSettingPage');
                          }
                        }
                        // IMIGotoPage.starNativeFileManagerSettingPage(LetDevice.deviceID)
                      },
                      accessibilityLabel: 'setting_storage',
                    },
                  ],
                });
              } */
            },
            accessibilityLabel: 'home_page_right_top_setting',
          },
        ]}
      />
    );
  }

  // 无网络状况
  netErrorView() {
    if (this.state.netConnected) {
      return;
    }
    return (
      <View
        pointerEvents="box-none"
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <XText
          style={{
            textAlign: 'center',
            color: colors.white,
            fontSize: 15,
          }}
          text={stringsTo('netWorkError')}
        />

        <View
          style={{
            width: '100%',
            justifyContent: 'space-evenly',
            flexDirection: 'row',
          }}>
          <RoundedButtonView
            buttonText={stringsTo('error_code_common_retry')}
            buttonStyle={{
              marginTop: 30,
              paddingHorizontal: 20,
              height: 40,
              borderRadius: 7,
            }}
            buttonTextStyle={{textAlign: 'center'}}
            onPress={() => {
              if(this.state.netConnected){
                this.homePageLivePlayerComponent.handleNetReload()
              }
            }}
            accessibilityLabel={'error_code_common_retry'}
          />
          <RoundedButtonView
            buttonText={stringsTo('error_help')}
            buttonStyle={{
              marginTop: 30,
              paddingHorizontal: 20,
              height: 40,
              borderRadius: 7,
            }}
            buttonTextStyle={{textAlign: 'center'}}
            onPress={() => {
              //播放器发生错误时，点击查看帮助
              this.homePageLivePlayerComponent.hanldeHelp()
            }}
            accessibilityLabel={'error_help'}
          />
        </View>
      </View>
    );
  }
  /*覆盖在播放器上的View */
  renderVideoSubView(isFullScreen, showFullScreenTools) {
    //竖屏时没有要显示的控件，竖屏显示拨号键、云台 是否显示云台
    let {showPanoramicView, showCallAndAlarmView, showSoundLightAlarm} = DeviceTemplatesUtils.getConfigProject('camera', LetDevice.model); //是否显示圆盘
    return (
      <View
        style={{
          width: '100%',
          height: '100%',
          alignItems: 'center',
          justifyContent: 'center',
          position: 'absolute',
        }}
        pointerEvents={'box-none'}>
        {showSoundLightAlarm? this._renderCallAlarmLightViewFullScreen(isFullScreen, showFullScreenTools): this._renderCallViewFullScreen(isFullScreen, showFullScreenTools)}
        {showPanoramicView ? null : this._rendLandscapeDirectionView(isFullScreen, showFullScreenTools)}
        {this.netErrorView()}
        {this._deviceOffLineView()}
        {this._DirectConnectHintView()}
        {this._renderTargetPushView(isFullScreen)}
      </View>
    );
  }
  //横屏时的拨号键,报警,白光灯
  _renderCallAlarmLightViewFullScreen(isFullScreen, showFullScreenTools) {
    const {isWhiteLightOn, isAlarmPromptOn, whiteLightBtnEnable, alarmPromptBtnEnable} = this.state
    if (!isFullScreen || !showFullScreenTools) {
      return null;
    }
    return (
      <View
        pointerEvents="auto"
        style={{
          position: 'absolute',
          right: 30,
          backgroundColor: 'transparent',
          bottom: 30,
        }}>
        <ImageButton
          style={{width: 44, height: 44}}
          source={
            this.state.isCalling
              ? require('../../resources/images/icon_calling.png')
              : require('../../resources/images/icon_call_fill.png')
          }
          onPress={() => {
            if (!this._canStepIn()) {
              return;
            }
            isCheckingPermission = true;
            this.homePageLivePlayerComponent?.updateCheckingPermission(true)
            IMIPermission.startCheckPermission(IMIPermission.PERMISSION_RECORD_AUDIO, status => {
              if (status === 0) {
                this._doCall();
                // ios权限进入后台再进入前台有延时
                setTimeout(() => {
                  isCheckingPermission = false;
                  this.homePageLivePlayerComponent?.updateCheckingPermission(false)
                }, 500)
              } else if (status === -1) {
                isCheckingPermission = false;
                this.homePageLivePlayerComponent?.updateCheckingPermission(false)
                showToast(stringsTo('audio_permission_denied'));
              }
            });
          }}
        />
        <ImageButton
          style={{width: 44, height: 44, marginTop: 46}}
          source={isAlarmPromptOn? require('../../resources/images/icon_warn_on_fill.png'): require('../../resources/images/icon_warn_off_fill.png')}
          disabled={!alarmPromptBtnEnable}
          onPress={() => {
            if (this._isShareUser()) {
              return;
            }
            this.onAlarmPromptPress()
          }}
        />
        <ImageButton
          style={{width: 44, height: 44, marginTop: 46}}
          source={isWhiteLightOn? require('../../resources/images/icon_white_light_on.png'): require('../../resources/images/icon_white_light_off_white.png')}
          disabled={!whiteLightBtnEnable}
          onPress={() => {
            if (this._isShareUser()) {
              return;
            }
            this.onWhiteLightPress()
          }}
        />
      </View>
    );
  }
  //横屏时的拨号键 包含报警
  _renderCallAndAlarmViewFullScreen(isFullScreen, showFullScreenTools) {
    if (!isFullScreen || !showFullScreenTools) {
      return null;
    }
    return (
      <View
        pointerEvents="auto"
        style={{
          position: 'absolute',
          right: 30,
          top:'50%',
          width:"100%",
          height:'100%',
          backgroundColor: 'transparent',
        }}>
        <ImageButton
          style={{width: 44, height: 44}}
          source={
            this.state.isCalling
              ? require("../../resources/images/icon_call_ing_1.png")
              : require("../../resources/images/icon_call_start_1.png")
          }
          highlightedSource={
            this.state.isCalling
                ? require("../../resources/images/icon_call_ing_1.png")
              : require("../../resources/images/icon_call_start_1.png")
          }
          onPress={() => {
            if (!this._canStepIn()) {
              return;
            }
            isCheckingPermission = true;
            this.homePageLivePlayerComponent?.updateCheckingPermission(true)
            IMIPermission.startCheckPermission(IMIPermission.PERMISSION_RECORD_AUDIO, status => {
              if (status === 0) {
                this._doCall();
                // ios权限进入后台再进入前台有延时
                setTimeout(() => {
                  isCheckingPermission = false;
                  this.homePageLivePlayerComponent?.updateCheckingPermission(false)
                }, 500)
              } else if (status === -1) {
                isCheckingPermission = false;
                this.homePageLivePlayerComponent?.updateCheckingPermission(false)
                showToast(stringsTo('audio_permission_denied'));
              }
            });
          }}
        />
        {this.state.showSpotlightTimer ? (
          <View
            style={{
              width: 44,
              height: 44,
              marginTop: 40,
              borderRadius: 22,
              backgroundColor: 'red',
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <Text style={{fontSize: 14, color: '#FFFFFF'}}>{this.state.countTime + 'S'}</Text>
          </View>
        ) : (
          <ImageButton
            style={{width: 44, height: 44, marginTop: 40}}
            source={require('../../resources/images/icon_alarm_off_white.png')}
            highlightedSource={require('../../resources/images/icon_alarm_off_white_p.png')}
            onPress={() => {
              if (this._isShareUser()) {
                return;
              }
              this.setState({isFullScreen: true}, callback => {
                this._onClickWarnButton();
              });
            }}
            // onPress={this._onClickWarnButton}
          />
        )}
      </View>
    );
  }
  //横屏时的拨号键
  _renderCallViewFullScreen(isFullScreen, showFullScreenTools) {
    if (!isFullScreen || !showFullScreenTools) {
      return null;
    }
    return (
      <View style={{position: 'absolute',top:'45%', right: 30,height:'100%',}}>
        <ImageButton
          style={{width: 44, height: 44}}
          source={
   this.state.isCalling
            ? require("../../resources/images/icon_call_ing_1.png")
              : require("../../resources/images/icon_call_start_1.png")
          }
          /*  source={this.state.isCalling ? require("../../resources/images/icon_hangup_fullscreen.png") : require("../../resources/images/icon_call_fullscreen.png")}*/
         highlightedSource={
          this.state.isCalling
             ? require("../../resources/images/icon_call_ing_1.png")
              : require("../../resources/images/icon_call_start_1.png")
         }
          // highlightedSource={
          //   this.state.isCalling
          //     ? this.state.isCallingStatue
          //       ? require('../../resources/images/icon_hangup_big_p.png')
          //       : require('../../resources/images/icon_hangup_fullscreen.png')
          //     : this.state.isCallingStatue
          //     ? require('../../resources/images/icon_call_p.png')
          //     : require('../../resources/images/icon_call_fullscreen.png')
          // }
          onPress={() => {
            if (this.state.isCallingStatue) {
              return;
            }
            if (this.state.isSleep) {
              showToast(stringsTo('power_off'));
              return false;
            }
            if (!this._canHang()) {
              return;
            }
            isCheckingPermission = true;
            this.homePageLivePlayerComponent?.updateCheckingPermission(true)
            IMIPermission.startCheckPermission(IMIPermission.PERMISSION_RECORD_AUDIO, status => {
              if (status === 0) {
                this._doCall();
                // ios权限进入后台再进入前台有延时
                setTimeout(() => {
                  isCheckingPermission = false;
                  this.homePageLivePlayerComponent?.updateCheckingPermission(false)
                }, 500)
              } else if (status === -1) {
                isCheckingPermission = false;
                this.homePageLivePlayerComponent?.updateCheckingPermission(false)
                showToast(stringsTo('audio_permission_denied'));
              }
            });
          }}
          accessibilityLabel={
            this.state.isCalling ? 'home_page_phone_on_full_screen' : 'home_page_phone_off_full_screen'
          }
        />
      </View>
    );
  }

  /*横屏云台View*/
  _rendLandscapeDirectionView(isFullScreen, showFullScreenTools) {

      return (
        <View
          style={{
            position: 'absolute',
            left: 30,
            bottom: 15,

            display:isFullScreen && showFullScreenTools?'flex':'none'
          }}>
          <JoystickControlView
            onStart={() => {}}
            onMove={type => {
              if (!this._canStepIn()) {
                return;
              }
              ptz_pressed = false;

              this.setState({isMoveStatus: true});
              clearInterval(this.setPTZIntervalID);
              this._doDirection(type);
            }}
            onLoosen={() => {
              ptz_pressed = false;
              this.setState({isMoveStatus: false});
              this.homePageLivePlayerComponent._onPressFullScreenTools();
              clearInterval(this.setPTZIntervalID);
              // this._getMotorPositonStatus();
            }}
            isFullscreen={true}
            diameterPan={160}
            diameterMid={36}
          />
        </View>
      );

  }
  //通话按钮控件
  _renderCallView() {
    return (
      <View
        style={{
          flex: 1,
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'center',
          //   backgroundColor: "white"
        }}>
        <ImageButton
          style={{width: 52, height: 52}}
          source={
            !this.state.isSleep
              ? this.state.isCalling
                ? this.state.isCallingStatue
                  ? require('../../resources/images/icon_call_p.png')
                  : require('../../resources/images/icon_hangup_fullscreen.png')
                : this.state.isCallingStatue
                ? require('../../resources/images/icon_hangup_big_p.png')
                : require('../../resources/images/icon_call_fullscreen.png')
              : require('../../resources/images/icon_call_p.png')
          }
          highlightedSource={
            !this.state.isSleep
              ? this.state.isCalling
                ? this.state.isCallingStatue
                  ? require('../../resources/images/icon_call_p.png')
                  : require('../../resources/images/icon_hangup_fullscreen.png')
                : this.state.isCallingStatue
                ? require('../../resources/images/icon_hangup_big_p.png')
                : require('../../resources/images/icon_call_fullscreen.png')
              : require('../../resources/images/icon_call_p.png')
          }
          onPress={() => {
            if (this.state.isCallingStatue) {
              return;
            }
            if (!this._canStepIn()) {
              return;
            }
            isCheckingPermission = true;
            this.homePageLivePlayerComponent?.updateCheckingPermission(true)
            IMIPermission.startCheckPermission(IMIPermission.PERMISSION_RECORD_AUDIO, status => {
              if (status === 0) {
                this._doCall();
                // ios权限进入后台再进入前台有延时
                setTimeout(() => {
                  isCheckingPermission = false;
                  this.homePageLivePlayerComponent?.updateCheckingPermission(false)
                }, 500)
              } else if (status === -1) {
                showToast(stringsTo('audio_permission_denied'));
                isCheckingPermission = false;
                this.homePageLivePlayerComponent?.updateCheckingPermission(false)
              }
            });
          }}
        />
      </View>
    );
  }
  /*绘制摇杆式云台View*/
  renderPtzControlView() {
    let {showAllPanoramara} = DeviceTemplatesUtils.getConfigProject('camera', LetDevice.model); //是否显示圆盘
    // console.log('是否显示全景绘图---panoramic111111', showAllPanoramara, this.state.isPanoramic);
    return (
      <View
        style={{
          /*flexGrow: 1,
                width: "100%",*/ //flexGrow在小屏手机上会导致底部导航栏被挤压  bug#7181
          flex: 1,
          flexDirection: 'column',
          display: 'flex',
          // marginTop: 20,
          paddingRight: 14,
          paddingLeft: 14,
          alignItems: 'center',
          justifyContent: 'center',
          position: 'relative',
        }}>
        {this.state.isPanoramic ? (
          <PanoramicView
            Type={this.state.panoramicType}
            accessibilityLabel={'show_all_panorama'}
            onTouch={pointX => {
              this.selectPositionX = pointX;
              ptz_pressedPanoramic = false;
              console.log(' _setPanoramaRotateAngle h=', this.selectPositionX, ',v=', this.selectPositionY);
              //this._doDirectionPanorama(this.selectPositionX, 50);
            }}
            onLoosen={() => {}}
            startPanoramic={() => {
              if (!this._canStepIn()) {
                return;
              }
              if (!this.state.isOnline) {
                showToast(stringsTo('device_offline'));
                return false;
              }
              if (this.state.isSleep) {
                showToast(stringsTo('power_off'));
                return false;
              }
              IMILogUtil.uploadClickEventForCount('PanoramaEnableComposition');
              LetDevice.sendDeviceServerRequest('PanoramaEnableComposition', {})
                .then(data => {
                  this.setState({panoramicType: 1});
                  this.timer && clearTimeout(this.timer);
                  this.intervalID && clearInterval(this.intervalID); //清除
                  this._panoramicTimeout();
                })
                .catch(error => {});
            }}
            endPanoramic={() => {
              // if (this.state.panoramicType == 1){
              //     showToast(stringsTo('panoramicing_tip'));
              //     return;
              // }
              this.setState({isPanoramic: false});
            }}
            selectPositionX={this.selectPositionX}
            minSelectPositionLeft={0}
            maxSelectPositionRight={SCREEN_WIDTH - 28}
            leftValue={this.state.leftValue}
            rightValue={this.state.rightValue}
            imgStoreUrl={this.state.overAllImgStoreUrl}
          />
        ) : (
          <JoystickControlView
            onMove={type => {
              if (!this._canStepIn()) {
                return;
              }
              if (!this.state.isOnline) {
                showToast(stringsTo('device_offline'));
                return false;
              }
              if (this.state.isSleep) {
                showToast(stringsTo('power_off'));
                return false;
              }
              ptz_pressed = false;
              clearInterval(this.setPTZIntervalID);
              this._doDirection(type);
            }}
            onLoosen={() => {
              // 暂时return
              ptz_pressed = false;
              clearInterval(this.setPTZIntervalID);
              // this._getMotorPositonStatus();
            }}
            isFullscreen={false}
            diameterPan={height < 680 ? 150 : 206}
            diameterMid={40}
            hiedPan={!this.props.showPtzCenterControl}
          />
        )}

        {showAllPanoramara ? (
          <XImage
            raw={true}
            style={{
              position: 'absolute',
              right: 15,
              bottom: 15,
            }}
            iconSize={50}
            icon={
              this.state.isPanoramic
                ? require('../../resources/images/icon_holder.png')
                : require('../../resources/images/icon_panoramic.png')
            }
            onPress={() => {
              // 暂时return
              if (this._isShareUser()) {
                return;
              }
              if (!this._canStepIn()) {
                return;
              }
              let isPanoramic = !this.state.isPanoramic;
              if (isPanoramic) {
                showLoading(stringsTo('commLoadingText'), true);
                imiAlarmEventCloudApi
                  .panoramaGetPhotoInfo(LetDevice.deviceID)
                  .then(res => {
                    showLoading(false);
                    console.log('-------------' + res.url);
                    // parseInt(res.status)==1||parseInt(res.status)==0?3:
                    let isEnabledOpen = parseInt(res.status) == 2 ? true : false;
                    this.setState({
                      panoramicType: !isEnabledOpen && res.timeOutFlag ? 3 : parseInt(res.status),
                      overAllImgStoreUrl: res.url,
                      isPanoramic: isPanoramic,
                    });
                  })
                  .catch(error => {
                    showLoading(false);
                    this.setState({isPanoramic: isPanoramic});
                  });
              } else {
                if (this.state.panoramicType == 1) {
                  showToast(stringsTo('panoramicing_tip'));
                  return;
                }
                this.setState({isPanoramic: isPanoramic});
              }
            }}
          />
        ) : null}

        {/* 常看位置的小眼睛 */}
       {this.props.showPtzCenterControl?<TouchableOpacity
       onPress={()=>{
          if (Math.abs(this.lastClickSnapPhoto-new Date().getTime())  < 1000) {
      //为了避免快速点击导致的截屏白屏问题，每秒只给截图一次
      return;
    }
    if (!this._canStepIn()) {
      return;
    }
    this.lastClickSnapPhoto = new Date().getTime();

            if(this.state.frequentlyLocationPhotoList.length<6){

  this.setState({ frequentlyLocationNameDialog: true ,frequentlyLocationName: '',});
            }else{
showToast(stringsTo('limit_reached_v6'))

            }

       }}
       style={{
        position: 'absolute',
        backgroundColor:'#12AA9C',
        borderRadius:24,
        width:"100%",
        marginLeft:24,
        marginRight:24,
        paddingBottom:13,
        paddingTop:13,
        bottom:24
       }}
       ><Text style={{textAlign:'center',
        color:'#FFFFFF',}}>{stringsTo("account_next")}</Text></TouchableOpacity>:
        <TouchableOpacity style={{position: 'absolute', 
        top: '50%',
        left: '50%',
         transform: [{translateX: -10}, {translateY: -25}]}} onPress={() => {
           if (!this._canStepIn()) {
              return;
            }
            if(this.state.frequentlyLocationPhotoList.length<6){
this.setState({addFrequentlyLocation:true})
            }else{
showToast(stringsTo('limit_reached_v6'))
setTimeout(() => {
  this.setState({addFrequentlyLocation:true})
}, 200);
            }

        }}>
     <Image style={{width:50,height:50}}  source={require('../../resources/images/small_eyes.png')}/>
    
    
        </TouchableOpacity>}
     
      </View>
    );
  }
  //通话和报警按钮控件
  _renderCallAndAlarmView() {
    return (
      <View
        style={{
          flex: 1,
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: 'white',
        }}>
        <ImageButton
          style={{width: 52, height: 52, marginRight: 60}}
          source={
            this.state.isCalling
              ? require('../../resources/images/icon_hangup.png')
              : require('../../resources/images/icon_call_big.png')
          }
          highlightedSource={
            this.state.isCalling
              ? require('../../resources/images/icon_hangup.png')
              : require('../../resources/images/icon_call_p.png')
          }
          onPress={() => {
            if (!this._canStepIn()) {
              return;
            }
            isCheckingPermission = true;
            this.homePageLivePlayerComponent?.updateCheckingPermission(true)
            IMIPermission.startCheckPermission(IMIPermission.PERMISSION_RECORD_AUDIO, status => {
              if (status === 0) {
                this._doCall();
                // ios权限进入后台再进入前台有延时
                setTimeout(() => {
                  isCheckingPermission = false;
                  this.homePageLivePlayerComponent?.updateCheckingPermission(false)
                }, 500)
              } else if (status === -1) {
                showToast(stringsTo('audio_permission_denied'));
                isCheckingPermission = false;
                this.homePageLivePlayerComponent?.updateCheckingPermission(false)
              }
            });
          }}
        />
        {this.state.showSpotlightTimer ? (
          <View
            style={{
              width: 52,
              height: 52,
              borderRadius: 26,
              backgroundColor: 'red',
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <Text style={{fontSize: 14, color: '#FFFFFF'}}>{this.state.countTime + 'S'}</Text>
          </View>
        ) : (
          <ImageButton
            style={{width: 52, height: 52}}
            source={require('../../resources/images/icon_alarm_off.png')}
            highlightedSource={require('../../resources/images/icon_alarm_off_p.png')}
            onPress={() => {
              if (this._isShareUser()) {
                return;
              }
              this.setState({isFullScreen: false}, callback => {
                this._onClickWarnButton();
              });
            }}
            // onPress={this._onClickWarnButton}
          />
        )}
      </View>
    );
  }
  //点击报警按钮
  _onClickWarnButton = () => {
    if (this.state.isCalling) {
      showToast(stringsTo('imi_speaking_block'));
      return;
    }

    if (!this._canStepIn()) {
      return;
    }
    // console.log('离线是不是走这里了');
    if (this.state.alarmSwitch) {
      if (this.state.oneKeySwitchValue) {
        //看家助手打开,且一键报警打开
        // 1闪烁 2 不亮 0 常亮
        showLoading(stringsTo('commWaitText'), true);
        let timestamp = Date.parse(new Date());
        let params = {click_time: timestamp.toString()};
        // let params = {"click_time":'1622719406'};
        // console.log('当前时间戳--',timestamp,JSON.stringify(params))
        LetDevice.sendDeviceServerRequest('TriggerOneKeyAlarm', JSON.stringify(params))
          .then(data => {
            // console.log('触发一键报警' + data);
            showLoading(false);
            this.setState({showSpotlightTimer: true});
            this.countdownTimer = setInterval(() => {
              //从10秒开始倒计时
              let time = this.state.countTime - 1;
              if (time <= 0) {
                //倒计时结束，停止视频播放
                this.countdownTimer && clearInterval(this.countdownTimer);
                this.setState({
                  showSpotlightTimer: false,
                  countTime: this.state.spotTime,
                });
              } else {
                this.setState({
                  countTime: time,
                });
              }
            }, 1000);
          })
          .catch(error => {
            showToast(I18n.t('operationFailed'));
            showLoading(false);
            // console.log('触发一键报警 error ' + error)
          });
      } else {
        //看家助手打开，一键报警未打开
        this.setState({showOneKeyAlert: true});
      }
    } else {
      //看家助手开关未打开
      this.setState({showOneKeyAlert: true});
    }
  };
  //一键警告dialog
  _renderOneKeySetDialog() {
    return (
      <AlertDialog
        title={I18n.t('set_onekey_tit')}
        visible={this.state.showOneKeyAlert}
        message={I18n.t('set_onekey_msg')}
        messageStyle={{
          marginBottom: 14,
          marginHorizontal: 28,
          fontSize: 12,
          // fontWeight: '500',
          color: '#7F7F7F',
        }}
        canDismiss={true}
        onDismiss={() => {
          this.setState({showOneKeyAlert: false});
        }}
        buttons={[
          {
            text: I18n.t('cancel'),
            callback: _ => {
              this.setState({showOneKeyAlert: false});
            },
          },
          {
            text: I18n.t('set_onekey_sure'),
            callback: _ => {
              this.setState({showOneKeyAlert: false});
              if (this.state.alarmSwitch) {
                if (!this.state.oneKeySwitchValue) {
                  // 未打开一键警告  进入一键警告页面
                  if (this.state.isFullScreen) {
                    this.homePageLivePlayerComponent && this.homePageLivePlayerComponent.quitFullScreen();
                    this.setState({isFullScreen: false}, callback => {
                      this.props.navigation.push('ClickWarningSetPage', {
                        enterType: 'liveVideo',
                      });
                    });
                    return;
                  }
                  this.props.navigation.push('ClickWarningSetPage', {
                    enterType: 'liveVideo',
                  });
                }
              } else {
                //未打开看家助手  进入看家助手界面
                if (this.state.isFullScreen) {
                  this.homePageLivePlayerComponent && this.homePageLivePlayerComponent.quitFullScreen();
                  this.setState({isFullScreen: false}, callback => {
                    this.props.navigation.push('HouseKeepSetting');
                  });
                  return;
                }
                this.props.navigation.push('HouseKeepSetting');
              }
            },
          },
        ]}
      />
    );
  }
  //
  _panoramicTimeout() {
    this.timer = setTimeout(() => {
      let times = 0;
      this.intervalID = setInterval(() => {
        times = times + 1;
        if (times > 7) {
          //rcp20秒后才开始拉取图片，每10秒拉一次6次即1分钟+20秒后还拉不到图片，认为失败了

          this.intervalID && clearInterval(this.intervalID); //清除
          this._getMergePhotoMeta(true);
          return;
        }
        //获取全景图信息
        this._getMergePhotoMeta(false);
      }, 10000);
    }, 20000);
  }

  _getMergePhotoMeta(isLast) {
    imiAlarmEventCloudApi
      .panoramaGetPhotoInfo(LetDevice.deviceID)
      .then(res => {
        console.log('-------------' + res.url);
        if (!isLast && parseInt(res.status) == 2) {
          this.setState({
            panoramicType: parseInt(res.status),
            overAllImgStoreUrl: res.url,
          });
        }
        if (isLast) {
          if (parseInt(res.status) != 2) {
            showToast(stringsTo('operate_time_out'));
          }
          let isEnabledOpen = parseInt(res.status) == 2 ? true : false;
          //parseInt(res.status)==1||parseInt(res.status)==0?3: 根据后台状态显示
          // this.setState({panoramicType:3});
          this.setState({
            panoramicType: !isEnabledOpen && res.timeOutFlag ? 3 : parseInt(res.status),
            overAllImgStoreUrl: res.url,
          });
        }
      })
      .catch(error => {
        if (isLast) {
          this.setState({panoramicType: 3});
        }
      });
  }
  //获取云台位置的回调，常看位置
  _getPtzAreaCallback(data){
   if (!this.isFcous) {
        return;
      }

  const index=  this.state.frequentlyLocationPhotoList.findIndex((item)=>{
  const pos=JSON.parse(item.pos)
  // x和y的差值都小于5也认识是近似角度
  const xDiff=Math.abs(pos[0]-data.position_x)
  const yDiff=Math.abs(pos[1]-data.position_y)
  return xDiff<5&&yDiff<5
  })
  if(index>-1){
  showToast(stringsTo("that_position_exists"))
  return
  }


  this.homePageLivePlayerComponent._onPressScreenShotAddCommonAngles()
  this.ptz_position={...data,name:this.state.frequentlyLocationName}


  }

  _getPtzAreaUpdate(data){
    if (!this.isFcous) {
        return;
      }
 if(this.currentPtz_position.position_x===data.position_x&&this.currentPtz_position.position_y===data.position_y){
  return
 }
 const index=  this.state.frequentlyLocationPhotoList.findIndex((item)=>{
  const pos=JSON.parse(item.pos)
  return pos[0]===data.position_x&&pos[1]===data.position_y
  })

 console.log("indexindexindexindex",data,this.state.frequentlyLocationPhotoList);
 this.currentPtz_position=data
  if(index>-1){
    this.ptz_position={...data,name:this.state.frequentlyLocationPhotoList[index].name}
    this.homePageLivePlayerComponent._onPressScreenShotAddCommonAngles(true)
  }
  }
  /*控制云台转动*/
  _doDirection(m_direction) {
    // 全景图暂时不需要
    // let type = (!this.state.isPanoramic && this.state.panoramicType == 0) || this.state.panoramicType == 1;
    // if (type) {
    //   showToast(stringsTo('Panoramic_loading'));
    // }

    ptz_pressed = true;



    this._checkDirection(m_direction);
    if(m_direction === 6){
    return
    }
    if (m_direction !== 5) {
      // 每100毫秒一次操作
      clearInterval(this.setPTZIntervalID);
      this.setPTZIntervalID = setInterval(() => {
        if (!this._checkDirection(m_direction)) {
          clearInterval(this.setPTZIntervalID);
        }
      }, 100);
    } else {
      ptz_pressed = false;
    }
  }

  /*判断当前是否可操作*/
  _checkDirection(m_direction) {
    console.log('_checkDirection m_direction=' + m_direction + ',ptz_pressed=' + ptz_pressed);
    // console.log("_checkDirection m_direction="+m_direction+",ptz_pressed="+ptz_pressed);
    if (ptz_pressed) {
      // this.sendDirectionCmd(m_direction);
      if (this.closeMotorPositon) {
        this.closeMotorPositon = false;
        // console.log('云台不发送指令');
      } else {
        // console.log('点击转动', new Date().getTime(), new Date().getTime() - (this.directionTime || 0))
        // this.directionTimer && clearTimeout(this.directionTimer);
        if (new Date().getTime() - (this.directionTime || 0) >= 180) {
          this.sendDirectionCmd(m_direction);
          this.directionTime = new Date().getTime();
        }
        // console.log('云台发送指令');
      }
    }

    return ptz_pressed;
  }

  /*发送控制云台的指令*/
  async sendDirectionCmd(m_direction) {
    this.homePageLivePlayerComponent?.setPositionShow(true);
    const messageJson = JSON.stringify({
      p2p_cmd: 1, //0x1:电机控制
      msg_id: this.msg_id,
      operation: m_direction, //电机转动方向 1:left 2:lright 3:up 4:down 5:reset
    });
    IMP2pClient?.sendP2pData('2', messageJson);
    this.msg_id += 1;
    // LetDevice.sendDeviceServerRequest('PTZActionControl', JSON.stringify(paramsJSON))
    //   .then(data => {
    //     console.log(' 控制云台 转动-------' + m_direction + data);
    //   })
    //   .catch(error => {
    //     console.log('sendDeviceServerRequest error ' + error);
    //   });
  }

  /*控制云台转动*/
  _doDirectionPanorama(h, v) {
    if (!this._checkDirectionPanorama(h, v)) {
      ptz_pressedPanoramic = true;
      this._checkDirectionPanorama(h, v);
    }
  }

  /*判断当前是否可操作*/
  _checkDirectionPanorama(h, v) {
    console.log('_checkDirection m_direction=' + h + ',ptz_pressed=' + ptz_pressed);

    if (ptz_pressedPanoramic) {
      this._setPanoramaRotateAngle(h, v);
    }

    return ptz_pressedPanoramic;
  }
  _setPanoramaRotateAngle(h, v) {
    let paramsJSON = {position: '[' + h + ',' + v + ']'};

    LetDevice.sendDeviceServerRequest('PanoramSlide', JSON.stringify(paramsJSON))
      .then(data => {
        console.log(' 控制云台 转动-------' + data);
      })
      .catch(error => {
        console.log('sendDeviceServerRequest error ' + error);
      });
  }

  // 获取转动状态
  // _getMotorPositonStatus() {
  //   LetDevice.getPropertyCloud('MotorPositon')
  //     .then(data => {
  //       //0休眠 1关闭
  //       console.log('MotorPositon--------' + data, typeof data);
  //       //056判断小于0
  //       if (LetDevice.model == 'a1Godgpvr3D') {
  //         let arrData = JSON.parse(data);
  //         if (arrData.length > 2) {
  //           let isMoveError = parseInt(arrData[0]);
  //           if (isMoveError < 0) {
  //             showToast(stringsTo('direction_end_009'));
  //           }
  //         }
  //       } else {
  //         if (data.substr(0, 3) == '-1,') {
  //           showToast(stringsTo('direction_end_009'));
  //         }
  //       }
  //     })
  //     .catch(error => {
  //       console.log(JSON.stringify(error));
  //     });
  // }

  /*监听直播流播放状态*/
  _onLivePlayerStatusChangeListener(status) {
    console.log('_onLivePlayerStatusChangeListener status=' + status);
    // 退出后台
    if (this.homePageLivePlayerComponent.leaveStatus) {
      return
    }
    if (status == HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PREPARED && LetDevice.isOnline) {
      if (this.state.isDataUsage && !this.handleOk) {
        this.homePageLivePlayerComponent.setLoadingView(false); //05-256 修复iOS不隐藏loading
        // this.homePageLivePlayerComponent.setPauseView(true);
        // this.setState({
        //   currentStatus: HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PAUSE,
        // });
        return
      }
      //判断是否离线可以避免直连模式下串流(手机连接设备A，进入B设备可以播放A的流）
      this.IMIVideoView?.start();
      this.homePageLivePlayerComponent.updateErrStatus();
    } else if (
      status == HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PAUSE ||
      status == HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.ERROR
    ) {
      this._dealWithErrorOrPause(true);
    } else if (status == HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PLAYING) {
      // this.setState({isOnline: true});
      this.homePageLivePlayerComponent.updateErrStatus();
    }
    if (status != HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PREPARED) {
      this.setState({currentStatus: status});
    }
  }

  _dealWithErrorOrPause(flag) {
    // this.IMIVideoView && this.state.currentStatus===HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PLAYING && this.IMIVideoView.stop();

    //直播流出错或暂停时，停止通话
    if (this.state.isCalling && this.IMIVideoView) {
      this.setState({isCalling: false, isCallingStatue: false});
      // 退出后台导致语音通话关闭声音若一开始是关闭的要保持关闭bug1324
      audioNeedRecovery && !this.isForegroundPage && this.homePageLivePlayerComponent.setCurrentMute(true);
      audioNeedRecovery && this.homePageLivePlayerComponent.setMute(true);
      this.IMIVideoView.stopSpeak();
    }
    console.log('我停止了');

    !flag && !isCheckingPermission && this.state.needForce && this.IMIVideoView && this.IMIVideoView.stop();
  }

  //通话时的波纹动画
  _renderCallWaveView() {
    if (this.state.fullScreen) {
      return null;
    }

    if (!this.state.isCalling) {
      return null;
    }

    return (
      <View
        style={{
          width: SCREEN_WIDTH,
          bottom: BottomLayoutContainerHeight, //计算出底部操作按钮容器的高度，确保水波纹动画在其之上即可 #6992
          position: 'absolute',
        }}>
        <WaveView waveHeight={36} waveWidth={SCREEN_WIDTH} />
      </View>
    );
  }
  //通话时的波纹动画
  _renderCallWaveNoPanoramicView() {
    if (this.state.fullScreen) {
      return null;
    }

    if (!this.state.isCalling) {
      return null;
    }

    return (
      <View
        style={{
          width: SCREEN_WIDTH,
          top: 90,
          // bottom: 83,
          position: 'absolute',
        }}>
        <WaveView waveHeight={36} waveWidth={SCREEN_WIDTH} />
      </View>
    );
  }
  //休眠提示
  _sleepView() {
    if (!this.state.isOnline) {
      return;
    }
    if (!this.state.isSleep) {
      return null;
    }
    if (!this.state.netConnected) {
      return null;
    }
    return (
      <View
        pointerEvents="box-none"
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: '#000',
        }}>
        <XText
          raw={true}
          style={{
            textAlign: 'center',
            color: colors.white,
            fontSize: 15,
          }}
          text={stringsTo('power_off')}
        />

        <RoundedButtonView
          buttonText={stringsTo('wake_up')}
          buttonStyle={{
            margin: 14,
            width: 110,
            height: 40,
          }}
          onPress={() => {
            if (this._isShareUser()) {
              return;
            }
            if (this.isSleepButtonDisable) {
              return;
            }
            this.isSleepButtonDisable = true;
            const paramJson = JSON.stringify({value: true});
            IMILog.logI('10001 -wake_up-setProperties', paramJson);
            LetDevice.setProperties(true, LetDevice.deviceID, '10001', paramJson)
              .then(() => {
                // this.setState({isSleep: false}, () => {
                  this.IMIVideoView && this.IMIVideoView.prepare();
                // });
              })
              .catch(() => {
                this.setState({isSleep: true});
                showToast(I18n.t('operationFailed'));
              })
              .finally(() => {
                this.isSleepButtonDisable = false;
              });
          }}
          accessibilityLabel={'home_page_video_dormancy'}
        />
      </View>
    );
  }

  //离线提示
  _deviceOffLineView() {
    if (this.state.isOnline) {
      return null;
    }
    if (deviceHotSpotUtil.isDirectConnectMode()) {
      return null;
    } //062的本地直连模式，不显示设备离线
    if (HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PLAYING == this.state.currentStatus) {
      return null;
    }
    return (
      <View
        pointerEvents="box-none"
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <XText
          raw={true}
          style={{
            textAlign: 'center',
            color: colors.white,
            fontSize: 15,
          }}
          text={stringsTo('device_offline')}
        />
      </View>
    );
  }

  //IPC062设备直连模式下的提示View(替代离线提示View)
  _DirectConnectHintView() {
    // let DeviceHotSpotUtils = DeviceHotSpotUtils.getInstance();
    if (!deviceHotSpotUtil.isShowDirectConnectHint()) {
      return null;
    }

    return (
      <View
        pointerEvents="box-none"
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <XText
          raw={true}
          style={{
            textAlign: 'center',
            color: colors.white,
            fontSize: 15,
            marginHorizontal: 55,
          }}
          text={stringsTo('hotspot_connect_hint')}
        />
        <RoundedButtonView
          buttonText={stringsTo('goto_wifi')}
          buttonStyle={{
            margin: 14,
            width: 110,
            height: 40,
            borderRadius: 7,
          }}
          onPress={() => {
            deviceHotSpotUtil.gotoWifiSettingPage();
          }}
        />
      </View>
    );
  }

  _isShareUser() {
    if (LetDevice.isShareUser) {
      showToast(stringsTo('shareUser_tip'));
      return true;
    }
    return false;
  }

  /*底部tab功能栏*/
  _renderBottomLayout() {
      if(!this.props.showBottomLayout || this.state.isFullScreen){
      return null
    }
    return   <View style={styles.bottomLayout}>
        <View
          style={{
            display: 'flex',
            width: '100%',
            height: (SCREEN_WIDTH * 76) / 360.0 + IphoneXSeries,
            maxHeight: 110 + IphoneXSeries,
            flexDirection: 'row',
            flexWrap: 'nowrap',
            alignItems: 'center',
            backgroundColor: '#FFFFFF',
          }}
        >
          {/*看家*/}
          <TouchableOpacity
            disabled={deviceHotSpotUtil.isDirectConnectMode()}
            style={styles.bottomLayoutItem}
            onPress={() => {
              // if (this._isShareUser()) return;
              if (!this._canStepInCall()) {
                return;
              }
              if (!this.state.netConnected) {
                showToast(stringsTo('network_not_connected'));
                return
              }
              if (this.areadyClick) {
                return
              }
              this.areadyClick = true
              // this.IMIVideoView && this.IMIVideoView.stop();
              this.props.navigation.push('AlarmListPage');
            }}
            accessibilityLabel={'home_page_housekeeping'}>
            <Image
              style={{
                width: 30,
                height: 30,
                opacity: deviceHotSpotUtil.isDirectConnectMode() ? 0.2 : 1,
              }}
              source={require('../../resources/images/icon_alarm.png')}
            />
            <Text
              style={{
                marginTop: 5,
                fontSize: 10,
                color: deviceHotSpotUtil.isDirectConnectMode() ? '#33333333' : '#333333',
              }}
              ellipsizeMode="tail"
              numberOfLines={2}>
              {stringsTo('bottom_house_keeping')}
            </Text>
          </TouchableOpacity>

          {/*回看*/}
          <TouchableOpacity
            style={styles.bottomLayoutItem}
            onPress={() => {
              if (!this._canStepInCall()) {
                return;
              }
              // if (!this._canStepIn()) {
              //   return;
              // }
              // if (this.state.isSleep) {
              //   showToast(stringsTo('power_off'));
              //   return;
              // }
              // if (!this.state.isOnline) {
              //   showToast(stringsTo('device_offline'));
              //   return;
              // }
              // if (!this.state.netConnected && !LetDevice.isLocalDevice) {
              //   //断网时点击回看，iOS和Android表现不一，因此直接拦截并提示
              //   showToast(stringsTo('network_not_connected'));
              //   return;
              // }
              if (!this.state.netConnected) {
                showToast(stringsTo('network_not_connected'));
                return
              }


              if (this.state.sdCardStatus == -1 || this.state.sdCardStatus == 0) {
                //进入插件状态获取失败 或 T卡状态可能获取不对，本身有T卡确显示无
                //暂时先进入这个页面以后记得改 TODO  PlayBackDualCamera
                if (this.areadyClick) {
                  return
                }
                this.areadyClick = true
                this.props.navigation.push('PlayBackPage', {
                  showPlayBackGridEvent: this.showPlayBackGridEvent === 1,
                  isOnLine: this.state.isOnline
                });
                // this.confirmSDCardState();
              } else if (this.state.sdCardStatus == 1) {
                if (this.areadyClick) {
                  return
                }
                this.areadyClick = true
                this.props.navigation.push(this.showPlayBackGridEvent === 2 ? 'PlayBackPageV2' : 'PlayBackPage', {
                  showPlayBackGridEvent: this.showPlayBackGridEvent === 1,
                  isOnLine: this.state.isOnline
                });
              } else {
                if (this.areadyClick) {
                  return
                }
                this.areadyClick = true
                this.props.navigation.push('SdCardNewPage');
              }
            }}
            accessibilityLabel={'home_page_play_back'}>
            <Image style={{width: 30, height: 30}} source={require('../../resources/images/icon_playback.png')} />
            <Text style={{marginTop: 5, fontSize: 10, color: '#333333'}} ellipsizeMode="tail" numberOfLines={2}>
              {stringsTo('playBackText')}
            </Text>
          </TouchableOpacity>

          {/*通话
          <TouchableOpacity
            style={[styles.bottomLayoutItem, {marginTop: 10}]}
            onPress={() => {
              if (this.state.isCallingStatue) {
                return;
              }
              if (!this.state.netConnected) {
                showToast(stringsTo('network_not_connected'));
                return
              }
              if (!this.state.isOnline) {
                showToast(stringsTo('device_offline'));
                return false;
              }
              if (this.state.isSleep) {
                showToast(stringsTo('power_off'));
                return false;
              }
              if (!this._canHang()) {
                return;
              }
              isCheckingPermission = true;
              this.homePageLivePlayerComponent?.updateCheckingPermission(true)
              IMIPermission.startCheckPermission(IMIPermission.PERMISSION_RECORD_AUDIO, status => {
                if (status === 0) {
                  this._doCall();
                  // ios权限进入后台再进入前台有延时
                  setTimeout(() => {
                    isCheckingPermission = false;
                    this.homePageLivePlayerComponent?.updateCheckingPermission(false)
                  }, 500)
                } else if (status === -1) {
                  isCheckingPermission = false;
                  this.homePageLivePlayerComponent?.updateCheckingPermission(false)
                  showToast(stringsTo('audio_permission_denied'));
                }
              });
            }}>
            <Image
              style={{width: 56, height: 56}}
              source={
                this.state.isCalling
                  ? this.state.isCallingStatue
                    ? require('../../resources/images/icon_hangup_big_p.png')
                    : require('../../resources/images/icon_hangup.png')
                  : this.state.isCallingStatue
                  ? require('../../resources/images/icon_call_p.png')
                  : require('../../resources/images/icon_call.png')
              }
              accessibilityLabel={this.state.isCalling ? 'home_page_phone_on' : 'home_page_phone_off'}
            />
          </TouchableOpacity>
          */}
          {/*云存*/}
          { !IMIPackage.closeCloudStorage &&
            <TouchableOpacity
              disabled={deviceHotSpotUtil.isDirectConnectMode()}
              style={styles.bottomLayoutItem}
              onPress={() => {
                if (this._isShareUser()) {
                  return;
                }
                if (!this._canStepInCall()) {
                  return;
                }

                if (!this.state.netConnected) {
                  showToast(stringsTo('network_not_connected'));
                  return
                }
                if (this.areadyClick) {
                  return
                }
                this.areadyClick = true
                if (this.state.vipState !== 1) {
                  this.checkVipState();
                } else {
                  this.goToCloud();
                }
              }}
              accessibilityLabel={'home_page_cloud_storage'}>
              <Image
                resizeMode="contain"
                style={{
                  width: 30,
                  height: 30,
                  opacity: deviceHotSpotUtil.isDirectConnectMode() ? 0.2 : 1,
                }}
                source={require('../../resources/images/icon_cloud.png')}
              />
              <Text
                style={{
                  marginTop: 5,
                  fontSize: 10,
                  color: deviceHotSpotUtil.isDirectConnectMode() ? '#33333333' : '#333333',
                }}
                ellipsizeMode="tail"
                numberOfLines={2}>
                {stringsTo('bottom_cloud_storage')}
              </Text>
            </TouchableOpacity>
          }

          {/*相册*/}
          {
            <TouchableOpacity
              style={styles.bottomLayoutItem}
              onPress={() => {
                if (!this._canStepInCall()) {
                  return;
                }
                if (this.areadyClick) {
                  return
                }
                this.areadyClick = true
                this.goToAlbum();
                // IMIGotoPage.startAlbumPage(LetDevice.deviceID);
              }}
              accessibilityLabel={'home_page_photo_album'}>
              <Image style={{width: 30, height: 30}} source={require('../../resources/images/icon_album_new.png')} />
              <Text style={{marginTop: 5, fontSize: 10, color: '#333333'}} ellipsizeMode="tail" numberOfLines={2}>
                {stringsTo('bottom_video_album')}
              </Text>
            </TouchableOpacity>
          }
        </View>
      </View>
   
  }
  /*底部tab功能栏*/
  _renderBottomNoCallLayout() {
    return (
      <View style={[styles.bottomLayout, {backgroundColor: 'white'}]}>
        {/*看家*/}
        <TouchableOpacity
          style={styles.bottomLayoutItemNoCall}
          onPress={() => {
            // if (this._isShareUser()) return;
            if (!this._canStepInCall()) {
              return;
            }
            this.props.navigation.push('AlarmListPage');
          }}
          accessibilityLabel={'home_page_housekeeping'}>
          <Image
            style={{
              width: 30,
              height: 30,
              opacity: this.state.isCallingStatue ? 0.2 : 1,
            }}
            source={require('../../resources/images/icon_alarm.png')}
          />
          <XText
            raw={true}
            style={{
              margin: 5,
              textAlign: 'center',
              fontSize: 10,
              color: this.state.isRecording ? '#C0C0C0C0' : '#333333',
              opacity: this.state.isCallingStatue ? 0.2 : 1,
            }} //设计稿改动
            ellipsizeMode="tail"
            numberOfLines={2}
            text={stringsTo('bottom_house_keeping')}
          />
        </TouchableOpacity>

        {/*回看*/}
        <TouchableOpacity
          style={styles.bottomLayoutItemNoCall}
          onPress={() => {
            if (this.state.isCallingStatue) {
              showToast(stringsTo('imi_speaking_block'));
              return;
            }
            if (!this._canStepInCall()) {
              return;
            }
            if (this.state.isSleep) {
              showToast(stringsTo('power_off'));
              return;
            }
            if (!this.state.isOnline) {
              showToast(stringsTo('device_offline'));
              return;
            }
            if (this.state.sdCardStatus == 0) {
              //T卡状态可能获取不对，本身有T卡确显示无
              // this.confirmSDCardState();
            } else if (this.state.sdCardStatus == 1) {
              this.props.navigation.push(this.showPlayBackGridEvent === 2 ? 'PlayBackPageV2' : 'PlayBackPage', {
                showPlayBackGridEvent: this.showPlayBackGridEvent === 1,
                isOnLine: this.state.isOnline
              });
            } else {
              //其他异常跳转到SD卡管理页面由用户自行操作
              this.props.navigation.push('SdCardNewPage');
            }
          }}
          accessibilityLabel={'home_page_play_back'}>
          <Image
            style={{
              width: 30,
              height: 30,
              opacity: this.state.isCallingStatue || this.state.isSleep ? 0.2 : 1,
            }}
            source={require('../../resources/images/icon_playback.png')}
          />
          <Text
            style={{
              margin: 5,
              fontSize: 10,
              color: '#333333',
              opacity: this.state.isCallingStatue || this.state.isSleep ? 0.2 : 1,
            }}
            ellipsizeMode="tail"
            numberOfLines={2}>
            {stringsTo('play_back_text')} {/*底部文字太长,文字换行*/}
          </Text>
        </TouchableOpacity>

        {/*云存*/}
        {
          !IMIPackage.closeCloudStorage && <TouchableOpacity
            style={styles.bottomLayoutItemNoCall}
            onPress={() => {
              if (this._isShareUser()) {
                return;
              }
              if (!this._canStepInCall()) {
                return;
              }
              if (this.state.vipState !== 1) {
                this.checkVipState();
              } else {
                this.goToCloud();
              }
            }}
            accessibilityLabel={'home_page_cloud_storage'}>
            <Image
              resizeMode="contain"
              style={{
                width: 30,
                height: 30,
                opacity: this.state.isCallingStatue ? 0.2 : 1,
              }}
              source={require('../../resources/images/icon_cloud.png')}
            />
            <Text
              style={{
                margin: 5,
                fontSize: 10,
                color: '#333333',
                opacity: this.state.isCallingStatue ? 0.2 : 1,
              }}
              ellipsizeMode="tail"
              numberOfLines={2}>
              {stringsTo('bottom_cloud_storage')}
            </Text>
          </TouchableOpacity>
        }

        {/*快捷*/}
        {
          <TouchableOpacity
            style={styles.bottomLayoutItemNoCall}
            onPress={() => {
              if (!this._canStepInCall()) {
                return;
              }
              this.goToAlbum();
              // IMIGotoPage.startAlbumPage(LetDevice.deviceID);
            }}
            accessibilityLabel={'home_page_photo_album'}>
            <Image
              style={{
                width: 30,
                height: 30,
                opacity: this.state.isCallingStatue ? 0.2 : 1,
              }}
              source={require('../../resources/images/icon_album_new.png')}
            />
            <Text
              style={{
                margin: 5,
                fontSize: 10,
                color: '#333333',
                opacity: this.state.isCallingStatue ? 0.2 : 1,
              }}
              ellipsizeMode="tail"
              numberOfLines={2}>
              {stringsTo('bottom_video_album')}
            </Text>
          </TouchableOpacity>
        }
      </View>
    );
  }
 /* 缩放ui,控制画面变动 */
//   _renderControllerZoomView() {
//     // 缩小
//     const reducePlayView=()=>{
// console.log("sjsjdjdsdjs");

//     }
//     // 放大
//     const enlargePlayView=()=>{
//       console.log("sjsjdjdsdjs");
//     }
//     // 复原
//     const resetPlayView=()=>{
//       console.log("sjsjdjdsdjs");
//     }

//     return <XView style={{display:'flex', flexDirection: 'row', justifyContent: 'center', alignItems: 'center',height:30}}>
//       <View style={{display:'flex', flexDirection: 'row',}}>
//        <TouchableHighlight style={{width:50}} onPress={reducePlayView}>
//         <Text>-</Text>
//       </TouchableHighlight>
//         <TouchableHighlight style={{width:50}} onPress={resetPlayView}>
//         <Text>复原</Text>
//       </TouchableHighlight>
//       <TouchableHighlight style={{width:50}}  onPress={enlargePlayView}>
//         <Text>+</Text>
//       </TouchableHighlight>
//      </View>
//     </XView>
//   }
  // 打电话
  onStartCall(){
    
              if (this.state.isCallingStatue) {
                return;
              }
              if (!this.state.netConnected) {
                showToast(stringsTo('network_not_connected'));
                return
              }
              if (!this.state.isOnline) {
                showToast(stringsTo('device_offline'));
                return false;
              }
              if (this.state.isSleep) {
                showToast(stringsTo('power_off'));
                return false;
              }
              if (!this._canHang()) {
                return;
              }
              isCheckingPermission = true;
              this.homePageLivePlayerComponent?.updateCheckingPermission(true)
              IMIPermission.startCheckPermission(IMIPermission.PERMISSION_RECORD_AUDIO, status => {
                if (status === 0) {
                  this._doCall();
                  // ios权限进入后台再进入前台有延时
                  setTimeout(() => {
                    isCheckingPermission = false;
                    this.homePageLivePlayerComponent?.updateCheckingPermission(false)
                  }, 500)
                } else if (status === -1) {
                  isCheckingPermission = false;
                  this.homePageLivePlayerComponent?.updateCheckingPermission(false)
                  showToast(stringsTo('audio_permission_denied'));
                }
              });
          
  }

  onAlarmPromptPress() {
    const isAlarmPromptOn = !this.state.isAlarmPromptOn
    const params = {msg_id: CAMERA_CONTROL.MANUAL_SOUND_AND_LIGHT_WARNING.PIID, value: isAlarmPromptOn};
    const paramJson = JSON.stringify(params);
    console.log('设置自动声光报警开关:', JSON.stringify(params));
    LetDevice.setProperties(true, LetDevice.deviceID, CAMERA_CONTROL.MANUAL_SOUND_AND_LIGHT_WARNING.PIID, paramJson).then(() => {
      console.log('set alarm prompt success')
      this.setState({ isAlarmPromptOn, whiteLightBtnEnable: !isAlarmPromptOn })
    })
    .catch(error => {
      this.setState({ whiteLightBtnEnable: true })
      console.log('set alarm prompt failed')
      console.log('失败----', error);
      showToast(stringsTo('operationFailed'));
    });
  }

  onWhiteLightPress() {
    const isWhiteLightOn = !this.state.isWhiteLightOn
    const params = {msg_id: WHITE_LIGHT.ON.PIID, value: isWhiteLightOn};
    const paramJson = JSON.stringify(params);
    console.log('设置白光补光灯开关:', JSON.stringify(params));
    LetDevice.setProperties(true, LetDevice.deviceID, WHITE_LIGHT.ON.PIID, paramJson).then(() => {
      console.log('set white light success')
      this.setState({ isWhiteLightOn, alarmPromptBtnEnable: !isWhiteLightOn })
    })
    .catch(error => {
      console.log('set white light failed')
      console.log('失败----', error);
      showToast(stringsTo('operationFailed'));
    });
  }

  // 失败回调
  onErrorChange() {
    console.log('onP2pSendState失败了');
    this.p2pStatusSuccess = false;
  }
  /**
   * 这个方法是在T卡状态为未插卡状态时
   * 再去获取一次T卡状态，只能减少T卡状态获取失败
   * 不能规避
   */
  confirmSDCardState() {
    console.log('---------内存卡状态');
    // showLoading(stringsTo('commLoadingText'), true, true);
    LetDevice.getSingleProperty('10013')
      .then(data => {
        // //取得的值是字符串'0'，'-1'，'5'...
        // console.log('sdStatus' + JSON.stringify(value));
        // showLoading(false);
        // //value = parseInt(value);

        // this.setState({sdCardStatus: value});
        // this.sdcardAction(value);
        console.log('==========内存卡信息', data)
        const value = data.value.value
        if (value === 8) {
          // showToast(stringsTo('sdcard_status7'), Toast.positions.BOTTOM);
          this.dealSDFormatAndShow().then(res => {
            if (!res) {
              this.setState({
                needFormat: true,
              })
            } else {
              this.setState({
                needFormatTip: true,
              })
            }
            this.setState({
              isFinishFormat: true,
            })
          })
        } else {
          this.setState({
            isFinishFormat: true,
          })
        }
      })
      .catch(error => {
        console.log('内存卡报错---------' + error);
        // showLoading(false);
        // this.sdcardAction(this.state.sdCardStatus);
      });
  }

  // 获取升级状态
  getFirmwareInfo() {
    const params = {
      Path: 'v1.0/imilab-01/ota/getUpgradeFirmware',
      ParamMap: {
        iotId: LetDevice.deviceID,
      },
      Method: 'GET',
    };
    LetIMIIotRequest.sendUserServerRequest(params, true)
      .then(item => {
        if (item.length > 0) {
          const firmwareInfo = customJsonParse(item[0])
          if (
            firmwareInfo?.status == 0 ||
            firmwareInfo?.status == 6 ||
            firmwareInfo?.status == 7 ||
            firmwareInfo?.status == 8
          ) {
            this.setState({
              hasUpgrade: true
            })
            this.dealUpgradeAndShow().then(res => {
              if (!res) {
                this.setState({
                  needUpgrade: true,
                  firmwareInfo: firmwareInfo
                })
              }
            })
          } else {
            this.setState({
              hasUpgrade: false
            })
          }
        } else {
          this.setState({
            hasUpgrade: false
          })
        }
      })
      .catch(e => {
        this.setState({
          hasUpgrade: false
        })
        console.log('========needUpgrade', JSON.stringify(e));
      })
  }

  dealSDFormatAndShow(){
    return new Promise((resolve, reject) => {
        IMIStorage.load({
            key: LetDevice.deviceID+'showSDFormatDialog',
            autoSync: true,
            syncInBackground: true,
        }).then(res => {
            //布尔类型
            console.log("dealSDAndShow",res.showSdDialog);
            resolve(res.showSdDialog);
        }).catch(_=> {
            resolve(false)
        });
    });
}

  dealUpgradeAndShow(){
    return new Promise((resolve, reject) => {
        IMIStorage.load({
            key: LetDevice.deviceID+'showOtaUpgradeDialog',
            autoSync: true,
            syncInBackground: true,
        }).then(res => {
            //布尔类型
            resolve(res.showOtaDialog);
        }).catch(_=> {
            resolve(false)
        });
    });

  }

  //点击sdcard后的动作
  sdcardAction(value) {
    if (value == 0) {
      //未插卡
      this.props.navigation.push('PlayBackNoSDCardSettingPage', {
        isVipValid: this.state.storageType !== 0,
      });
    } else if (value == 1) {
      if (!this._canStepInCall()) {
        return;
      }
      if (this.state.isSleep) {
        showToast(stringsTo('power_off'));
        return;
      }
      if (!this.state.isOnline) {
        showToast(stringsTo('device_offline'));
        return;
      }
      this.props.navigation.push(this.showPlayBackGridEvent === 2 ? 'PlayBackPageV2' : 'PlayBackPage', {
        showPlayBackGridEvent: this.showPlayBackGridEvent === 1,
      });
    } else {
      //其他异常跳转到SD卡管理页面由用户自行操作
      this.props.navigation.push('SdCardNewPage');
    }
  }

  //点击底部菜单中的云存按钮时，再查询一次会员状态
  checkVipState() {
    showLoading(stringsTo('commLoadingText'), true);
    imiAlarmEventCloudApi
      .getVipState(LetDevice.deviceID)
      .then(res => {
        let data = JSON.parse(res);
        let storageType = 0;
        if (data.status == 1) {
          storageType = data.status;
        }
        console.log(22323, data);

        showLoading(false);
        this.setState({vipState: data.status, storageType: storageType}, () => {
          this.goToCloud();
        });
      })
      .catch(error => {
        showLoading(false);
        console.log('_getVipState -2', JSON.stringify(error));
        this.setState({vipState: 0}, () => {
          this.goToCloud();
        });
      });
  }
  goToCloud() {
    console.log(
      tag,
      'goToCloud,jeff minApiLevel = ',
      IMIPackage.minApiLevel,
      ',storageType = ',
      this.state.storageType,
      'this.state.vipState',
      this.state.vipState,
    );
    //let item = [];
    // 第一版先加个免费领取，先不跳转

    // this.IMIVideoView && this.IMIVideoView.stop();
    if (this.state.vipState === 1) {
      this.props.navigation.push('CloudStoragePage');
    } else if (this.state.vipState !== 1) {
      //IMIGotoPage.starCloudBuyPage(LetDevice.deviceID);
      this._checkFreeStorage();
      // this.setState({showFreeCloud: true, giftDialog: true});
    } 
    else {
      // if (this.state.storageType === 1) {
      //   //连续云存
      //   //item.eventType = 0;
      //   this.props.navigation.push('CloudStoragePage');
      // } else if (this.state.storageType === 2) {
      //   //item.eventType = 1;
      //   this.props.navigation.push('CloudStoragePage');
      // } else {
      //   this.areadyClick = false
      //   this.setState({showFreeCloud: true, giftDialog: true});
      // }
      /* if (IMIPackage.minApiLevel >= 10005) {
        if (this.state.storageType === 1) {
          //连续云存
          item.eventType = 0;
          this.props.navigation.push('CloudStoragePage', {item: item});
        } else if (this.state.storageType === 2) {
          item.eventType = 1;
          this.props.navigation.push('CloudStoragePage', {item: item});
        } else {
          IMIGotoPage.starCloudBuyPage(LetDevice.deviceID);
        }
      } else {
        if (LetDevice.model == 'a1Godgpvr3D' || LetDevice.model == 'a1zcQKoHQ83' || LetDevice.model == 'a1QRbHvcYBd') {
          //区分056跳转到云存储
          if (IMIPackage.minApiLevel > 10002) {
            //支持云存储筛选事件最低版本为10003
            let eventStr = {
              eventList: [AlarmType.MOVE, AlarmType.PEOPLE, AlarmType.SOUND],
            };
            if (LetDevice.model == 'a1zcQKoHQ83') {
              eventStr = {
                eventList: [AlarmType.MOVE, AlarmType.PEOPLE, AlarmType.SOUND, AlarmType.CROSSING],
              };
            }
            if (LetDevice.model == 'a1QRbHvcYBd') {
              eventStr = {
                eventList: [AlarmType.MOVE, AlarmType.PEOPLE, AlarmType.SOUND, AlarmType.NOBODY],
              };
            }
            IMIGotoPage.startAliMoveDetectionPageConfig(null, LetDevice.deviceID, JSON.stringify(eventStr));
          } else {
            IMIGotoPage.startAliMoveDetectionPageV2(null, LetDevice.deviceID);
          }
        } else {
          if (IMIPackage.minApiLevel > 10001) {
            IMIGotoPage.startAliMoveDetectionPageV2(null, LetDevice.deviceID);
          } else {
            IMIGotoPage.startAliMoveDetectionPage();
          }
        }
      } */
    }
  }

  //进入相册
  goToAlbum() {
    this.checkIMIPermission();
    // let {showRNAlbum} = DeviceTemplatesUtils.getConfigProject('camera', 'a1FKrifIRwH');
    // console.log(tag, 'goToAlbum,jeff showRNAlbum = ', showRNAlbum, ',model = ', LetDevice.model);
    // if (showRNAlbum) {
    //   this.checkIMIPermission();
    // } else {
    //   IMIGotoPage.startAlbumPage(LetDevice.deviceID);
    // }
  }

  //  检查进入相册权限
  checkIMIPermission() {
    IMIPermission.startCheckPermission(IMIPermission.PERMISSION_READ_EXTERNAL_STORAGE, status => {
      if (status === 0) {
        IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, status2 => {
          if (status2 === 0) {
            // if (isAndroid() && IMIPackage.minApiLevel>=10006){
            //     this.props.navigation.push('CameraListPage');
            // }else if (isIos() &&IMIPackage.minApiLevel>=10005){
            //     this.props.navigation.push('CameraListPage');
            // }else {
            //     IMIGotoPage.startAlbumPage(LetDevice.deviceID);
            // }
            // if (IMIPackage.minApiLevel < 10007) {
            //   IMIGotoPage.startAlbumPage(LetDevice.deviceID);
            // } else {
            //   this.props.navigation.push('CameraListPage');
            // }
            // this.IMIVideoView && this.IMIVideoView.stop();
            this.props.navigation.push('CameraListPage');
          } else if (status2 === -1) {
            this.areadyClick = false;
            showToast(stringsTo('storage_permission_denied'));
          }
        });
      } else if (status === -1) {
        this.areadyClick = false;
        showToast(stringsTo('storage_permission_denied'));
      }
    });
  }

  setStopSleep(value){
    this.setState({
      stopSleep: value
    })
  }

  _getAlarmAbility(showSoundLightAlarm) {
    if (showSoundLightAlarm) {
      let alarmBtn = {
        isText: false,
        data: [require("../../resources/images/icon_warn_off.png"), require("../../resources/images/icon_warn_on.png")],
        onPress: () => {
          this.onAlarmPromptPress()
        },
        dataIndex: this.state.isAlarmPromptOn? 1: 0,
        disabled: !this.state.alarmPromptBtnEnable
      }
      let whiteLightBtn = {
        isText: false,
        data: [require("../../resources/images/icon_white_light_off.png"), require("../../resources/images/icon_white_light_on.png")],
        onPress: () => {
          this.onWhiteLightPress()
        },
        dataIndex: this.state.isWhiteLightOn? 1: 0,
        disabled: !this.state.whiteLightBtnEnable
      }
      return [
        {name: 'callBtn', insertIndex: 0},
        {name: 'voiceBtn', insertIndex: 1},
        {item: alarmBtn, insertIndex: 2},
        {item: whiteLightBtn, insertIndex: 3}
      ]
    } else {
      return [
        {name: 'callBtn', insertIndex: 0},
        {name: 'screenshotBtn', insertIndex: 1},
        {name: 'recordBtn', insertIndex: 2}
      ]
    }
  }

  render() {
    let {
      showCallAndAlarmView = false,
      showPanoramicView = false,
      showSynchronous056,
      showCustomQuality,
      audioParams,
      showAllPanoramara,
      showSDError,
      showLiveToolbarBottomTitle,
      lensCorrect,
      cameraNumber = '1',
      videoCodec = '0',
      showSoundLightAlarm = false
    } = DeviceTemplatesUtils.getConfigProject('camera', LetDevice.model); //是否显示圆盘
    // if (lensCorrect) {
    //   lensCorrect_x = lensCorrect[0];
    //   lensCorrect_y = lensCorrect[1];
    // }
    const disabledStatus = !this.state.isOnline || !this.state.netConnected || this.state.stopSleep

    //摄像机默认清晰度：流畅、标清、高清，如有自定义，请在camera/configProject中配置
    let qualityDataTitle = [
      {
        title: stringsTo('quality_auto'),
        index: 0,
        modeIndex: 0,
        accessibilityLabel: 'home_page_clarity_show_auto',
      },
      {
        title: stringsTo('quality_low'),
        index: 1,
        modeIndex: 2,
        accessibilityLabel: 'home_page_clarity_show_low',
      },
      {
        title: stringsTo('quality_common_3K'),
        index: 2,
        modeIndex: 7,
        accessibilityLabel: 'home_page_clarity_show_fhd',
      },
    ];
    // if (showCustomQuality) {
    //   qualityDataTitle = showCustomQuality;
    // }
    //固件不支持 自动 0 {title:'高清',index:2},
    let dataSource = {
      iotId: LetDevice.deviceID,
      playerClass: PlayerClass.LIVE,
      videoCodec: videoCodec, //0:h265, 1:h264
      cameraNumber: cameraNumber,
    };

    let sleepModeIndex = this.state.isSleep ? 1 : 0;
    let sleepBtn = {
      isText: false,
      data: [require('../../resources/images/icon_sleep.png'), require('../../resources/images/icon_sleep_on.png')],
      onPress: this._onClickSleepButton,
      disabled: disabledStatus ? true :  showPanoramicView ? this.state.isCallingStatue : false,
      dataIndex: sleepModeIndex,
      accessibilityLabel: ['home_page_dormancy_on', 'home_page_dormancy_off'],
      bottomTitle: showLiveToolbarBottomTitle ? stringsTo('toolbar_text_sleep') : null,
    };
    const toolBarBtns = []
    toolBarBtns.push({item: sleepBtn, insertIndex: 0})
    const isSingleCamera = cameraNumber === '1'
    if (isSingleCamera && showSoundLightAlarm) {
      toolBarBtns.push({name: 'screenshotBtn', insertIndex: 1})
      toolBarBtns.push({name: 'recordBtn', insertIndex: 2})
      toolBarBtns.push({name: 'quality', insertIndex: 3})
      toolBarBtns.push({name: 'fullScreenBtn', insertIndex: 4})
    } else if (isSingleCamera && !showSoundLightAlarm) {
      toolBarBtns.push({name: 'voiceBtn', insertIndex: 1})
      toolBarBtns.push({name: 'quality', insertIndex: 2})
      toolBarBtns.push({name: 'fullScreenBtn', insertIndex: 3})
    } else if (!isSingleCamera && showSoundLightAlarm) {
      toolBarBtns.push({name: 'screenshotBtn', insertIndex: 1})
      toolBarBtns.push({name: 'recordBtn', insertIndex: 2})
      toolBarBtns.push({name: 'quality', insertIndex: 3})
      toolBarBtns.push({name: 'fullScreenBtn', insertIndex: 4})
    } else {
      toolBarBtns.push({name: 'screenshotBtn', insertIndex: 1})
      toolBarBtns.push({name: 'recordBtn', insertIndex: 2})
      toolBarBtns.push({name: 'quality', insertIndex: 3})
      toolBarBtns.push({name: 'fullScreenBtn', insertIndex: 4})
    }
    /* if (audioParams) {
      dataSource.audioParams = audioParams;
    } */
    let LiveComponentHolder = isSingleCamera ? HomePageLivePlayerComponent: HomePageLiveDualPlayerComponent;
    return (
      <View style={{flex: 1}}>
        <LiveComponentHolder
          {...this.props}
          disabledStatus={disabledStatus}
          navigationBarHeight={this.navigationBarHeight}
          canPlay={this.state.canPlay}
          leaveMute={this.leaveMute}
          key={this.state.uniqueKey}
          initStatus={this.state.initStatus}
          ref={component => (this.homePageLivePlayerComponent = component)}
          videoRef={ref => {
            this.IMIVideoView = ref;
          }}
          firstIn={this.state.firstIn}
          qualityData={qualityDataTitle}
          showNavBar={this.props.showNavBar}
          navBar={(bps, p2pType, isFullScreen,cruiseIng) => this._renderNavigationBar(bps, p2pType, isFullScreen, this.state.hasUpgrade,cruiseIng)}
          showPlayItem={this.props.showPlayItem}
          // toolBarMoreItems={
          //   showCallAndAlarmView || deviceHotSpotUtil.isDirectConnectMode() ? [] : [{item: sleepBtn, insertIndex: 0}]
          // }
          toolBarMoreItems={
            toolBarBtns
          }
          bottomBarMoreItems={
            this._getAlarmAbility(showSoundLightAlarm)
          }
          videoSubView={(isFullScreen, showFullScreenTools) =>
            this.renderVideoSubView(isFullScreen, showFullScreenTools)
          }
          onLivePlayerStatusChange={status => this._onLivePlayerStatusChangeListener(status)}
          dataSource={dataSource}
          isSleepStatus={this.state.isSleep}
          isMove={this.state.isMoveStatus}
          albumName={LetDevice.deviceID}
          isOnLine={this.state.isOnline}
          isCalling={this.state.isCalling}
          isShowPanoramicView={showPanoramicView}
          netConnected={this.state.netConnected} //网络连接
          isCallingStatue={this.state.isCallingStatue}
          onCheckPermissionStatusChange={value => (isCheckingPermission = value)}
          showRTCView={this.state.showRTCView}
          setStopSleep={(value) => this.setStopSleep(value)}
          initRTCView={this.initRTCView}
          onErrorChange={this.onErrorChange}
          isDataUsage={this.state.isDataUsage}
          isWifi={this.state.isWifi}
          onStartCall={this.onStartCall.bind(this)}
        
          doOnline={(flag) => {
            this.setState({
              isOnline: flag
            })
            if (!flag && this.state.isCalling) {
              this._doCallClose()
            }
          }}
          doSleep={(flag) => {
            this.setState({
              isSleep: flag
            })
            if (flag && this.state.isCalling) {
              this._doCallClose()
            }
          }}
          isFcous={this.isFcous}
          getPtzAreaCallback={this._getPtzAreaCallback.bind(this)}
          getPtzAreaUpdate={this._getPtzAreaUpdate.bind(this)}
          // addFrequentlyLocation={this.state.isAddFrequentlyLocation||this.props.showPtzCenterControl} //是否是增加常看位置或者是添加常看位置的页面
          addFrequentlyLocationPhoto={this.addFrequentlyLocationPhoto}
          onVoiceCallErrorOccurred={(code, status) => {
            //两部手机通话互相占用
            // if (code==12||code==-88002){
            //
            //     this._doCallClose(false);
            // }
            if (this.state.isCalling) {
              this._doCallClose(false, status);
            }
          }}
          onOrientationChange={isFullScreen => {
            this.setState({isFullScreen: isFullScreen})
          }}
        >
          {showPanoramicView ? this._renderCallWaveNoPanoramicView() : this._renderCallWaveView()}
          {showCallAndAlarmView
            ? this._renderCallAndAlarmView()
            : showPanoramicView
            ? this._renderCallView()
            : this.renderPtzControlView()}
            {/* {this._renderControllerZoomView()} */}

          {!this.state.isFullScreen ? <RNLine style={{height: 1}} /> : null}

        </LiveComponentHolder>

        {/*直播页底部菜单按钮*/}
        {showPanoramicView ? this._renderBottomNoCallLayout() : this._renderBottomLayout()}


        {this.state.step ? (
          <XView
            raw={true}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
            }}
            onPress={() => {
              if (this.state.step == 1) {
                //不需要显示全景绘制
                if (showPanoramicView) {
                  this.setState({step: 0});
                  IMIStorage.save({
                    key: LetDevice.deviceID + 'cameraGuideForZoomed',
                    data: {
                      cameraGuideForZoomed: 0,
                    },
                    expires: null,
                  });
                } else {
                  if (showAllPanoramara) {
                    // 显示全景绘图
                    this.setState({step: 2});
                  } else {
                    // 不显示全景绘图
                    this.setState({step: 0});
                    IMIStorage.save({
                      key: LetDevice.deviceID + 'cameraGuideForZoomed',
                      data: {
                        cameraGuideForZoomed: 0,
                      },
                      expires: null,
                    });
                  }
                }
              }
              if (this.state.step == 2) {
                this.setState({step: 0});
                IMIStorage.save({
                  key: LetDevice.deviceID + 'cameraGuideForZoomed',
                  data: {
                    cameraGuideForZoomed: 0,
                  },
                  expires: null,
                });
                console.log('走这里了么');
              }
            }}>
            {this.state.step == 1 ? (
              <XView
                raw={true}
                style={{
                  height: '50%',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <XImage raw={true} iconSize={50} icon={require('../../resources/images/icon_zoom.png')} />
                <XText
                  raw={true}
                  style={{
                    marginTop: 35,
                    color: colors.white,
                    textAlign: 'center',
                    lineHeight: 18,
                  }}
                  text={stringsTo('camera_guide_for_zoomed_str')}
                />
              </XView>
            ) : null}
            {this.state.step == 2 ? (
              <XView
                raw={true}
                style={{
                  position: 'absolute',
                  right: 15,
                  bottom: BottomLayoutContainerHeight + 15,
                  left: 20,
                  height: 80,
                  alignItems: 'flex-end',
                }}>
                <XText
                  raw={true}
                  style={{color: colors.white, textAlign: 'center'}}
                  text={stringsTo('camera_guide_for_panoramic_str')}
                />
                <XImage
                  raw={true}
                  style={{marginTop: 14}}
                  iconSize={50}
                  icon={require('../../resources/images/icon_panoramic.png')}
                />
              </XView>
            ) : null}
          </XView>
        ) : null}
        {this.state.needFormat && <MessageDialog
          title={stringsTo('sdcard_format_title_tips')}
          message={stringsTo('sdcard_format_title_tips_content')}
          messageStyle={{marginBottom: 20, paddingLeft: 10, paddingRight: 10}}
          visible={this.state.needFormat}
          canDismiss={true}
          onDismiss={() => {
            IMIStorage.save({
              key: LetDevice.deviceID+'showSDFormatDialog',
              data: {
                  showSdDialog: true
              },
              expires: null,
            });
            this.setState({needFormat: false, needFormatTip: true});
          }}
          buttons={[
            {
              text: I18n.t('cancel'),
              accessibilityLabel: 'cancelIndexFormat',
              callback: _ => {
                IMIStorage.save({
                  key: LetDevice.deviceID+'showSDFormatDialog',
                  data: {
                      showSdDialog: true
                  },
                  expires: null,
                });
                this.setState({needFormat: false, needFormatTip: true});
              },
            },
            {
              text: I18n.t('ok_button'),
              accessibilityLabel: 'okIndexFormat',
              callback: _ => {
                this.setState({needFormat: false});
                IMIStorage.save({
                  key: LetDevice.deviceID+'showSDFormatDialog',
                  data: {
                      showSdDialog: true
                  },
                  expires: null,
                });
                this.props.navigation.push('SdCardNewPage', {
                  type: 2
                });
              },
            },
          ]}
        />}
        {!this.state.needFormat && this.state.isFinishFormat && this.state.needUpgrade && <MessageDialog
          title={null}
          showTitle={false}
          message={stringsTo('upgrade_state_content') + this.state.firmwareInfo.versionName + '，' + stringsTo('upgrade_state_content_end')}
          messageStyle={{marginTop: 30, paddingLeft: 10, paddingRight: 10}}
          visible={this.state.needUpgrade}
          canDismiss={true}
          onDismiss={() => {
            IMIStorage.save({
              key: LetDevice.deviceID+'showOtaUpgradeDialog',
              data: {
                  showOtaDialog: true
              },
              expires: 1000 * 3600 * 24,
            });
            this.setState({needUpgrade: false});
          }}
          buttons={[
            {
              text: I18n.t('cancel'),
              accessibilityLabel: 'cancelIndexUpgrade',
              callback: _ => {
                IMIStorage.save({
                  key: LetDevice.deviceID+'showOtaUpgradeDialog',
                  data: {
                      showOtaDialog: true
                  },
                  expires: 1000 * 3600 * 24,
                });
                this.setState({needUpgrade: false});
              },
            },
            {
              text: I18n.t('ok_button'),
              accessibilityLabel: 'okIndexUpgrade',
              callback: _ => {
                this.setState({needUpgrade: false});
                IMIStorage.save({
                  key: LetDevice.deviceID+'showOtaUpgradeDialog',
                  data: {
                      showOtaDialog: true
                  },
                  expires: 1000 * 3600 * 24,
                });
                this.props.navigation.push('UpgradePage', {
                  type: 1
                });
              },
            },
          ]}
        />}
        {this.state.sleepRecord && <MessageDialog
          title={stringsTo('sleepTipTitle')}
          message={stringsTo('sleepTipContent')}
          messageStyle={{marginBottom: 20, paddingLeft: 10, paddingRight: 10}}
          visible={this.state.sleepRecord}
          canDismiss={true}
          onDismiss={() => {
            IMIStorage.save({
              key: LetDevice.deviceID+'showSleepDialog',
              data: {
                showSleepDialog: true
              },
              expires: null,
            });
            this.setState({sleepRecord: false});
          }}
          buttons={[
            {
              text: I18n.t('know_button'),
              accessibilityLabel: 'okIndexSleepKnow',
              btnStyle:{
                backgroundColor:"#EEEEEE",
              },
              style: {
                color: imiThemeManager.theme.primaryColor
              },
              callback: _ => {
                IMIStorage.save({
                  key: LetDevice.deviceID+'showSleepDialog',
                  data: {
                    showSleepDialog: true
                  },
                  expires: null,
                });
                this.setState({sleepRecord: false});
              },
            },
          ]}
        />}
        {this._renderExpiredCountdownHintDialog()}
        {/*{this._renderGiftDialog()}*/}
        {/*领取免费云存提示对话框 --- 新版*/}
        {this._renderNewGiftDialog()}
        {showCallAndAlarmView ? this._renderOneKeySetDialog() : null}
        {showSynchronous056 || showSDError ? this._renderMicroSDCardAbnormalDialog() : null}
        {this._renderAddFrequentlyLocationDialog()}
        {this._renderAddFrequentlyLocationNameDialog()}

      </View>
  )}
  /*查询和计算云存会员还有几天到期*/
  _getCloudExpiredRemainDays() {
    if (LetDevice.isShareUser) {
      return;
    }
    if (LetDevice.isLocalDevice) {
      return;
    }
    let params = {
      Path: 'api/app/storage/expireTime',
      ParamMap: {
        model: LetDevice.model,
        deviceId: LetDevice.deviceID,
      },
    };
    LetIMIIotRequest.sendUserServerRequest(params)
      .then(data => {
        let cloudExpireDate = JSON.parse(data).expireTime;
        let remainDays = null;

        let currentStamp = moment().unix() * 1000;

        if (cloudExpireDate != 0) {
          //为0则是没开通云存获者是重绑机器了

          cloudExpireDate = moment(parseInt(cloudExpireDate));
          remainDays = cloudExpireDate.diff(moment(), 'day');
          if (cloudExpireDate <= currentStamp) {
            //过期了则剩余天数就是负数
            remainDays = -100;
          }
          this.setState(
            {
              cloudExpireRemainDays: remainDays,
              expiredDialog: remainDays != null && remainDays >= 0 && remainDays < 7,
            },
            () => {
              console.log('ggggggggg-----' + this.state.cloudExpireRemainDays);
            },
          );
        } else {
          //如果为0可能是0从未购买、1使用中、2已过期
          // this.getCloundStorageStatus()
        }
      })
      .catch(error => {
        console.log('查询云存过期时间-- error ', error);
      });
  }

  // 获取云存储相关信息
  //获取是否可以领取免费云存
  getFreeCloudStorageInfo() {
    if (LetDevice.isShareUser) {
      return;
    }

    let params = {
      Path: 'api/app/storage/free',
      ParamMap: {
        deviceId: LetDevice.deviceID,
        model: LetDevice.model,
      },
    };

    LetIMIIotRequest.sendUserServerRequest(params)
      .then(data => {
        // alert('免费云存数据--'+JSON.stringify(data));
        console.log(JSON.stringify(data));
        let result = JSON.parse(data);
        if (result.able) {
          this.setState({
            giftDialog: true,
            giftName: result.plan.planName,
            giftPrice: result.plan.marketPrice,
            giftId: result.plan.id,
            deviceName: result.deviceName,
            currency: result.currency, //区分人民币和美元
          });
        }
      })
      .catch(error => {
        console.log('推送数据获取错误--', error);
        console.log('sendUserServerRequest error ' + error);
      });
  }

  // 未领取云存 记录当前时间
  saveAlertDayTime() {
    let nowTime = Date.parse(new Date());

    // let endDate = new Date((new Date()).getFullYear(),(new Date()).getMonth(),(new Date()).getDate(), 23, 59, 59);
    // let endTime = parseInt(endDate.getTime());
    // console.log('今天的日期最后--',endDate,endTime);

    let newDate = new Date(new Date().getTime() + 1000 * 60 * 60 * 24);

    let tempNewDate = new Date(newDate.getFullYear(), newDate.getMonth(), newDate.getDate(), 0, 0, 0);
    let startTime = parseInt(tempNewDate.getTime());

    IMIStorage.save({
      key: LetDevice.deviceID + 'freeDay',
      data: {
        nextStartTime: startTime,
      },
      expires: null,
    });
  }
  // 云存储提示
  getCloudInfo() {
    if (LetDevice.isLocalDevice) {
      return;
    }
    this._getCloudExpiredRemainDays();
    this.getFreeDaySave();
    // 获取云存储相关信息
    this.getFreeCloudStorageInfo();
  }

  _getVipState() {
    if (LetDevice.isLocalDevice) {
      return;
    }
    imiAlarmEventCloudApi
      .getVipState(LetDevice.deviceID)
      .then(res => {
        console.log('_getVipState' + res);
        let data = JSON.parse(res);
        let storageType = 0;
        if (data.status == 1) {
          storageType = data.storageType;
        }
        console.log('=========', data.status)
        this.setState({vipState: data.status, storageType: storageType});
      })
      .catch(error => {
        console.log('_getVipState -1', error);
        this.setState({vipState: -1});
      });
  }

  getFreeDaySave() {
    IMIStorage.load({
      key: LetDevice.deviceID + 'freeDay',
      autoSync: true,
      syncInBackground: true,
    })
      .then(res => {
        let oldTime = res.nextStartTime;
        let newTime = Date.parse(new Date());
        // let tempTime = newTime - oldTime;
        // let oneDayTime = 24*60*60*1000

        // console.log('当前剩余时间',tempTime,oneDayTime);

        if (newTime >= oldTime) {
          this.setState(
            {
              showFreeCloud: true,
            },
            callback => {
              // console.log('显示',this.state.showFreeCloud);
            },
          );
        } else {
          this.setState(
            {
              showFreeCloud: false,
            },
            callback => {
              // console.log('不显示',this.state.showFreeCloud);
            },
          );
        }
      })
      .catch(_ => {
        this.setState(
          {
            showFreeCloud: true,
          },
          callback => {
            // console.log('报错这里zou',this.state.showFreeCloud);
          },
        );
      });
  }
  /*领取免费云存提示对话框*/
  _renderGiftDialog() {
    if (!this.state.showFreeCloud) {
      return null;
    }
    return (
      <GiftDialog
        visible={this.state.giftDialog}
        resource={require('../../resources/images/pic_gift_box.png')}
        giftName={this.state.giftName}
        giftPrice={this.state.giftPrice}
        currency={this.state.currency}
        canDismiss={false}
        onDismiss={() => {
          this.setState({giftDialog: false});
          this.saveAlertDayTime();
        }}
        onPress={() => {
          //快速点击，会出现多次领用云存的问题，
          //改为giftDialog状态成功后再去更新云端值,降低出错
          //this.gainFreeCloudStorage();
          this.setState({giftDialog: false}, () => {
            this.gainFreeCloudStorage();
          });
        }}
        onPressCloseDialog={() => {
          this.setState({giftDialog: false});
          this.saveAlertDayTime();
        }}
      />
    );
  }

  /*领取免费云存提示对话框 --- 新版*/
  _renderNewGiftDialog() {
    if (!this.state.showFreeCloud) {
      return null;
    }
    return (
      <NewGiftDialog
        visible={this.state.giftDialog}
        resource={require('../../resources/images/newFreeCloudBg.png')}
        giftName={this.state.giftName}
        giftPrice={this.state.giftPrice}
        currency={this.state.currency}
        canDismiss={false}
        onDismiss={() => {
          this.setState({giftDialog: false});
          this.saveAlertDayTime();
        }}
        onPress={() => {
          //快速点击，会出现多次领用云存的问题，
          //改为giftDialog状态成功后再去更新云端值,降低出错
          //this.gainFreeCloudStorage();
          this.setState({giftDialog: false}, () => {
            // this.gainFreeCloudStorage();
            IMIGotoPage.startNativeDevPageWithRouter(LetDevice.deviceID,"link://feedback/pages/toCloudBuyBrowserPage");
          });
        }}
        onPressCloseDialog={() => {
          this.setState({giftDialog: false});
          this.saveAlertDayTime();
        }}
      />
    );
  }

  //获取本地存储
  _getLocalStorage() {
      
    //畸变校正
    IMIStorage.load({
      key: LetDevice.deviceID + 'DistortionCorrection',
      autoSync: true,
      syncInBackground: true,
    })
      .then(res => {
        this.setState({lensCorrect: res.lensCorrect});
      })
      .catch(_ => {
        this.setState({lensCorrect: false});
      });
    //新手引导
    IMIStorage.load({
      key: LetDevice.deviceID + 'cameraGuideForZoomed',
      autoSync: true,
      syncInBackground: true,
    })
      .then(res => {
        this.setState({step: res.cameraGuideForZoomed});
      })
      .catch(_ => {
        this.setState({step: 1});
      });

    //获取本地存储信息--浏览节点提醒是否不再弹窗提醒
    IMIStorage.load({
      key: LetDevice.deviceID + 'cloudExpireRemainDaysLocal',
      autoSync: true,
      syncInBackground: true,
    })
      .then(res => {
        this.setState({
          cloudExpireRemainDaysLocal: res.cloudExpireRemainDaysLocal,
        });
      })
      .catch(_ => {});
    // 获取本地存储信息--清晰度设置
    IMIStorage.load({
      key: LetDevice.deviceID + 'qualityIndex',
      autoSync: true,
      syncInBackground: true,
    })
      .then(res => {
        
        this.homePageLivePlayerComponent.setQualityIndex(res.qualityIndex);
      })
      .catch(_ => {});


  }
  /*云存储即将过期的弹窗提醒*/
  _renderExpiredCountdownHintDialog() {
    //弹窗提示每天只提醒一次
    if (
      this.state.cloudExpireRemainDays == null ||
      (this.state.cloudExpireRemainDaysLocal != null &&
        this.state.cloudExpireRemainDaysLocal == this.state.cloudExpireRemainDays)
    ) {
      return null;
    }
    return (
      <AlertDialog
        title={stringsTo('cloud_will_time_out_str')}
        visible={this.state.expiredDialog}
        message={I18n.t('cloud_time_out_effect_str')}
        canDismiss={false}
        buttons={[
          {
            text: I18n.t('know_button'),
            callback: _ => {
              this.setState({expiredDialog: false});
              IMIStorage.save({
                key: LetDevice.deviceID + 'cloudExpireRemainDaysLocal',
                data: {
                  cloudExpireRemainDaysLocal: this.state.cloudExpireRemainDays,
                },
                expires: null,
              });
            },
          },
          {
            text: I18n.t('buy_again'),
            callback: _ => {
              this.setState({expiredDialog: false}, () => {
                IMIGotoPage.starCloudBuyPage(LetDevice.deviceID);
              });
              IMIStorage.save({
                key: LetDevice.deviceID + 'cloudExpireRemainDaysLocal',
                data: {
                  cloudExpireRemainDaysLocal: this.state.cloudExpireRemainDays,
                },
                expires: null,
              });
            },
          },
        ]}
      />
    );
  }
  /*SD卡异常提示*/
  _renderMicroSDCardAbnormalDialog() {
    //弹窗提示每次进入都提示只提醒一次
    if (!LetDevice.isOnline || LetDevice.isShareUser) {
      //设备离线时，和分享用户不弹窗
      return null;
    }
    return (
      <AlertDialog
        showTitle={false}
        visible={this.state.SDCardAbnormalDialog}
        message={this.state.needFormatMessage}
        messageStyle={{
          marginVertical: 10,
          marginHorizontal: 15,
          fontSize: 17,
          fontWeight: '500',
          color: '#333333',
        }}
        canDismiss={false}
        buttons={[
          {
            text: I18n.t('temporarily_not'),
            callback: _ => {
              this.setState({SDCardAbnormalDialog: false, showTopTips: true});
              IMIStorage.save({
                key: (LetDevice.deviceID + 'showSDAbnormalDialog').replace(/_/g, ''),
                data: {
                  showSdDialog: true,
                },
                expires: null,
              });
            },
          },
          {
            text: I18n.t('formatting_btn'),
            callback: _ => {
              this.setState({SDCardAbnormalDialog: false}, () => {
                showLoading(stringsTo('storageCardFormating'), true, true);
                LetDevice.sendDeviceServerRequest('FormatStorageMedium', JSON.stringify({}))
                  .then(data => {
                    console.log(' FormatStorageMedium  then-> ', JSON.stringify(data));
                    this.isFormating = true; //偶现点击格式化轮询不到3的情况,查到了1，loading就直接消失了，所以此处就置为true
                    this.intervalQuerySD = setInterval(
                      () => this.getSdStatusAndStorageInfo(this.state.loopQueryType),
                      3500,
                    );
                  })
                  .catch(error => {
                    console.log('FormatStorageMedium error ' + error);
                    showLoading(false);
                    console.log('发送格式化-----------失败');
                    showToast(I18n.t('waitFailedTip'));
                  });
                IMIStorage.save({
                  key: (LetDevice.deviceID + 'showSDAbnormalDialog').replace(/_/g, ''),
                  data: {
                    showSdDialog: true,
                  },
                  expires: null,
                });
              });
            },
          },
        ]}
      />
    );
  }

  /**
   * SD卡点击暂不后，实时流上提示用户
   */
  _renderTargetPushView(isFullScreen = false) {
    if (!this.state.needFormatTip || LetDevice.isShareUser) {
      return null;
    }
    let ViewStyle = {
      position: 'absolute',
      width: '100%',
      // top: this._getTitleBarPortraitHeight() + 13,
      top: !isFullScreen ? this.navigationBarHeight + 13 : 13,
      alignItems: 'center',
      justifyContent: 'center',
    };
    let containerStyle = {
      position: 'absolute',
      top: 0,
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: 10,
      borderWidth: 0.5,
      borderColor: 'rgba(255,255,255,0.1)',
      flexDirection: 'row',
      backgroundColor: '#ffffff',
      shadowRadius: 8,
      marginHorizontal: 20,
      shadowOpacity: 0.15,
      shadowOffset: {width: 0, height: 2},
      shadowColor: '#ffffff',
      padding: 5
    };
    let title = stringsTo('sdcard_format_title_tips');
    // let titleh5 = null;
    let subtitle = stringsTo('targetPushTitle_subtitle');
    // switch (this.targetpushItem) {
    //   case 'noIndex':
    //     title = stringsTo('targetPush_sdcard_format');
    //     titleh5 = 'Native_SDCardFormatSetting';
    //     subtitle = stringsTo('targetPushTitle_subtitle');
    //     break;
    //   case 'oldIndex':
    //     title = stringsTo('sdcard_format_title_tips');
    //     titleh5 = 'Native_SDCardFormatSetting';
    //     subtitle = stringsTo('targetPushTitle_subtitle');
    //     break;
    //   case 'error':
    //     //SD卡异常
    //     title = stringsTo('sdcard_status_error');
    //     titleh5 = 'Native_SDCardFormatSetting';
    //     subtitle = stringsTo('targetPushTitle_subtitle');
    //     break;
    //   default:
    //     console.log('TargetPushError');
    // }
    // title = `${title} `;
    return (
      <View style={ViewStyle}>
        <View style={containerStyle}>
          <Text
            style={{
              fontSize: locales[0]?.languageCode.indexOf('zh') !== -1 ? 12 : 10,
              color: '#4c4c4c',
              marginLeft: 5,
            }}>
            {`${title}！`}
            <Text
              style={{
                fontSize: locales[0]?.languageCode.indexOf('zh') !== -1 ? 12 : 10,
                color: imiThemeManager.theme.primaryColor,
                fontWeight: '500',
                marginLeft: 10
              }}
              onPress={() => {
                this.setState({needFormatTip: false});
                this.props.navigation.push('SdCardNewPage', {
                  type: 2
                });
              }}>
              {subtitle}
            </Text>
          </Text>

          <TouchableHighlight
            underlayColor="transparent"
            onPress={() => {
              this.setState({
                needFormatTip: false,
              });
            }}>
            <Image
              style={{width: 22, height: 22, marginLeft: 8, marginRight: 9}}
              source={require('../../resources/images/close.png')}
            />
          </TouchableHighlight>
        </View>
      </View>
    );
  }

  // 添加常看位置弹窗
  _renderAddFrequentlyLocationDialog(){
    let list =this.state.frequentlyLocationPhotoList
    if(list.length<6){
    list=this.state.frequentlyLocationPhotoList.sort((a,b)=>b.createTime-a.createTime).concat([{type:'add',key:'add'}])
     }


     const widthItem =Math.floor((width - 70) / 3) ;

    return <MessageDialog
          title={stringsTo('common_angles')}
          visible={this.state.addFrequentlyLocation}
          canDismiss={true}
          onDismiss={() => {
            this.setState({addFrequentlyLocation: false,});
          }}
            buttons={[
            {
              text: I18n.t('cancel'),
              accessibilityLabel: 'okIndexSleepKnow',
              btnStyle:{
                borderRadius:23,
                backgroundColor: 'rgba(0,0,0,0.04)',

              },
              style: {
                color: '#12AA9C'
              },
              callback: _ => {
                this.setState({addFrequentlyLocation: false,});
              },
            },
          ]}

    >
      <View style={{display:'flex',flexWrap:'wrap',flexDirection:'row',padding:20}}>
      {list.map((item,index) => {
        if (item.type === 'add') {

          return <View key={'add'}>
          <TouchableOpacity style={{backgroundColor:'#EEEEEE',borderRadius:10,width:widthItem,height:widthItem*0.57,display:'flex',
            alignItems:'center',justifyContent:'center'}}
            /* ios 不能同时显示两个modal */
              onPress={()=>this.setState({frequentlyLocationNameDialog:true,frequentlyLocationName: '',addFrequentlyLocation: false,})}>
            <Image style={{height:24,width:24}} source={require('../../resources/images/icon_add_fav_area.png')}/>

          </TouchableOpacity>
          <Text style={{fontSize:12,color:'#999999',textAlign:'center',marginTop:7}}>{stringsTo('add_to_favorites')}</Text>
          </View>
        }
        let tempSnapShotPath =item.path
          
        return <TouchableOpacity onPress={()=>this.goDesignatedLocation(item.idx*1)} key={item.path} style={{marginRight:10,position:'relative',marginBottom:10}} >
           <Image style={{borderRadius:10,width:widthItem,height:widthItem*0.57,}}   source={{uri: 'file://' + tempSnapShotPath}}/>
          <Text style={{fontSize:12,color:'#999999',textAlign:'center',marginTop:7}}>{item.name}</Text>
         {/* <TouchableOpacity style={{position:'absolute',top:-10,right:-10}}onPress={()=>this.deleteFrequentlyLocationPhoto(item.createTime)} >
          <Image style={{width:20,height:20}} source={require('../../resources/images/icon_delete1.png')}/>
     </TouchableOpacity> */}
        </TouchableOpacity>
      })}
     {this.state.frequentlyLocationPhotoList.length<1?null: <TouchableOpacity style={{position:'absolute',top:-40,right:10}} onPress={()=>{
          this.setState({addFrequentlyLocation: false});
           this.props.navigation.push('CommonAngles', {
                  idDelete: true
                });
      }}>
          <Image style={{width:20,height:20}} source={require('../../resources/images/icon_edit.png')}/>
     </TouchableOpacity>}
      </View>
  </MessageDialog>
  }
  // 添加常看位置名字弹窗
  _renderAddFrequentlyLocationNameDialog() {

    return <View>
    <MessageDialog
      title={stringsTo('enter_Name')}
      visible={this.state.frequentlyLocationNameDialog}
      canDismiss={true}
      bottomStyle={{bottom: isAndroid() ? 0 : this.state.keyBoardHeight}}
      onDismiss={() => {
        this.setState({ frequentlyLocationNameDialog: false ,frequentlyLocationNameError:'',});
      }}
      buttons={[
        {
          text: I18n.t('cancel'),
          accessibilityLabel: 'cancelIndexFormat',
          callback: _ => {

            this.setState({ frequentlyLocationNameDialog: false ,frequentlyLocationNameError:''});
          },
          btnStyle: {
             backgroundColor: 'rgba(0,0,0,0.04)',
    borderRadius: 23,
    paddingTop: 12,
    paddingBottom: 12,
          },
        },
        {
          text: I18n.t('imi_save'),
          accessibilityLabel: 'okIndexFormat',
          callback: _ => {
            if(!this.state.frequentlyLocationName){
           this.setState({frequentlyLocationNameError:stringsTo('empty_not_support')})
              return
            }
            const index=this.state.frequentlyLocationPhotoList?.findIndex((item)=>item.name==this.state.frequentlyLocationName)
            if(index>-1){
              this.setState({frequentlyLocationNameError:stringsTo('name_already_exists')})
              return
            }

            this._doDirection(6);
            this.setState({ frequentlyLocationNameDialog: false,  });
          },
          btnStyle:{
            backgroundColor:'#12AA9C',
            borderRadius: 23,
           paddingTop: 12,
           paddingBottom: 12,
           opacity:(this.state.frequentlyLocationNameError||!this.state.frequentlyLocationName)?0.3:1
          }
        },
      ]}
    >
    <View style={{ marginLeft:27,
        marginRight:27}}>
      <TextInput style={{
        height: 48,
        margin: 12,
        padding: 10,
        backgroundColor: '#F4F4F4',
        borderRadius:10,
        borderWidth:this.state.frequentlyLocationNameError?2:0,
        borderColor:'#F43F31'
      }} value={this.state.frequentlyLocationName} onChangeText={(e) => {

        this.setState({ frequentlyLocationName: e,frequentlyLocationNameError:'' })
      }} maxLength={8}
        returnKeyType="done"
              clearButtonMode="while-editing"
              enablesReturnKeyAutomatically={true}
              editable={true}
              autoFocus={true}
              keyboardType="default"
      />
      {this.state.frequentlyLocationNameError&&<Text style={{color:'red',
        marginLeft:28
      }}>
        {this.state.frequentlyLocationNameError}
        </Text>}
        </View>
    </MessageDialog>
    </View>
  }
  getIsAdd=()=>{

    if(!(this.state.frequentlyLocationName||this.props.anglesName)){
      return false
    }
    return true
  }
  // 添加常看位置路径，截图成功的回调
  addFrequentlyLocationPhoto = (photo) => {
    if (!this.isFcous) {
        return;
      }
    if(!this.getIsAdd()){
      return
    }
    showLoading(stringsTo('add_ing'),true)
  console.log("保存了",photo);
  return

   const list =[...this.state.frequentlyLocationPhotoList];
   const {position_x,position_y}=this.ptz_position
   const index=  this.state.frequentlyLocationPhotoList.findIndex((item)=>{
  const pos=JSON.parse(item.pos)
  return pos[0]===position_x&&pos[1]===position_y
  })
  const dateTime=new Date().getTime()
  if(index>-1){
    list[index].path=photo
      console.log("addFrequentlyLocationPhoto", list[index]);
  }else{


    list.push({idx:list.length,pos:JSON.stringify([position_x,position_y]),path:photo,name:this.state.frequentlyLocationName||this.props.anglesName,frequentlyLocationName: '',createTime:dateTime})

  }

    //每次保存之前都要先进行查询，防止数据过多
    try {
       LetDevice.getSingleProperty('10028').then((data) => {

      if (data.value?.code == 0) {
        let list1 = data.value.value? JSON.parse(data.value.value):[]
        const mapList = new Map();
        list1.forEach((item) => {
          mapList.set(item.createTime, item)
        })
        list.forEach((item) => {
          mapList.set(item.createTime, item)
        })
        const newList = Array.from(mapList.values())
        if (newList.length > 6) {
            this.ptz_position = {}
          this.setState({ frequentlyLocationPhotoList: list })
          showToast(stringsTo('limit_reached_v6'))
        } else {
          const equipmentList = newList.map((item,index) => {

            return {
              idx: index,
              name: item.name,
              pos: item.pos,
              createTime: item.createTime
            }
          })


          const paramJson = JSON.stringify({ msg_id: '10028', value: JSON.stringify(equipmentList) });


          LetDevice.setProperties(true, LetDevice.deviceID, '10028', paramJson, true).then(() => {
            this.ptz_position = {}
            this.initFrequentlyLocationPhotoList=equipmentList
            this.setState({ frequentlyLocationPhotoList: newList })
            IMIStorage.save({
              key: AddFrequentlyLocationPhotoKey,
              data: JSON.stringify(newList),
              expires: null,
            });
          })
        }
      }
    })
    } catch (error) {
        console.log("保存失败", error);
        showToast(stringsTo('add_error'))
            this.ptz_position = {}
    } finally{
      showLoading("",false)
    }
   



  }
  // 删除常看位置
  deleteFrequentlyLocationPhoto = (createTime) => {
     if (!this.isFcous) {
        return;
      }
      showLoading(stringsTo('del_ing'),true)
   const list =[...this.state.frequentlyLocationPhotoList];
  //   list.splice(index,1)
  //   const creteTimeIndex=this.state.frequentlyLocationPhotoList[index]?.createTime

      try {
       LetDevice.getSingleProperty('10028').then((data) => {

      if (data.value?.code == 0) {
        let list1 = data.value.value? JSON.parse(data.value.value):[]
        const mapList = new Map();
        list1.forEach((item) => {


          if(`${createTime}`===`${item.createTime}`){
             return
          }
           if(!item.createTime){
             return
          }

          mapList.set(item.createTime, item)
        })

        list.forEach((item) => {
            if(mapList.has(item.createTime)){
                   mapList.delete(item.createTime)
               mapList.set(item.createTime, item)
            }

        })


        const newList = Array.from(mapList.values())

        if (newList.length > 6) {
          showToast(stringsTo('limit_reached_v6'))
          this.setState({ frequentlyLocationPhotoList: list })
        } else {
          const equipmentList = newList.map((item,index) => {
            return {
              idx: index,
              name: item.name,
              pos: item.pos,
              createTime: item.createTime
            }
          })
          const paramJson = JSON.stringify({ msg_id: '10028', value: JSON.stringify(equipmentList) });
          console.log("保存的数据paramJson", paramJson);

          LetDevice.setProperties(true, LetDevice.deviceID, '10028', paramJson, true).then(() => {
            this.ptz_position = {}
            this.initFrequentlyLocationPhotoList=equipmentList
            this.setState({ frequentlyLocationPhotoList: newList })
             showToast(stringsTo('delete_success'))
            IMIStorage.save({
              key: AddFrequentlyLocationPhotoKey,
              data: JSON.stringify(list),
              expires: null,
            });
          })
        }
      }
    })
    } catch (error) {
        console.log("保存失败", error);
            this.ptz_position = {}
              showToast(stringsTo('delete_failed'))
    } finally{
         showLoading("",false)
    }
  }
  //去常看位置
  goDesignatedLocation=(indexId)=>{

   if (!this._canStepIn()) {
      return;
    }
    const index= this.initFrequentlyLocationPhotoList.findIndex((item)=>item.idx==indexId)
    console.log("去常看位置",indexId,this.initFrequentlyLocationPhotoList,index);

 const paramJson=JSON.stringify({msg_id: '10029',value:index})
 LetDevice.setProperties(true, LetDevice.deviceID, '10029', paramJson,true).catch((error)=>{
 console.log("保存失败",error);
  })
  }
  /*查询SD卡状态*/
  getSdStatusAndStorageInfo() {
    LetDevice.getPropertyCloud('StorageStatus')
      .then(value => {
        console.log('getPropertyCloud---------' + value + typeof value);
        let stateProps = {};
        value = parseInt(value);
        if (value == 0) {
          //未插卡
          this.intervalQuerySD && clearInterval(this.intervalQuerySD);
          showLoading(false);
          showToast(I18n.t('injectSuccess'));
          this.setState({sdCardStatus: value});
        } else if (value == 1 || value == 2) {
          //正常使用中 和 未格式化(格式化失败为此状态)
          showLoading(false);

          if (value == 1 && this.isFormating) {
            //发送格式化指令后，轮询出卡状态正常
            showToast(I18n.t('sdcard_format_success'));
          }

          if (value == 2 && this.isFormating) {
            //发送格式化指令后，轮询出状态为未格式化，则格式化失败
            showToast(I18n.t('sdcard_format_fail'));
          }

          this.intervalQuerySD && clearInterval(this.intervalQuerySD);
          this.isFormating = false;
        } else if (value == 3) {
          //正在格式化
          this.isFormating = true;
          //showLoading(stringsTo('storageCardFormating'), true, true);
        } else if (value == 4) {
          //SD卡弹出. 格式化时会unmount,所以会有4
          /*showLoading(false);
                showToast(I18n.t("sdcard_format_fail"));*/
        } else if (value == 5) {
        }
        stateProps.sdCardStatus = value;
        this.setState(stateProps);
      })
      .catch(error => {
        console.log('error:', error);
        // showLoading(stringsTo('storageCardFormating'), false);
        console.log(JSON.stringify(error));
      });
  }

  async gainFreeCloudStorage() {
    /*   this.paramMap = {
      deviceId: LetDevice.deviceID,
      model: LetDevice.model,
      planId: this.state.giftId + '',
      deviceName: this.state.deviceName,
    };
    let params = {
      Path: 'api/app/storage/gain',
      ParamMap: this.paramMap,
    };

    LetIMIIotRequest.sendUserServerRequest(params)
      .then(data => {
        showToast(stringsTo('get_success'));
      })
      .catch(error => {
        console.log('领取免费云存数据-- error ', error);
      }); */
    showLoading(stringsTo('commLoadingText'), true);
    const data2 = await cloudDeviceService.getSpuList(LetDevice.deviceID).catch(e => {});
        let planId = ''
        
        try {
          const data1 = JSON.parse(data2)
          Array.isArray(data1) && data1.map(res0 => {
            Array.isArray(res0?.cloudstorageSkuList) && res0?.cloudstorageSkuList.map(res1 => {
              Array.isArray(res1?.activityInfoList) && res1?.activityInfoList.map(res2 => {
                if (res2.activityType === 1) {
                  planId = res1?.skuId
                }
              })
            })
          })
        } catch (error) {
          console.log(error)
          planId = ''
        }
    await cloudDeviceService
      .gainFreeStorage(LetDevice.deviceID, planId)
      .catch(e => {
        console.log('gainFreeStorage---', e, LetDevice.deviceID, planId);
        showLoading(false);
      })
      .then(result => {
        // console.log('领取成功 gainFreeStorage---', result);
          showLoading(false);
          showToast('领取成功')
      });
  }
  // 判断是否弹出领取云存储
  async _checkFreeStorage() {
    showLoading(stringsTo('commLoadingText'), true);
    //判断是否弹出领取免费云存
    const data = await cloudDeviceService.getStorageFree(LetDevice.deviceID).catch(e => {
      console.log(e);
      showLoading(false);
    });
    this.areadyClick = false
    if (data) {
      const res = customJsonParse(data);
      console.log('data---------', data);
      showLoading(false);
      if (res?.isAllowReceiveFreePackage) {
        this.setState({showFreeCloud: true, giftDialog: true});
      } else {
        // showToast('没有次数');
        IMIGotoPage.startNativeDevPageWithRouter(LetDevice.deviceID,"link://feedback/pages/toCloudBuyBrowserPage");
      }
    } else {
      showLoading(false);
      console.log('data---------无数据');
    }
  }

  //批量关闭push事件开关
  batchCloseEventNotice() {
    //不同的项目可能有不需要的推送通知
    let {
      showMoveSwitch,
      showPeopleSwitch,
      showLoudSwitch,
      showFencesSwitch = false,
      showArea,
      showNoHuman = false,
    } =DeviceTemplatesUtils.getConfigProject('camera', LetDevice.model);
    let params = {};

    //关闭看家
    if (showMoveSwitch) {
      params.MOVE = false;
    }
    if (showPeopleSwitch) {
      params.PEOPLE = false;
    }
    if (showLoudSwitch) {
      params.SOUND = false;
    }
    if (showFencesSwitch) {
      params.OVER_AREA = false;
    }
    if (showArea) {
      params.KEY_AREA = false;
    }
    if (showNoHuman) {
      params.NOBODY = false;
    }
    //批量设置本项目事件推送
    imiAlarmEventCloudApi
      .setEventNoticePushByList(LetDevice.deviceID, LetDevice.model, params)
      .then(res => {
        this.needSyncEventPushValue = false; //仅在进入插件时同步一次，之后离开、回到本页面没必要同步
      })
      .catch(error => {
        IMILogUtil.uploadClickEventForCount('OpenCloseHouseError');
      });
  }
}

const styles = StyleSheet.create({
  bottomLayout: {
    display: 'flex',
    width: '100%',
    height: (SCREEN_WIDTH * 76) / 360.0,
    maxHeight: 110 + IphoneXSeries,
    paddingBottom: IphoneXSeries, //IphoneX底部遮挡上移
    flexDirection: 'row',
    flexWrap: 'nowrap',
    alignItems: 'center',
    justifyContent: 'center',
  /*  position:"absolute",
    bottom:0*/
  },
  bottomLayoutItem: {
    flex: 1,
    height: '100%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  /*  marginTop: 20,*/
  },
  bottomLayoutItemNoCall: {
    flex: 1,
    height: '100%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
