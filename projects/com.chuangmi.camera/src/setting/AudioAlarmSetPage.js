import React from 'react';

import {BackHandler, Image, StyleSheet, Text, TouchableWithoutFeedback, View} from 'react-native';

import {imiThemeManager, showLoading} from '../../../../imilab-design-ui';

import ListItem from '../../../../imilab-design-ui/src/widgets/settingUI/ListItem';
import ListItmeWithSwitch from '../../../../imilab-design-ui/src/widgets/settingUI/ListItmeWithSwitch';
import SlideGear from '../../../../imilab-design-ui/src/widgets/settingUI/SlideGear';
import MessageDialog from '../../../../imilab-design-ui/src/widgets/settingUI/MessageDialog';
import I18n, {stringsTo} from '../../../../globalization/Localize';
import BaseDeviceComponent from '../../../../imilab-rn-sdk/components/BaseDeviceComponent';
import {LetDevice} from '../../../../imilab-rn-sdk/native/iot-kit/IMIDevice';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import {isAndroid} from '../../../../imilab-rn-sdk/utils/Utils';
import IMIHost from '../../../../imilab-rn-sdk/native/local-kit/IMIHost';
import {CAMERA_CONTROL, SOUND_AND_LIGHT_WARNING} from '../../constants/Spec';

const voiceArr = [
  {
    //"无",
    replyName: stringsTo('voice_for_wu'),
    replyCNUrl: 'null', //大陆
    replyUSUrl: 'null', //美国
    replySGUrl: 'null', //新加坡
    replyDEUrl: 'null', //欧洲
    replRUUrl: 'null', //俄罗斯
    replIDUrl: 'null', //印度
    index: 1,
    time: '5',
  },
  {
    //"警报",
    replyName: stringsTo('voice_for_warning'),
    replyCNUrl: '1-alarm', //大陆
    // replyCNUrl:'https://cdn.cnbj2.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/1-alarm.opus',//大陆
    replyUSUrl: 'https://cdn.awsusor0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/1-alarm.opus', //美国
    replySGUrl: 'https://cdn.awssgp0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/1-alarm.opus', //新加坡
    replyDEUrl: 'https://cdn.awsde0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/1-alarm.opus', //欧洲 德国
    replRUUrl: 'https://cdn.ksyru0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/1-alarm.opus', //俄罗斯
    replIDUrl: 'https://cdn.awsind0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/1-alarm.opus', //印度
    index: 2,
    time: '5',
  },
  {
    //"叮咚声",
    replyName: stringsTo('voice_for_dingdong'),
    replyCNUrl: '2-ding_dong', //大陆
    // replyCNUrl:'https://cdn.cnbj2.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/2-ding_dong.opus',//大陆
    replyUSUrl: 'https://cdn.awsusor0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/2-ding_dong.opus', //美国
    replySGUrl: 'https://cdn.awssgp0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/2-ding_dong.opus', //新加坡
    replyDEUrl: 'https://cdn.awsde0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/2-ding_dong.opus', //欧洲
    replRUUrl: 'https://cdn.ksyru0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/2-ding_dong.opus', //俄罗斯
    replIDUrl: 'https://cdn.awsind0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/2-ding_dong.opus', //印度
    index: 3,
    time: '5',
  },
  {
    //"您好，欢迎光临"
    replyName: stringsTo('voice_for_welcome'),
    replyCNUrl: '3-welcom_female', //大陆
    // replyCNUrl:'https://cdn.cnbj2.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/3-welcom_female.opus',//大陆
    replyUSUrl: 'https://cdn.awsusor0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/3-welcom_female.opus', //美国
    replySGUrl: 'https://cdn.awssgp0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/3-welcom_female.opus', //新加坡
    replyDEUrl: 'https://cdn.awsde0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/3-welcom_female.opus', //欧洲
    replRUUrl: 'https://cdn.ksyru0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/3-welcom_female.opus', //俄罗斯
    replIDUrl: 'https://cdn.awsind0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/3-welcom_female.opus', //印度
    index: 4,
    time: '5',
  },
  {
    //"您已进入监控区域"
    replyName: stringsTo('voice_for_area'),
    replyCNUrl: '4-entered_monitoring_male', //大陆
    // replyCNUrl:'https://cdn.cnbj2.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/4-entered_monitoring_male.opus',//大陆
    replyUSUrl:
      'https://cdn.awsusor0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/4-entered_monitoring_male.opus', //美国
    replySGUrl:
      'https://cdn.awssgp0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/4-entered_monitoring_male.opus', //新加坡
    replyDEUrl:
      'https://cdn.awsde0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/4-entered_monitoring_male.opus', //欧洲
    replRUUrl:
      'https://cdn.ksyru0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/4-entered_monitoring_male.opus', //俄罗斯
    replIDUrl:
      'https://cdn.awsind0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/4-entered_monitoring_male.opus', //印度
    time: '5',
  },
  {
    //"请随手关门，谢谢"
    replyName: stringsTo('voice_for_closedoor'),
    // replyCNUrl:'https://cdn.cnbj2.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/5-close_door_female.opus',//大陆
    replyCNUrl: '5-close_door_female', //大陆
    replyUSUrl: 'https://cdn.awsusor0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/5-close_door_female.opus', //美国
    replySGUrl: 'https://cdn.awssgp0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/5-close_door_female.opus', //新加坡
    replyDEUrl: 'https://cdn.awsde0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/5-close_door_female.opus', //欧洲
    replRUUrl: 'https://cdn.ksyru0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/5-close_door_female.opus', //俄罗斯
    replIDUrl: 'https://cdn.awsind0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/5-close_door_female.opus', //印度
    index: 6,
    time: '5',
  },
  {
    //"请注意安全"
    replyName: stringsTo('voice_for_safe'),
    // replyCNUrl:'https://cdn.cnbj2.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/6-safety_female.opus',//大陆
    replyCNUrl: '6-safety_female', //大陆
    replyUSUrl: 'https://cdn.awsusor0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/6-safety_female.opus', //美国
    replySGUrl: 'https://cdn.awssgp0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/6-safety_female.opus', //新加坡
    replyDEUrl: 'https://cdn.awsde0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/6-safety_female.opus', //欧洲
    replRUUrl: 'https://cdn.ksyru0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/6-safety_female.opus', //俄罗斯
    replIDUrl: 'https://cdn.awsind0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/6-safety_female.opus', //印度
    index: 7,
    time: '5',
  },
  {
    //"上下楼梯,请注意安全"
    replyName: stringsTo('voice_for_stairs'),
    // replyCNUrl:'https://cdn.cnbj2.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/9-stairs_safe_female.opus',//大陆
    replyCNUrl: '9-stairs_safe_female', //大陆
    replyUSUrl: 'https://cdn.awsusor0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/9-stairs_safe_female.opus', //美国
    replySGUrl: 'https://cdn.awssgp0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/9-stairs_safe_female.opus', //新加坡
    replyDEUrl: 'https://cdn.awsde0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/9-stairs_safe_female.opus', //欧洲
    replRUUrl: 'https://cdn.ksyru0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/9-stairs_safe_female.opus', //俄罗斯
    replIDUrl: 'https://cdn.awsind0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/9-stairs_safe_female.opus', //印度
    index: 8,
    time: '5',
  },
  {
    //"危险区域，请离开"
    replyName: stringsTo('voice_for_dangerArea'),
    // replyCNUrl:'https://cdn.cnbj2.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/10-danger_area_male.opus',//大陆
    replyCNUrl: '10-danger_area_male', //大陆
    replyUSUrl: 'https://cdn.awsusor0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/10-danger_area_male.opus', //美国
    replySGUrl: 'https://cdn.awssgp0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/10-danger_area_male.opus', //新加坡
    replyDEUrl: 'https://cdn.awsde0.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/10-danger_area_male.opus', //欧洲
    replRUUrl: 'https://cdn.ksyru0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/10-danger_area_male.opus', //俄罗斯
    replIDUrl: 'https://cdn.awsind0-eco.fds.api.mi-img.com/chuangmi-cdn/product/common/audio/10-danger_area_male.opus', //印度
    index: 9,
    time: '5',
  },
];

const TAG = 'AudioAlarmSetPage';

/**
 * 声光报警设置页面
 */
export default class AudioAlarmSetPage extends BaseDeviceComponent {
  static propTypes = {};

  constructor(props) {
    super(props);
    this.state = {
      lightSwitch: false, //声光报警
      onlyPeopleSwitch: true, //只侦测人形
      repeatValue: [1, 1, 1, 1, 1, 1, 1],
      startTime: '00:00:00', //开始时间
      endTime: '23:59:59', //结束时间
      triggerTypes: [],
      spotlightMode: SOUND_AND_LIGHT_WARNING.LIGHT_WARNING_MODE.ENUM.FAST_FLICKER,
      tempSpotlightMode: SOUND_AND_LIGHT_WARNING.LIGHT_WARNING_MODE.ENUM.FAST_FLICKER,
      audioPath: '1-alarm', //提示音路径
      containTime: 10, //持续时间
      containTimeTemp: 10, //持续时间
      showSpotlightModal: false, //聚光灯报警弹窗
      isLightOn: false,
      isLightOnTemp: false,
      isAudioOn: false,
      data: [],
      allSpotlightData: '', //声光报警数据
      allVoiceArr: [], //所有音频数据 包括标准+自定义音频
    };
  }

  UNSAFE_componentWillMount() {
    if (isAndroid()) {
      BackHandler.addEventListener('hardwareBackPress', this.onBackHandler);
    }
  }

  componentWillUnmount() {
    this.timer && clearTimeout(this.timer);
    if (isAndroid()) {
      BackHandler.removeEventListener('hardwareBackPress', this.onBackHandler);
    }
  }

  onBackHandler = () => {
    console.log('onBackAndroid  navigatiosn.isFocused() ' + navigation.isFocused());
    if (navigation.isFocused()) {
      this._onPressBack();
      return true;
    } else {
    }
    return false;
  };

  // 返回上一页
  _onPressBack = () => {
    if (this.props.route.params.callback) {
      console.log('返回传值', this.state.lightSwitch);
      let tempStr;
      if (this.state.lightSwitch) {
        tempStr = '1'; // 已开启
      } else {
        tempStr = '0'; // 未开启
      }
      console.log('返回值---', tempStr);
      this.props.route.params.callback(tempStr);
    }
    this.props.navigation.pop();
  };

  componentDidMount() {
    console.log('当前服务器', IMIHost.serverCode);
    this._getAllValue();
  }

  _getAudioStr(audioPath) {
    let str = ''
    if (IMIHost.serverCode === IMIHost.SERVER_CODE.SG) {
      str = voiceArr.find(item => item.replySGUrl === audioPath).replyName
    } else if (IMIHost.serverCode === IMIHost.SERVER_CODE.US) {
      str = voiceArr.find(item => item.replyUSUrl === audioPath).replyName
    } else if (IMIHost.serverCode === IMIHost.SERVER_CODE.EU) {
      str = voiceArr.find(item => item.replyDEUrl === audioPath).replyName
    } else {
      str = voiceArr.find(item => item.replyCNUrl === audioPath).replyName
    }
    return str;
  }

  _getSpotlightModeContent() {
    let content = stringsTo('potlight_flash');
    if (!this.state.isLightOn) {
      content = stringsTo('potlight_not_bright');
    } else if (SOUND_AND_LIGHT_WARNING.LIGHT_WARNING_MODE.ENUM.ALWAYS_LIGHT === this.state.spotlightMode) {
      content = stringsTo('potlight_long_bright');
    }
    return content;
  }

  async _getAllValue() {
    showLoading(stringsTo('commWaitText'), true);
    const promises = [
      LetDevice.getSingleProperty(CAMERA_CONTROL.AUTO_SOUND_AND_LIGHT_WARNING.PIID),
      LetDevice.getSingleProperty(SOUND_AND_LIGHT_WARNING.WARNING_REPETITION.PIID),
      LetDevice.getSingleProperty(SOUND_AND_LIGHT_WARNING.ENABLE_TIME_PERIOD.PIID),
      LetDevice.getSingleProperty(SOUND_AND_LIGHT_WARNING.WARNING_LIGHT_SWITCH.PIID),
      LetDevice.getSingleProperty(SOUND_AND_LIGHT_WARNING.WARNING_AUDIO_SWITCH.PIID),
      LetDevice.getSingleProperty(SOUND_AND_LIGHT_WARNING.WARNING_ALARM_CONFIG.PIID),
      LetDevice.getSingleProperty(SOUND_AND_LIGHT_WARNING.LIGHT_WARNING_MODE.PIID),
      LetDevice.getSingleProperty(SOUND_AND_LIGHT_WARNING.TRIGGER_EVENT.PIID),
    ];
    const results = await Promise.allSettled(promises);
    console.log(TAG, '_getAllValue results', results);

    let {lightSwitch, repeatValue, startTime, endTime, spotlightMode, triggerTypes, isAudioOn, isLightOn, containTime, audioPath} = this.state;

    let onlyPeopleSwitch = this.state.onlyPeopleSwitch;

    results.forEach(item => {
      if (item.status === 'fulfilled') {
        const value = item.value?.value?.value;
        console.log(TAG, `thingId: ${item.value?.thingId}, value: ${value}`);
        switch (String(item.value?.thingId)) {
          case CAMERA_CONTROL.AUTO_SOUND_AND_LIGHT_WARNING.PIID:
            lightSwitch = value;
            break;
          case SOUND_AND_LIGHT_WARNING.WARNING_REPETITION.PIID:
            repeatValue = JSON.parse(value);
            break;
          case SOUND_AND_LIGHT_WARNING.ENABLE_TIME_PERIOD.PIID:
            const timePeriod = value.split('-');
            if (timePeriod.length === 2) {
              startTime = timePeriod[0];
              endTime = timePeriod[1];
              if (startTime.length === 5) {
                startTime = startTime + ':00';
              }
              if (endTime.length === 5) {
                endTime = endTime + ':00';
              }
            }
            break;
          case SOUND_AND_LIGHT_WARNING.WARNING_LIGHT_SWITCH.PIID:
            isLightOn = value
            break;
          case SOUND_AND_LIGHT_WARNING.WARNING_AUDIO_SWITCH.PIID:
            isAudioOn = value
            break;
          case SOUND_AND_LIGHT_WARNING.WARNING_ALARM_CONFIG.PIID:
            const audioValue = JSON.parse(value)
            audioPath = audioValue.sound_file || '1-alarm'
            break;
          case SOUND_AND_LIGHT_WARNING.LIGHT_WARNING_MODE.PIID:
            const lightValue = JSON.parse(value)
            spotlightMode = lightValue.light_mode || SOUND_AND_LIGHT_WARNING.LIGHT_WARNING_MODE.ENUM.FAST_FLICKER;
            containTime = lightValue.period || 5
            break;
          case SOUND_AND_LIGHT_WARNING.SOUND_TEST.PIID:
            break;
          case SOUND_AND_LIGHT_WARNING.TRIGGER_EVENT.PIID:
            triggerTypes = JSON.parse(value)
            break;
          default:
            break;
        }
      }
    });
    this.setState({
      lightSwitch,
      onlyPeopleSwitch,
      repeatValue,
      startTime,
      endTime,
      triggerTypes,
      isLightOn,
      isAudioOn,
      spotlightMode,
      containTime,
      audioPath,
    });
    showLoading(false);
  }

  render() {
    const spotlightModeContent = this._getSpotlightModeContent();
    global.navigation = this.props.navigation;

    const {startTime, endTime, isAudioOn, audioPath} = this.state

    const isAllDay = startTime === '00:00:00' && endTime === '23:59:59';
    const effectTimeContent = isAllDay ? stringsTo('all_day') : startTime.substring(0, 5) + '-' + endTime.substring(0, 5);

    let voiceTipStr = stringsTo('voice_for_wu')
    if (isAudioOn) {
      voiceTipStr = this._getAudioStr(audioPath)
    }
    return (
      <View style={styles.container}>
        <NavigationBar
          title={stringsTo('soundLightAlarm')}
          left={[{key: NavigationBar.ICON.BACK, onPress: () => this._onPressBack()}]}
          right={[]}
        />
        {/*声光报警*/}
        <ListItmeWithSwitch
          title={stringsTo('soundLightAlarm')}
          value={this.state.lightSwitch}
          onValueChange={value => {
            const oldValue = this.state.lightSwitch;
            const piid = CAMERA_CONTROL.AUTO_SOUND_AND_LIGHT_WARNING.PIID;
            LetDevice.setProperties(
              true,
              LetDevice.deviceID,
              piid,
              JSON.stringify({
                msg_id: piid,
                value: value,
              }),
              true,
            )
              .then(() => {
                console.log(TAG, `update success: piid: ${piid}, value: ${value}`);
                this.setState({lightSwitch: value});
              })
              .catch(error => {
                console.log(TAG, `update failed: piid: ${piid}, value: ${value}, error: `, error);
                this.setState({lightSwitch: oldValue});
              });
          }}
        />

        {this.state.lightSwitch ? (
          <View style={{flex: 1}}>
            <ListItem
              title={stringsTo('alarm_time')}
              value={effectTimeContent}
              onPress={() => {
                this.props.navigation.navigate('EffectTimeConfigPage', {
                  type: 'lightEffectTime',
                  startTime: this.state.startTime,
                  endTime: this.state.endTime,
                  repeatValue: this.state.repeatValue,
                  callback: (startTime, endTime, repeatValue) => {
                    console.log(
                      TAG,
                      'navigate back value startTime, endTime, repeatValue: ',
                      startTime,
                      endTime,
                      repeatValue,
                    );
                    this.setState({startTime, endTime, repeatValue});
                    if (repeatValue) {
                      const piid = SOUND_AND_LIGHT_WARNING.WARNING_REPETITION.PIID;
                      LetDevice.setProperties(
                        true,
                        LetDevice.deviceID,
                        piid,
                        JSON.stringify({
                          msg_id: piid,
                          value: `[${repeatValue}]`,
                        }),
                        true,
                      );
                    }
                    if (startTime && endTime) {
                      const piid = SOUND_AND_LIGHT_WARNING.ENABLE_TIME_PERIOD.PIID;
                      const value = startTime + '-' + endTime;
                      LetDevice.setProperties(
                        true,
                        LetDevice.deviceID,
                        piid,
                        JSON.stringify({
                          msg_id: piid,
                          value: value,
                        }),
                        true,
                      );
                    }
                  },
                });
              }}
            />
            <ListItem
              title={stringsTo('alarm_voice')}
              value={voiceTipStr}
              onPress={() => {
                this.props.navigation.navigate('PromptVoiceSetPage', {
                  type: 'lightVoice',
                  isAudioOn: this.state.isAudioOn,
                  audioPath: this.state.audioPath,
                  callback: (audioPath, isAudioOn) => {
                    console.log(TAG, 'navigate back value audioPath: ', audioPath, ', isAudioOn: ', isAudioOn);
                    this.setState({ isAudioOn, audioPath })
                  },
                });
              }}
            />
            <ListItem
              title={stringsTo('alarm_light')}
              value={spotlightModeContent}
              onPress={() => {
                this.setState({
                  tempSpotlightMode: this.state.spotlightMode,
                  isLightOnTemp: this.state.isLightOn,
                  containTimeTemp: this.state.containTime,
                  showSpotlightModal: true
                });
              }}
            />
            <ListItem
              title={stringsTo('alarm_event_type')}
              onPress={() => {
                this.props.navigation.navigate('AlarmEventTypePage', {
                  triggerTypes: this.state.triggerTypes
                });
              }}
            />
          </View>
        ) : null}
        {/*聚光灯弹窗*/}
        {this._spotlightModal()}
      </View>
    );
  }

  // 聚光灯报警弹窗
  _spotlightModal() {
    const { isLightOn, isLightOnTemp, showSpotlightModal, tempSpotlightMode, spotlightMode, containTime, containTimeTemp} = this.state
    const warnMode = SOUND_AND_LIGHT_WARNING.LIGHT_WARNING_MODE.ENUM
    const isFlicker = isLightOnTemp && (tempSpotlightMode === warnMode.FAST_FLICKER || tempSpotlightMode === warnMode.SLOW_FLICKER)
    const isAlwaysLight = isLightOnTemp && tempSpotlightMode === warnMode.ALWAYS_LIGHT
    return (
      <View>
        <MessageDialog
          title={I18n.t('alarm_light')}
          visible={showSpotlightModal}
          canDismiss={false}
          buttons={[
            {
              text: I18n.t('cancel'),
              accessibilityLabel: 'cancelSpotWarning',
              callback: _ => {
                this.setState({showSpotlightModal: false});
              },
            },
            {
              text: I18n.t('ok_button'),
              accessibilityLabel: 'okSpotWarning',
              callback: _ => {
                const oldValue = spotlightMode;
                const oldLightOnValue = isLightOn;
                const oldContainTimeValue = containTime;
                this.setState({
                  showSpotlightModal: false,
                  spotlightMode: tempSpotlightMode,
                  isLightOn: isLightOnTemp,
                  containTime: containTimeTemp
                });

                const piid = SOUND_AND_LIGHT_WARNING.LIGHT_WARNING_MODE.PIID;
                LetDevice.setProperties(true, LetDevice.deviceID, piid,
                  JSON.stringify({
                    msg_id: piid,
                    value: `{"light_mode": ${tempSpotlightMode}, "period": ${containTimeTemp}}`,
                  }), true,
                )
                  .then(() => {
                    console.log(TAG, `update success: piid: ${piid}, value: ${value}`);
                  })
                  .catch(error => {
                    console.log(TAG, `update failed: piid: ${piid}, value: ${value}, error: `, error);
                    this.setState({spotlightMode: oldValue, containTime: oldContainTimeValue});
                  });

                const lightOnPiid = SOUND_AND_LIGHT_WARNING.WARNING_LIGHT_SWITCH.PIID;
                LetDevice.setProperties(true, LetDevice.deviceID, lightOnPiid,
                  JSON.stringify({
                    msg_id: lightOnPiid,
                    value: isLightOnTemp,
                  }), true,
                )
                  .then(() => {
                    console.log(TAG, `update success: piid: ${lightOnPiid}, value: ${value}`);
                  })
                  .catch(error => {
                    console.log(TAG, `update failed: piid: ${lightOnPiid}, value: ${value}, error: `, error);
                    this.setState({isLightOn: oldLightOnValue});
                  });
              },
            },
          ]}>
          {/*不亮*/}
          <TouchableWithoutFeedback onPress={() => {
            this.setState({ isLightOnTemp: false })
          }}>
            <View style={{flexDirection: 'column', height: 60, display: 'flex', alignItems: 'center', backgroundColor: 'white',}}>
              <View style={{flexDirection: 'row', width: '100%', height: '100%', alignItems: 'center'}}>
                <Text style={{flexGrow: 1, fontSize: 15, marginLeft: 20, textAlign: 'left',
                    color: !isLightOnTemp ? imiThemeManager.theme.primaryColor : '#333333',}}>
                  {stringsTo('potlight_not_bright')}
                </Text>
                <Image style={{height: 20, width: 20, marginRight: 20, resizeMode: 'contain'}} source={
                    !isLightOnTemp? require('../../resources/images/icon_select_s.png') : null}/>
              </View>
            </View>
          </TouchableWithoutFeedback>

          {/*闪烁*/}
          <TouchableWithoutFeedback onPress={() => {
            this.setState({ isLightOnTemp: true, tempSpotlightMode: warnMode.FAST_FLICKER })
          }}>
            <View style={{flexDirection: 'column', height: 60, display: 'flex', alignItems: 'center', backgroundColor: 'white',}}>
              <View style={{flexDirection: 'row', width: '100%', height: '100%', alignItems: 'center'}}>
                <Text style={{flexGrow: 1, fontSize: 15, marginLeft: 20, textAlign: 'left',
                  color: isFlicker ? imiThemeManager.theme.primaryColor : '#333333',}}>
                  {stringsTo('potlight_flash')}
                </Text>
                <Image style={{height: 20, width: 20, marginRight: 20, resizeMode: 'contain'}} source={
                  isFlicker ? require('../../resources/images/icon_select_s.png') : null}/>
              </View>
            </View>
          </TouchableWithoutFeedback>

          {/*常亮*/}
          <TouchableWithoutFeedback onPress={() => {
            this.setState({ isLightOnTemp: true, tempSpotlightMode: warnMode.ALWAYS_LIGHT })
          }}>
            <View style={{flexDirection: 'column', height: 60, display: 'flex', alignItems: 'center', backgroundColor: 'white',}}>
              <View style={{flexDirection: 'row', width: '100%', height: '100%', alignItems: 'center'}}>
                <Text style={{flexGrow: 1, fontSize: 15, marginLeft: 20, textAlign: 'left',
                  color: isAlwaysLight ? imiThemeManager.theme.primaryColor : '#333333',}}>
                  {stringsTo('potlight_long_bright')}
                </Text>
                <Image style={{height: 20, width: 20, marginRight: 20, resizeMode: 'contain'}} source={
                  isAlwaysLight ? require('../../resources/images/icon_select_s.png') : null}/>
              </View>
            </View>
          </TouchableWithoutFeedback>

          {!isLightOnTemp ? null : (
            <View style={{flex: 1}}>
              <View style={{width: '100%', height: 60, backgroundColor: 'white'}}>
                <Text
                  style={{
                    flexGrow: 1,
                    fontSize: 15,
                    marginLeft: 20,
                    color: '#333333',
                    textAlign: 'left',
                    lineHeight: 56,
                  }}>
                  {stringsTo('potlight_contain_time') + '(' + containTimeTemp + 'S' + ')'}
                </Text>
              </View>
              <SlideGear
                options={Array.from({length: 26}, (v, i) => i + 1)}
                indicatorTextArray={[5, 10, 15, 20, 25, 30]}
                value={containTimeTemp - 5}
                onValueChange={index => {
                  this.setState({
                    containTimeTemp: index + 5,
                  });
                }}
              />
            </View>
          )}
        </MessageDialog>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: imiThemeManager.theme.pageBg,
  },
});
