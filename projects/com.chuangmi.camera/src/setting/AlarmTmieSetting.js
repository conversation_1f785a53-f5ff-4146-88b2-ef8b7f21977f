import React from 'react';

import {StyleSheet, View, Image, Text, TouchableWithoutFeedback, Dimensions, BackHandler} from 'react-native';

import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';

import Separator from '../../../../imilab-design-ui/src/widgets/settingUI/Separator';
import I18n, {stringsTo} from '../../../../globalization/Localize';
import BaseDeviceComponent from '../../../../imilab-rn-sdk/components/BaseDeviceComponent';
import DatePicker from '../../../../imilab-design-ui/src/widgets/settingUI/DatePicker';

import {LetDevice, LetIMIIotRequest} from '../../../../imilab-rn-sdk';
import {showLoading, showToast} from '../../../../imilab-design-ui';
import {isAndroid} from '../../../../imilab-rn-sdk/utils/Utils';
import IMILogUtil from '../../../../imilab-rn-sdk/native/local-kit/IMILogUtil';
const screen_width = Dimensions.get('window').width;
import ListItem from '../../../../imilab-design-ui/src/widgets/settingUI/ListItem';

/**
 * 看家时间设置页面
 */

export default class AlarmTmieSetting extends BaseDeviceComponent {
  static propTypes = {};

  constructor(props, context) {
    super(props, context);
    this.state = {
      alarmMode: 0,
      isPickerVisiable: false,
      isStartPicker: true,
      startTime: '',
      endTime: '',
    };
    this.noticeData = {};
    this.initMode = 0;
  }
  componentDidMount() {
    console.log('nightFunctionPage --', this.props.route.params?.alarmData);
    if (this.props.route.params?.alarmData !== undefined) {
      //   this.setState({
      //     alarmMode: this.props.route.params.alarmData,
      //   });
      this.noticeData = this.props.route.params.alarmData;
      if (
        this.props.route.params.alarmData.timeBegin == '00:00:00' &&
        this.props.route.params.alarmData.timeEnd == '23:59:59'
      ) {
        this.setState({
          alarmMode: 0,
        });
        this.initMode = 0;
      } else if (
        this.props.route.params.alarmData.timeBegin == '08:00:00' &&
        this.props.route.params.alarmData.timeEnd == '20:00:00'
      ) {
        this.setState({
          alarmMode: 2,
        });
        this.initMode = 2;
      } else if (
        this.props.route.params.alarmData.timeBegin == '20:00:00' &&
        this.props.route.params.alarmData.timeEnd == '08:00:00'
      ) {
        this.setState({
          alarmMode: 1,
        });
        this.initMode = 1;
      } else {
        this.setState({
          alarmMode: 3,
          startTime: this.props.route.params.alarmData.timeBegin,
          endTime: this.props.route.params.alarmData.timeEnd,
        });
        this.initMode = 3
      }
    }
  }
  
  UNSAFE_componentWillMount() {
    if (isAndroid()) {
      BackHandler.addEventListener('hardwareBackPress', this.onBackHandler);
    }
  }
  componentWillUnmount() {
    if (isAndroid()) {
      BackHandler.removeEventListener('hardwareBackPress', this.onBackHandler);
    }
    showLoading(false);
  }

  onBackHandler = () => {
    console.log('onBackAndroid  navigatiosn.isFocused() ' + navigation.isFocused());
    if (navigation.isFocused()) {
      this._onPressBack();
      return true;
    } else {
    }
    return false;
  };

  // 返回上一页
  _onPressBack = () => {
    if (this.props.route.params.callback) {
      console.log('返回传值', this.state.alarmMode);
      let tempStr;
      if (this.state.alarmMode == 1) {
        //晚上模式
        tempStr = stringsTo('alarm_time_night'); // 已开启
      } else if (this.state.alarmMode == 0) {
        // 全天模式
        tempStr = stringsTo('alarm_time_all');
      } else if (this.state.alarmMode == 2) {
        // 白天模式
        tempStr = stringsTo('alarm_time_day');
      } else if (this.state.alarmMode == 3) {
        if (!this.state.startTime || !this.state.endTime) {
          showToast(stringsTo('date_picker_time_title'));
          return
        }
        // 自定义
        tempStr = stringsTo('alarm_direction_custom') + '(' + this.state.startTime?.slice(0, -3) + '-' + this.state.endTime?.slice(0, -3) + ')';
      }

      console.log('返回值---', tempStr);
      this.props.route.params.callback(tempStr);
    }
    this.props.navigation.pop();
  };

  // 设置看家开关属性
  setSwitchValueByCloud() {
    const params = {
      Path: '/v1.0/imilab-01/app/cloudstorage/set-video-plan',
      ParamMap: {
        iotId: LetDevice.deviceID,
        ...this.noticeData,
      },
      Method: 'POST',
    };
    showLoading(stringsTo('commWaitText'), true);
    LetIMIIotRequest.sendUserServerRequest(params, false)
      .then(item => {
        showToast(stringsTo('settings_set_success'))
        // this._onPressBack();
      })
      .catch(e => {
        showToast(I18n.t('operationFailed'));
        this.setState({
          alarmMode: this.initMode
        })
        console.log(JSON.stringify(e));
      })
      .finally(() => {
        showLoading(false);
      });
  }
 getNextDayTime(){
  
  const isNextDay=this.state.startTime>this.state.endTime
  return isNextDay?stringsTo('setting_monitor_next_day'):''
 }
  render() {
    return (
      <View style={styles.container}>
        <NavigationBar
          title={stringsTo('alarm_time_set')}
          left={[{key: NavigationBar.ICON.BACK, onPress: () => this._onPressBack()}]}
          right={[]}
        />
        <Separator />
        <View style={{width: '100%', height: 210}}>
          {this._renderItemOne()}
          {this._renderItemTwo()}
          {this._renderItemThe()}
          {this._renderItemFour()}
          {/* {this.state.alarmMode === 3 ? this._renderShowTime() : null} */}
          {this._showDatePicker()}
        </View>
      </View>
    );
  }
  _renderItemOne() {
    return (
      <View style={{width: '100%', height: 70, backgroundColor: 'white'}}>
        <TouchableWithoutFeedback
          onPress={_ => {
            this.noticeData.timeBegin = '00:00:00';
            this.noticeData.timeEnd = '23:59:59';
            this.setState({
              alarmMode: 0,
            });
            this.setSwitchValueByCloud();
          }}>
          <View style={{flexDirection: 'column', height: 70, backgroundColor: 'white'}}>
            <View style={{flex: 1}}>
              <Text
                style={{
                  marginLeft: 14,
                  marginTop: 8,
                  lineHeight: 22,
                  width: screen_width - 14 * 3 - 25,
                  flexGrow: 1,
                  fontSize: 15,
                  textAlign: 'left',
                  justifyContent: 'center',
                  color: this.state.alarmMode == 0 ? '#496EE0' : '#000000',
                }}>
                {/*  {stringsTo('full_color_vision_title')}  因为物模型是按照060a02，所以只有关闭自动，开启，微光全彩改成关闭*/}
                {stringsTo('alarm_time_all')}
              </Text>
              <Text
                numberOfLines={2}
                style={{
                  marginLeft: 14,
                  height: 32,
                  width: screen_width - 14 * 3 - 25,
                  fontSize: 12,
                  textAlign: 'left',
                  color: '#828282',
                }}>
                {stringsTo('alarm_time_24') + stringsTo('alarm_time_info')}
              </Text>
            </View>
            <View style={{flexDirection: 'row', position: 'absolute', flex: 1, right: 14}}>
              <Image
                style={{
                  marginTop: 22.5,
                  height: 25,
                  width: 25,
                  tintColor: this.state.alarmMode == 0 ? null : 'transparent',
                }}
                source={require('../../resources/images/icon_select_s.png')}
              />
            </View>
          </View>
        </TouchableWithoutFeedback>
      </View>
    );
  }

  _renderItemTwo() {
    return (
      <View style={{width: '100%', height: 70, backgroundColor: 'white'}}>
        <TouchableWithoutFeedback
          onPress={_ => {
            this.noticeData.timeBegin = '20:00:00';
            this.noticeData.timeEnd = '08:00:00';
            this.setState({
              alarmMode: 1,
            });
            this.setSwitchValueByCloud();
          }}>
          <View style={{flexDirection: 'column', height: 70, backgroundColor: 'white'}}>
            <View style={{flex: 1}}>
              <Text
                style={{
                  marginLeft: 14,
                  marginTop: 8,
                  lineHeight: 22,
                  width: screen_width - 14 * 3 - 25,
                  flexGrow: 1,
                  fontSize: 15,
                  textAlign: 'left',
                  justifyContent: 'center',
                  color: this.state.alarmMode == 1 ? '#496EE0' : '#000000',
                }}>
                {stringsTo('alarm_time_night')}
              </Text>
              <Text
                numberOfLines={2}
                style={{
                  marginLeft: 14,
                  height: 32,
                  width: screen_width - 14 * 3 - 25,
                  fontSize: 12,
                  textAlign: 'left',
                  color: '#828282',
                }}>
                {stringsTo('alarm_time_208') + stringsTo('alarm_time_info')}
              </Text>
            </View>
            <View style={{flexDirection: 'row', position: 'absolute', flex: 1, right: 14}}>
              <Image
                style={{
                  marginTop: 22.5,
                  height: 25,
                  width: 25,
                  tintColor: this.state.alarmMode == 1 ? null : 'transparent',
                }}
                source={require('../../resources/images/icon_select_s.png')}
              />
            </View>
          </View>
        </TouchableWithoutFeedback>
      </View>
    );
  }

  _renderItemThe() {
    return (
      <View style={{width: '100%', height: 70, backgroundColor: 'white'}}>
        <TouchableWithoutFeedback
          onPress={_ => {
            this.noticeData.timeBegin = '08:00:00';
            this.noticeData.timeEnd = '20:00:00';
            this.setState({
              alarmMode: 2,
            });
            this.setSwitchValueByCloud();
          }}>
          <View style={{flexDirection: 'column', height: 70, backgroundColor: 'white'}}>
            <View style={{flex: 1}}>
              <Text
                style={{
                  marginLeft: 14,
                  marginTop: 8,
                  lineHeight: 22,
                  width: screen_width - 14 * 3 - 25,
                  flexGrow: 1,
                  fontSize: 15,
                  textAlign: 'left',
                  justifyContent: 'center',
                  color: this.state.alarmMode == 2 ? '#496EE0' : '#000000',
                }}>
                {/*  {stringsTo('fullColor_smart_tit')} //因为物模型是按照060a02，所以只有关闭自动，开启，智能夜视4改成改成自动*/}
                {stringsTo('alarm_time_day')}
              </Text>
              <Text
                numberOfLines={2}
                style={{
                  marginLeft: 14,
                  height: 32,
                  width: screen_width - 14 * 3 - 25,
                  fontSize: 12,
                  textAlign: 'left',
                  color: '#828282',
                }}>
                {stringsTo('alarm_time_820') + stringsTo('alarm_time_info')}
              </Text>
            </View>
            <View style={{flexDirection: 'row', position: 'absolute', flex: 1, right: 14}}>
              <Image
                style={{
                  marginTop: 22.5,
                  height: 25,
                  width: 25,
                  tintColor: this.state.alarmMode == 2 ? null : 'transparent',
                }}
                source={require('../../resources/images/icon_select_s.png')}
              />
            </View>
          </View>
        </TouchableWithoutFeedback>
      </View>
    );
  }
  _renderItemFour() {
    return (
      <View style={{width: '100%', height: 70, backgroundColor: 'white'}}>
        <TouchableWithoutFeedback
          onPress={_ => {
            // this.noticeData.timeBegin = '08:00:00';
            // this.noticeData.timeEnd = '20:00:00';
            // this.setSwitchValueByCloud();
            this.props.navigation.push('AlarmTmieSelect', {
              alarmData: this.noticeData,
              callback: alarmstr => {
                console.log('返回修改侦测时间--', alarmstr);
                this.noticeData.timeBegin = alarmstr.startTime;
                this.noticeData.timeEnd = alarmstr.endTime;
                if (
                  alarmstr.startTime == '00:00:00' &&
                  alarmstr.endTime == '23:59:59'
                ) {
                  this.setState({
                    alarmMode: 0,
                    startTime: alarmstr.startTime,
                    endTime: alarmstr.endTime,
                  });
                } else if (
                  alarmstr.startTime == '08:00:00' &&
                  alarmstr.endTime == '20:00:00'
                ) {
                  this.setState({
                    alarmMode: 2,
                    startTime: alarmstr.startTime,
                    endTime: alarmstr.endTime,
                  });
                } else if (
                  alarmstr.startTime == '20:00:00' &&
                  alarmstr.endTime == '08:00:00'
                ) {
                  this.setState({
                    alarmMode: 1,
                    startTime: alarmstr.startTime,
                    endTime: alarmstr.endTime,
                  });
                } else {
                  this.setState({
                    alarmMode: 3,
                    startTime: alarmstr.startTime,
                    endTime: alarmstr.endTime,
                  });
                }
              },
            });
          }}>
          <View style={{flexDirection: 'column', height: 70, backgroundColor: 'white'}}>
            <View style={{flex: 1}}>
              <Text
                style={{
                  marginLeft: 14,
                  marginTop: 8,
                  lineHeight: 22,
                  width: screen_width - 14 * 3 - 25,
                  flexGrow: 1,
                  fontSize: 15,
                  textAlign: 'left',
                  justifyContent: 'center',
                  color: this.state.alarmMode == 3 ? '#496EE0' : '#000000',
                }}>
                {/*  {stringsTo('fullColor_smart_tit')} //因为物模型是按照060a02，所以只有关闭自动，开启，智能夜视4改成改成自动*/}
                {stringsTo('alarm_direction_custom')}
              </Text>
              <Text
                numberOfLines={2}
                style={{
                  marginLeft: 14,
                  height: 32,
                  width: screen_width - 14 * 3 - 25,
                  fontSize: 12,
                  textAlign: 'left',
                  color: '#828282',
                }}>
                {
                  (!this.state.startTime?.slice(0, -3) && !this.state.endTime?.slice(0, -3)) || this.state.alarmMode != 3
                  ? stringsTo('alarm_time_info') : this.state.startTime?.slice(0, -3) + '-' + this.getNextDayTime()+this.state.endTime?.slice(0, -3) + '，' + stringsTo('alarm_time_info')
                }
              </Text>
            </View>
            <View style={{flexDirection: 'row', position: 'absolute', flex: 1, right: 14}}>
              <Image
                style={{
                  marginTop: 22.5,
                  height: 25,
                  width: 25,
                  tintColor: this.state.alarmMode == 3 ? null : 'transparent',
                }}
                source={require('../../resources/images/icon_select_s.png')}
              />
            </View>
          </View>
        </TouchableWithoutFeedback>
      </View>
    );
  }

  _renderShowTime() {
    return (
      <View>
        <ListItem
          titleStyle={{fontSize: 15, fontWeight: 'normal', color: '#000000'}}
          title={I18n.t('picker_start_time')}
          hideArrow={false}
          value={this.state.startTime?.slice(0, -3)}
          onPress={() => {
            if (this.state.alarmMode != 3) {
              showToast(I18n.t('noDIYTimeTip'));
              return;
            }
            this.setState({isPickerVisiable: true, isStartPicker: true});
          }}
        />
        <ListItem
          titleStyle={{fontSize: 15, fontWeight: 'normal', color: '#000000'}}
          title={I18n.t('picker_end_time')}
          hideArrow={false}
          value={this.state.endTime?.slice(0, -3)}
          onPress={() => {
            if (this.state.alarmMode != 3) {
              showToast(I18n.t('noDIYTimeTip'));
              return;
            }
            this.setState({isPickerVisiable: true, isStartPicker: false});
          }}
        />
      </View>
    );
  }
  _showDatePicker() {

    return (
      <View>
        <DatePicker
          visible={this.state.isPickerVisiable}
          title={this.state.isStartPicker ? I18n.t('picker_start_time') : I18n.t('picker_end_time')}
          type={'time24'}
          onDismiss={_ => {}}
          // currentSelectTime={this.state.isStartPicker ? this.state.startTimeArr:this.state.endTimeArr}
          onSelectConfirm={time => {
            console.log('当前选择的时间为-------', time.rawArray, time.date);
            if (this.state.isStartPicker) {
              if (time.rawArray[0] + ':' + time.rawArray[1] + ':00' === this.state.endTime) {
                this.setState({isPickerVisiable: false})
                showToast(stringsTo('time_equal'));
                return
              }
              this.setState(
                {isPickerVisiable: false, startTime: time.rawArray[0] + ':' + time.rawArray[1] + ':00'},
                () => {
                  console.log('当前开始的时间为-------', this.state.startTime, time.rawArray, time.date);
                  if (this.state.endTime != '') {
                    this.noticeData.timeBegin = this.state.startTime;
                    this.noticeData.timeEnd = this.state.endTime;
                    // this.setSwitchValueByCloud(true);
                  }
                },
              );
            } else {
              if (time.rawArray[0] + ':' + time.rawArray[1] + ':00' === this.state.startTime) {
                this.setState({isPickerVisiable: false})
                showToast(stringsTo('time_equal'));
                return
              }
              this.setState(
                {isPickerVisiable: false, endTime: time.rawArray[0] + ':' + time.rawArray[1] + ':00'},
                () => {
                  console.log('当前结束的时间为-------', this.state.endTime, time.rawArray, time.date);
                  if (this.state.startTime != '') {
                    this.noticeData.timeBegin = this.state.startTime;
                    this.noticeData.timeEnd = this.state.endTime;
                    // this.setSwitchValueByCloud(true);
                  }
                },
              );
            }
          }} //["02", "02"], Tue Nov 24 2020 02:02:43 GMT+0800 (China Standard Time)
        />
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
});
