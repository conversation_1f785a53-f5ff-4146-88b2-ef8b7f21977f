/* eslint-disable react-hooks/exhaustive-deps */
import {Text, View, StyleSheet, ScrollView, TextInput, TouchableOpacity, KeyboardAvoidingView, Image, Dimensions, Keyboard} from 'react-native';
import React, {useState, useRef, useEffect} from 'react';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import {IMIGotoPage, IMIStorage, LetDevice} from '../../../../imilab-rn-sdk';
import I18n, {stringsTo} from '../../../../globalization/Localize';
import MessageDialog from '../../../../imilab-design-ui/src/widgets/settingUI/MessageDialog';
import {showLoading, showToast} from '../../../../imilab-design-ui/src/widgets/Loading';
import CommonUtils from '../utils/CommonUtils';
const AddFrequentlyLocationPhotoKey = LetDevice.deviceID + 'addFrequentlyLocationPhoto';
const newWidth = Math.min(Dimensions.get('window').width, Dimensions.get('window').height);

const CommonAngles = props => {
  const {idDelete} = props.route.params || {};
  const [inputValue, setInputValue] = useState('');
  const [inputValueError, setInputValueError] = useState('');
  // const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);

  const [editingAngle, setEditingAngle] = useState(null);
  const [deletingAngleIndex, setDeletingAngleIndex] = useState(null);
  const [deleteMode, setDeleteMode] = useState(!!idDelete);
  const [deleteTips, setDeleteTips] = useState(false);
  const [frequentlyLocationPhotoList, setFrequentlyLocationPhotoList] = useState([]);
  const [deleteCreateTimes, setDeleteCreateTimes] = useState([]);
  const [keyHeight, setKeyHeight] = useState(0);
  const editInputRef = useRef(null);
  const initList = useRef({});
  useEffect(() => {
    let focusNav = props.navigation.addListener('focus', () => {
      const setLocationList = (res, data) => {
        let list = data.value.value ? JSON.parse(data.value.value) : [];
        const mapList = new Map();
        list.forEach(item => {
          mapList.set(item.createTime, item);
        });
        const list2 = JSON.parse(res);
        list2.forEach(item => {
          if (mapList.has(item.createTime)) {
            mapList.delete(item.createTime);
            mapList.set(item.createTime, item);
          }
        });
        const newList = Array.from(mapList.values());
        const newInitList = newList.slice(0, 6).sort((a, b) => b.createTime - a.createTime);
        initList.current = [...newInitList];
        setFrequentlyLocationPhotoList([...newInitList]);
      };
      LetDevice.getSingleProperty('10028').then(data => {
        if (data.value?.code == 0) {
          IMIStorage.load({
            key: AddFrequentlyLocationPhotoKey,
            autoSync: true,
            syncInBackground: true,
          })
            .then(res => {
              setLocationList(res, data);
            })
            .catch(() => {
              setLocationList(JSON.stringify([]), data);
            });
        }
      });
    });
    return () => {
      focusNav?.remove && focusNav?.remove();
    };
  }, []);
  useEffect(() => {
    if (!deleteMode) {
      setDeleteCreateTimes([]);
    }
  }, [deleteMode]);
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      (e) => {
      
       setKeyHeight(e.endCoordinates.height)
      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      (e) => {
       setKeyHeight(0)
      }
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);
  // 验证角度名称
  const validateAngleName = (name, excludeId = null) => {
    if (!name.trim()) {
      setInputValueError(stringsTo('empty_not_support'));
      return false;
    }
    // if (name.length > 8) {
    //   setInputValueError(stringsTo('empty_not_support'));
    //   return false;
    // }
    if (frequentlyLocationPhotoList.some(angle => angle.name.trim() === name.trim() && angle.idx !== excludeId)) {
      setInputValueError(stringsTo('name_already_exists'));
      return false;
    }
    return true;
  };

  // 编辑角度名称
  const handleEditAngle = () => {
    if (validateAngleName(inputValue, editingAngle.idx)) {
      setFrequentlyLocationPhotoList(
        frequentlyLocationPhotoList.map(angle =>
          angle.idx === editingAngle.idx ? {...angle, name: inputValue.trim()} : angle,
        ),
      );
      setInputValue('');
      setShowEditModal(false);
      setEditingAngle(null);
    }
  };

  // 点击名称编辑
  const handleNamePress = angle => {
    setEditingAngle(angle);
    setInputValue(angle.name||'');
    setShowEditModal(true);
    // setDeleteMode(false);
    // setTimeout(() => {
    //   editInputRef.current?.focus();
    // }, 100);
  };

  // 点击删除按钮
  const handleDeletePress = index => {
    // setDeletingAngleId(id);
    if (frequentlyLocationPhotoList.length < 3) {
      setDeletingAngleIndex(index);
      setDeleteTips(true);
      return;
    }
    deleteData(index);
  };
  const deleteData = index => {
    const newList = frequentlyLocationPhotoList;
    const createTime = newList[index]?.createTime;
    const newCreateTimes = deleteCreateTimes;
    if (createTime) {
      newCreateTimes.push(createTime);
    }
    newList.splice(index, 1);
    setDeleteTips(false);
    setDeleteCreateTimes(newCreateTimes);
    setFrequentlyLocationPhotoList([...newList]);
  };
  // 打开添加对话框
  const handleAddPress = () => {
    props.navigation.push('AddCommonAngles', {anglesName: inputValue});
    // setShowAddModal(true);
    // setTimeout(() => {
    //   inputRef.current?.focus();
    // }, 100);
  };

  // 关闭编辑对话框
  const handleCloseEditModal = () => {
    setShowEditModal(false);
    setInputValue('');
    setEditingAngle(null);
    setInputValueError('');
  };

  // 点击其他地方取消长按状态
  const handleContainerPress = () => {
    setDeleteMode(false);
  };
  const compareArraysDeep = (arr1, arr2) => {
    if (arr1.length !== arr2.length) {
      return false;
    }

    for (let i = 0; i < arr1.length; i++) {
      if (!CommonUtils.deepEqual(arr1[i], arr2[i])) {
        return false;
      }
    }

    return true;
  };
  const saveAngles = () => {
    if (compareArraysDeep(frequentlyLocationPhotoList, initList.current)) {
      handleContainerPress();
      return;
    }
    try {
      showLoading(stringsTo('setting_in_progress'), true);
      LetDevice.getSingleProperty('10028').then(data => {
        if (data.value?.code == 0) {
          let list1 = data.value.value ? JSON.parse(data.value.value) : [];
          const mapList = new Map();
          list1.forEach(item => {
            if (!deleteCreateTimes.includes(item.createTime)) {
              mapList.set(item.createTime, item);
            }
          });
          frequentlyLocationPhotoList.forEach(item => {
            if (!deleteCreateTimes.includes(item.createTime)) {
              mapList.set(item.createTime, item);
            }
          });
          const newList = Array.from(mapList.values());
          if (newList.length > 6) {
            showToast(stringsTo('limit_reached_v6'));
          } else {
            const equipmentList = newList.map((item, index) => {
              return {
                idx: index,
                name: item.name,
                pos: item.pos,
                createTime: item.createTime,
              };
            });

            const paramJson = JSON.stringify({msg_id: '10028', value: JSON.stringify(equipmentList)});
      

            LetDevice.setProperties(true, LetDevice.deviceID, '10028', paramJson, true).then(() => {
              handleContainerPress();
              setFrequentlyLocationPhotoList([...newList]);
              initList.current = newList;
              IMIStorage.save({
                key: AddFrequentlyLocationPhotoKey,
                data: JSON.stringify(newList),
                expires: null,
              });
              if(newList.length<2){
                  const paramJson1 = JSON.stringify({msg_id: '10037', value: 0});
              LetDevice.setProperties(true, LetDevice.deviceID, '10037', paramJson1, true)
              }
            });
          }
        }
      });
    } catch (error) {
      console.log('保存失败', error);
      console.log('保存失败', error);
      showToast(stringsTo('operationFailed'));
      setFrequentlyLocationPhotoList([...initList.current]);
    } finally {
      showLoading('', false);
    }
    // showLoading(stringsTo('setting_in_progress'), true);
    // const equipmentList = [];
    // const list = frequentlyLocationPhotoList.map((item, index) => {
    //   equipmentList.push({
    //     idx: index,
    //     pos: item.pos,
    //     name: item.name,
    //   });
    //   return {
    //     idx: index,
    //     pos: item.pos,
    //     path: item.path,
    //     name: item.name,
    //   };
    // });

    // const paramJson = JSON.stringify({msg_id: '10028', value: JSON.stringify(list)});

    // LetDevice.setProperties(true, LetDevice.deviceID, '10028', paramJson)
    //   .catch(error => {
    //     console.log('保存失败', error);
    //     showToast(stringsTo('operationFailed'));
    //     setFrequentlyLocationPhotoList([...initList.current]);
    //   })
    //   .then(() => {
    //     handleContainerPress();
    //     initList.current = frequentlyLocationPhotoList;
    //     IMIStorage.save({
    //       key: AddFrequentlyLocationPhotoKey,
    //       data: JSON.stringify(list),
    //       expires: null,
    //     });
    //   })
    //   .finally(() => {
    //     showLoading('', false);
    //   });
  };
  // 渲染角度项
  const renderAngleItem = (item, index) => {
    if (deleteMode) {
      return (
        <View key={item.path || index} style={[styles.angleItem, styles.delAngleItem]}>
          {/* 删除模式下的名称和删除按钮布局 */}
          <View style={styles.deleteNameContainer}>
            <TouchableOpacity style={styles.nameContainerFlex} onPress={() => handleNamePress(item)}>
              <Text style={[styles.angleName, styles.delAngleName]} numberOfLines={1}>
                {item.name}
              </Text>
              <Text style={styles.delAngleSubName} numberOfLines={1}>
                {stringsTo('rename')}
                <Image style={styles.rightArrow} source={require('../../resources/images/rightArrow.png')} />
              </Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.deleteIconFlex} onPress={() => handleDeletePress(index)}>
              <Image
                style={styles.deleteIconCircle}
                source={require('../../resources/images/icon_delete_angles.png')}
              />
            </TouchableOpacity>
          </View>

          <Image style={[styles.anglePreview, styles.delAnglePreview]} source={{uri: 'file://' + item.path}} />
        </View>
      );
    }
    return (
      <View key={item.path || index} style={styles.angleItem}>
        <TouchableOpacity style={styles.angleContent}>
          <Image style={[styles.anglePreview]} source={{uri: 'file://' + item.path}} />
        </TouchableOpacity>

        {/* 名称 - 可点击编辑 */}
        <TouchableOpacity style={styles.nameContainer} onPress={() => handleNamePress(item)}>
          <Text style={styles.angleName} numberOfLines={1}>
            {item.name}
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  // 渲染添加按钮
  const renderAddButton = () => (
    <View style={styles.angleItem} key={'renderAddButton'}>
      <TouchableOpacity style={styles.addButton} onPress={handleAddPress}>
        <Image style={styles.addIcon} source={require('../../resources/images/icon_add2x.png')} />
      </TouchableOpacity>
      <View style={styles.nameContainer}>
        <Text style={styles.addText}>{stringsTo('add_common_angle')}</Text>
      </View>
    </View>
  );

  // 创建网格数据
  const createGridData = () => {
    const gridData = [...frequentlyLocationPhotoList];
    //.concat([{type: 'add', key: 'add'}])

    if (!deleteMode && frequentlyLocationPhotoList.length < 6) {
      gridData.unshift({type: 'add', key: 'add'});
    }

    return gridData;
  };

  return (
    <KeyboardAvoidingView  behavior={Platform.OS === "ios" ? "padding" : "height"} 
    style={[styles.container, {backgroundColor: deleteMode ? '#F7F7F7' : '#FFFFFF'}]}
     keyboardVerticalOffset={Platform.OS === "ios" ? keyHeight : 0}>
      <NavigationBar
        title={stringsTo('common_angles')}
        left={[
          {
            key: deleteMode ? NavigationBar.ICON.CUSTOM : NavigationBar.ICON.BACK,
            n_source: require('../../resources/images/icon_cancel.png'),
            onPress: () => {
              if (deleteMode) {
                handleContainerPress();
                console.log('initList.current', initList.current.length);

                setFrequentlyLocationPhotoList([...initList.current]);
              } else {
                props.navigation.canGoBack() ? props.navigation.goBack() : IMIGotoPage.exit();
              }
            },
            accessibilityLabel: 'cruise_control',
          },
        ]}
        right={
          (frequentlyLocationPhotoList.length < 1&&!deleteMode)
            ? []
            : [
                {
                  key: deleteMode ? NavigationBar.ICON.COMPLETE : NavigationBar.ICON.CUSTOM,
                  n_source: require('../../resources/images/icon_edit.png'),
                  onPress: () => {
                    if (deleteMode) {
                      saveAngles();
                    } else {
                      setDeleteMode(true);
                    }
                  },
                  accessibilityLabel: 'cruise_control',
                },
              ]
        }
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.gridContainer}>
          {createGridData().map((item, index) => {
            if (item.type === 'add') {
              return renderAddButton();
            }
            return renderAngleItem(item, index);
          })}
        </View>
       {/* 编辑角度名称弹窗 - 使用MessageDialog */}
      <MessageDialog
        title={stringsTo('enter_Name')}
        visible={showEditModal}
        onDismiss={handleCloseEditModal}
        messageContainerStyle={{paddingBottom: 28+keyHeight, paddingLeft: 28, paddingRight: 28}}
        buttons={[
          {
            text: I18n.t('cancel'),
            callback: handleCloseEditModal,
            btnStyle: styles.btnCancelStyle,
          },
          {
            text: stringsTo('ok_button'),
            callback: handleEditAngle,
            disabled: inputValue?.length === 0,
            btnStyle: {
              ...styles.btnStyle,
              ...{
                opacity: inputValue?.length === 0 ? 0.3 : 1,
              },
            },
          },
        ]}>
        <View style={styles.dialogContent}>
          <View style={styles.inputContainer}>
            <TextInput
              ref={editInputRef}
              style={[styles.textInput, {borderWidth: inputValueError ? 1 : 0}]}
              placeholderTextColor="#999"
              value={inputValue}
              onChange={() => {
                setInputValueError('');
              }}
              maxLength={8}
              onChangeText={setInputValue}
            />
            <Text style={styles.inputValueError}>{inputValueError}</Text>
          </View>
        </View>
      </MessageDialog>
      </ScrollView>

       
      {/* 小于两个删除 */}
      <MessageDialog
        showTitle={false}
        visible={deleteTips}
        onDismiss={() => setDeleteTips(false)}
        messageContainerStyle={{paddingBottom: 28, paddingLeft: 28, paddingRight: 28}}
        buttons={[
          {
            text: I18n.t('cancel'),
            callback: () => setDeleteTips(false),
            btnStyle: styles.btnCancelStyle,
          },
          {
            text: stringsTo('ok_button'),
            callback: () => deleteData(deletingAngleIndex),
            btnStyle: styles.btnStyle,
          },
        ]}>
        <View style={styles.dialogContent}>
          <Text style={styles.deleteTips}>{stringsTo('common_angles_less_than_2')}</Text>
        </View>
      </MessageDialog>
    </KeyboardAvoidingView >
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
  
    paddingHorizontal: 16,
  },
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingTop: 20,
  },
  angleItem: {
    width: '48%',
    marginBottom: 9,
  },
  delAngleItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    alignItems: 'center',
    padding: 12,
  },
  angleItemDelete: {
    backgroundColor: '#FFFFF',
  },
  angleContent: {
    backgroundColor: '#F7F7F7',
    borderRadius: 10,
  },
  anglePreview: {
    width: '100%',
    height: 100,
    borderRadius: 10,
  },
  delAnglePreview: {},
  // 删除模式下的名称和删除按钮容器
  deleteNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 8,
    paddingVertical: 5,
  },
  // 删除模式下的名称容器
  nameContainerFlex: {
    flex: 1,
    marginRight: 8,
  },
  // 删除模式下的删除图标
  deleteIconFlex: {
    width: 22,
    height: 22,
  },
  nameContainer: {
    paddingHorizontal: 8,
    paddingVertical: 5,
  },
  angleName: {
    fontSize: 14,
    color: '#333',
    textAlign: 'center',
  },
  delAngleName: {
    textAlign: 'left',
    fontSize: 12,
    fontWeight: 'bold',
  },
  delAngleSubName: {
    fontSize: 12,
    color: '#7F7F7F',
  },
  rightArrow: {
    width: 12,
    height: 12,
    marginLeft: 4,
  },
  addButton: {
    backgroundColor: '#F7F7F7',
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    height: 100,
  },
  addIcon: {
    width: 30,
    height: 30,
  },
  addIconText: {
    fontSize: 32,
    color: '#999',
    fontWeight: '300',
  },
  addText: {
    fontSize: 14,
    color: '#7F7F7F',
    textAlign: 'center',
  },
  // MessageDialog 相关样式
  dialogContent: {
    paddingVertical: 0,
  },
  deleteTips: {
    color: 'rgba(0,0,0,0.8)',
    fontSize: 16,
    textAlign: 'center',
    marginTop: 28,
  },
  inputContainer: {
    marginBottom: 10,
  },
  textInput: {
    borderRadius: 10,
    paddingHorizontal: 12,
    borderColor: '#F43F31',
    fontSize: 16,
    color: '#333',
    backgroundColor: '#F4F4F4',
    height: 48,
    margin: 12,
    padding: 10,
  },
  charCount: {
    fontSize: 12,
    color: '#999',
    textAlign: 'right',
    marginTop: 4,
  },
  deleteMessage: {
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
    paddingVertical: 10,
  },
  deleteIconCircle: {
    width: 22,
    height: 22,
  },
  btnStyle: {
    backgroundColor: '#12AA9C',
    borderRadius: 23,
    paddingTop: 12,
    paddingBottom: 12,
    width: (newWidth - 72) / 2,
  },
  btnCancelStyle: {
    background: 'rgba(0,0,0,0.04)',
    borderRadius: 23,
    paddingTop: 12,
    paddingBottom: 12,
    width: (newWidth - 72) / 2,
  },
  inputValueError: {
    color: 'red',
  },
});

export default CommonAngles;
