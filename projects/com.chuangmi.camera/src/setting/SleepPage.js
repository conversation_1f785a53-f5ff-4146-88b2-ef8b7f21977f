import React from 'react';

import {StyleSheet, View, ScrollView, Dimensions, Text} from 'react-native';

import {imiThemeManager} from '../../../../imilab-design-ui';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import ListItem from '../../../../imilab-design-ui/src/widgets/settingUI/ListItem';
import ListItmeWithSwitch from '../../../../imilab-design-ui/src/widgets/settingUI/ListItmeWithSwitch';
import Separator from '../../../../imilab-design-ui/src/widgets/settingUI/Separator';
import I18n from '../../../../globalization/Localize';
import BaseDeviceComponent from '../../../../imilab-rn-sdk/components/BaseDeviceComponent';

import {LetDevice, IMIStorage} from '../../../../imilab-rn-sdk';

import {showLoading, showToast} from '../../../../imilab-design-ui';
import IMIGotoPage from '../../../../imilab-rn-sdk/native/local-kit/IMIGotoPage';

import {stringsTo} from '../../../../globalization/Localize';
import Orientation from 'react-native-orientation';
import DeviceTemplatesUtils from '../../../../imilab-rn-sdk/core/device/DeviceTemplatesUtils';
import IMILogUtil from '../../../../imilab-rn-sdk/native/local-kit/IMILogUtil';
/**
 * 摄像机功能设置页面
 */

export default class SleepPage extends BaseDeviceComponent {
  static propTypes = {};

  componentDidMount() {
    /*
        /*LetDevice.getPropertyCloud('DayNightMode').then(function (data) {
            console.log('getPropertyCloud' + data);
        }).catch(error => {
            console.log(JSON.stringify(error))
        });*/
    Orientation.lockToPortrait();

    this._subscribe_focus = this.props.navigation.addListener('focus', () => {
      this.getNewData();
    });
  }

  getNewData() {
    showLoading(stringsTo('commWaitText'), true);
    Promise.all([LetDevice.getSingleProperty('10001'), LetDevice.getSingleProperty('10008')])
      .then(data => {
        console.log('dataObject', data);
        const stateProps = {};
        data?.forEach(item => {
          // console.log(item);

          //休眠 true 代表不是休眠，false代表休眠
          if (`${item?.thingId}` === `${10001}`) {
            stateProps.sleepStatusSwitch = item?.value?.value;
          }
          //固定位置休眠
          if (`${item?.thingId}` === `${10008}`) {
            console.log('nightFunctionValue ', item);
            stateProps.sleepPosition = item?.value?.value;
          }
        });
        this.setState(stateProps);
        showLoading(stringsTo('commWaitText'), false);
      })
      .catch(error => {
        console.log('error1', JSON.stringify(error));
        showLoading(stringsTo('commWaitText'), false);
      });
    /* LetDevice.updateAllPropertyCloud()
      .then(data => {
        console.log('updateAllPropertyCloud:', JSON.parse(data));
        let dataPackage = JSON.parse(data);
        let stateProps = {};
        //TODO 镜头畸变校正固件端未实现
        if (dataPackage.SleepStatus) {
          stateProps.sleepStatusSwitch = !dataPackage.SleepStatus.value;
        }
        if (dataPackage.SleepPosition) {
          //056需要保存位置,关闭固定休眠时

          if (dataPackage.SleepPosition.value == 'off' || dataPackage.SleepPosition.value == '') {
            stateProps.sleepPosition = false;
          } else {
            stateProps.sleepPosition = true;
          }
        }
        console.log('物模型云端数据--------', stateProps);
        this.setState(stateProps);
      })
      .catch(error => {
        console.log(JSON.stringify(error));
      }); */
  }

  constructor(props, context) {
    super(props, context);
    this.state = {
      sleepStatusSwitch: true,
      sleepPosition: false,
    };
  }

  componentWillUnmount() {
    this._subscribe_focus && this._subscribe_focus();
  }

  render() {
    let {showPresetSleep, showSynchronous056} = DeviceTemplatesUtils.getConfigProject('camera', LetDevice.model);
    return (
      <View style={styles.container}>
        <NavigationBar
          title={stringsTo('sleep_set')}
          left={[
            {
              key: NavigationBar.ICON.BACK,
              onPress: () => this.props.navigation.pop(),
              accessibilityLabel: 'dormancy_go_back',
            },
          ]}
          right={[]}
        />
        <ScrollView showsVerticalScrollIndicator={false} style={{backgroundColor: '#F1F1F1'}}>


          {/*    <View style={{height:14,backgroundColor: "#F1F1F1"}} />*/}
          <ListItmeWithSwitch
            title={stringsTo('sleep_title')}
            value={!this.state.sleepStatusSwitch}
            onValueChange={value => {
              showLoading(stringsTo('commWaitText'), true);
              let params = {msg_id: '10001', value: !value};
              let paramJson = JSON.stringify(params);
              LetDevice.setProperties(true, LetDevice.deviceID, '10001', paramJson)
                .then(res => {
                  this.setState({sleepStatusSwitch: !value}, () => {
                    showLoading(false);
                  });
                })
                .catch(e => {
                  console.log('error：', JSON.stringify(e), paramJson, LetDevice.deviceID);

                  showLoading(false);
                  this.setState({
                    sleepStatusSwitch: value,
                  });

                  showToast(I18n.t('operationFailed'));
                });
              /* if (!value) {
                showLoading(stringsTo('commWaitText'), true);
                let params = {msg_id: '10033', value};
                let paramJson = JSON.stringify(params);
                LetDevice.setProperties(true, LetDevice.deviceID, '10033', paramJson)
                  .then(res => {
                    this.setState({sleepStatusSwitch: value}, () => {
                      showLoading(false);
                    });
                  })
                  .catch(() => {
                    showLoading(false);
                    this.setState({
                      sleepStatusSwitch: !value,
                    });
                    showToast(I18n.t('operationFailed'));
                  });
              } else {
                showLoading(stringsTo('commWaitText'), true);
                let params = {msg_id: 10033, value};
                let paramJson = JSON.stringify(params);
                LetDevice.setProperties(true, LetDevice.deviceID, 10033, paramJson)
                  .then(res => {
                    this.setState({sleepStatusSwitch: value}, () => {
                      showLoading(false);
                    });
                  })
                  .catch(err => {
                    showLoading(false);
                    this.setState({
                      sleepStatusSwitch: !value,
                    });
                    showToast(I18n.t('operationFailed'));
                  });
              } */
            }}
            accessibilityLabel={['dormancy_off', 'dormancy_on']}
          />

          {/*<Text*/}
          {/*  style={{*/}
          {/*    color: '#00000080',*/}
          {/*    fontSize: 12,*/}
          {/*    paddingLeft: 15,*/}
          {/*    paddingTop: 5,*/}
          {/*    width: '100%',*/}
          {/*  }}>*/}
          {/*  {'固定画面'}*/}
          {/*</Text>*/}
          {/*<ListItmeWithSwitch*/}
          {/*  title={stringsTo('sleep_title')}*/}
          {/*  value={!this.state.sleepStatusSwitch}*/}
          {/*  onValueChange={value => {*/}
          {/*    showLoading(stringsTo('commWaitText'), true);*/}
          {/*    let params = {msg_id: '10001', value: !value};*/}
          {/*    let paramJson = JSON.stringify(params);*/}
          {/*    // LetDevice.setProperties(true, LetDevice.deviceID, '10001', paramJson)*/}
          {/*    //   .then(res => {*/}
          {/*    //     this.setState({sleepStatusSwitch: !value}, () => {*/}
          {/*    //       showLoading(false);*/}
          {/*    //     });*/}
          {/*    //   })*/}
          {/*    //   .catch(e => {*/}
          {/*    //     console.log('error：', JSON.stringify(e), paramJson, LetDevice.deviceID);*/}
          {/*    //*/}
          {/*    //     showLoading(false);*/}
          {/*    //     this.setState({*/}
          {/*    //       sleepStatusSwitch: value,*/}
          {/*    //     });*/}
          {/*    //*/}
          {/*    //     showToast(I18n.t('operationFailed'));*/}
          {/*    //   });*/}
          {/*  }}*/}
          {/*  accessibilityLabel={['dormancy_off', 'dormancy_on']}*/}
          {/*/>*/}

          {/*<Text*/}
          {/*  style={{*/}
          {/*    color: '#00000080',*/}
          {/*    fontSize: 12,*/}
          {/*    paddingLeft: 15,*/}
          {/*    paddingTop: 5,*/}
          {/*    width: '100%',*/}
          {/*    backgroundColor: '#ffffff',*/}
          {/*  }}>*/}
          {/*  {'云台画面'}*/}
          {/*</Text>*/}
          
          {/*<ListItmeWithSwitch*/}
          {/*  title={stringsTo('sleep_title')}*/}
          {/*  value={!this.state.sleepStatusSwitch}*/}
          {/*  onValueChange={value => {*/}
          {/*    showLoading(stringsTo('commWaitText'), true);*/}
          {/*    let params = {msg_id: '10001', value: !value};*/}
          {/*    let paramJson = JSON.stringify(params);*/}
          {/*    // LetDevice.setProperties(true, LetDevice.deviceID, '10001', paramJson)*/}
          {/*    //   .then(res => {*/}
          {/*    //     this.setState({sleepStatusSwitch: !value}, () => {*/}
          {/*    //       showLoading(false);*/}
          {/*    //     });*/}
          {/*    //   })*/}
          {/*    //   .catch(e => {*/}
          {/*    //     console.log('error：', JSON.stringify(e), paramJson, LetDevice.deviceID);*/}
          {/*    //*/}
          {/*    //     showLoading(false);*/}
          {/*    //     this.setState({*/}
          {/*    //       sleepStatusSwitch: value,*/}
          {/*    //     });*/}
          {/*    //*/}
          {/*    //     showToast(I18n.t('operationFailed'));*/}
          {/*    //   });*/}
          {/*  }}*/}
          {/*  accessibilityLabel={['dormancy_off', 'dormancy_on']}*/}
          {/*/>*/}


          {/*    <View style={{height:14,backgroundColor: "#F1F1F1"}} />*/}
          {/*showPresetSleep 写死 记得打开 TODO*/}
          {true ? (
            <ListItem
              title={stringsTo('presetAngleSleep')}
              subtitle={stringsTo('Preset_Sleep_subTitle2')}
              value={this.state.sleepPosition ? stringsTo('preset_opened') : stringsTo('preset_closed')}
              onPress={() => {
                if (!this.state.sleepStatusSwitch) {
                  showToast(I18n.t('power_off'));
                } else {
                  /*  this.props.navigation.push(
                    showSynchronous056 || LetDevice.model == 'a1QRbHvcYBd'
                      ? 'PositionSleepNewPage'
                      : 'PositionSleepPage',
                  ); */
                  this.props.navigation.push('PositionSleepPage');
                }
              }}
              accessibilityLabel={'dormancy_fixed'}
            />
          ) : null}
          {/*   <ListItem
            title={stringsTo('timer_sleep_title')}
            onPress={() => {
              IMIGotoPage.startSleepTimerPage(LetDevice.deviceID);
            }}
            accessibilityLabel={'dormancy_timing'}
          /> */}
          {/*   <View style={{height:14,backgroundColor: "#F1F1F1"}} />*/}
        </ScrollView>
      </View>
    );
  }

  _assignRoot = component => {
    this.cameraGLView = component;
  };

  _onPrepared(data) {
    console.log(`_onPrepared code : ${data}  `);
    this.cameraGLView.start();
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: imiThemeManager.theme.pageBg,
  },
  functionSettingStyle: {
    color: '#0000007F',
    fontSize: 12,
    marginTop: 23,
    marginLeft: 14,
  },
});
