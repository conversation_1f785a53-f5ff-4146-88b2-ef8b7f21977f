import React, { version } from 'react';

import {StyleSheet, View} from 'react-native';

import {imiThemeManager} from '../../../../imilab-design-ui';
import ListItem from '../../../../imilab-design-ui/src/widgets/settingUI/ListItem';
import I18n from '../../../../globalization/Localize';
import BaseDeviceComponent from '../../../../imilab-rn-sdk/components/BaseDeviceComponent';
import {stringsTo} from '../../../../globalization/Localize';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import DeviceTemplatesUtils from '../../../../imilab-rn-sdk/core/device/DeviceTemplatesUtils';
import {LetDevice} from '../../../../imilab-rn-sdk';

export default class MoreSetting extends BaseDeviceComponent {
  static propTypes = {};

  constructor(props) {
    super(props);
    this.state = {
      version: DeviceTemplatesUtils.getConfigProject('camera', LetDevice.model).versionCode
    };
  }

  componentWillUnmount() {
  }

  componentDidMount() {
  }
  render() {
    return (
      <View style={styles.container}>
        <NavigationBar
          title={I18n.t('angelMoreSetting')}
          left={[
            {
              key: NavigationBar.ICON.BACK,
              onPress: () => {
                this.props.navigation.canGoBack() ? this.props.navigation.goBack() : IMIGotoPage.exit();
              },
              accessibilityLabel: 'more_setting_back',
            },
          ]}
          right={[]}
        />
        <ListItem
          hideArrow={true}
          title={stringsTo('rn_version')}
          value={this.state.version}
          accessibilityLabel={'setting_camera'}
          />
          {!LetDevice.isShareUser && <ListItem
          title={stringsTo('network_info')}
          accessibilityLabel={'setting_camera'}
          onPress={() => {
            this.props.navigation.push('NetInfo');
          }}
          />}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: imiThemeManager.theme.pageBg,
  }
});
