/* eslint-disable react-hooks/exhaustive-deps */
import {Text, View, StyleSheet, ScrollView, TouchableOpacity, Dimensions} from 'react-native';
import React, {useEffect, useState, useRef} from 'react';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import {IMIGotoPage, IMIStorage, LetDevice} from '../../../../imilab-rn-sdk';
import {showLoading, showToast} from '../../../../imilab-design-ui';
import ListItmeWithSwitch from '../../../../imilab-design-ui/src/widgets/settingUI/ListItmeWithSwitch';
import ListItem from '../../../../imilab-design-ui/src/widgets/settingUI/ListItem';
import MessageDialog from '../../../../imilab-design-ui/src/widgets/settingUI/MessageDialog';
import I18n, {stringsTo} from '../../../../globalization/Localize';
import CustomSpinner from './component/CustomSpinner';
import ChoiceItemNew from '../../../../imilab-design-ui/src/widgets/settingUI/ChoiceItemNew';

const AddFrequentlyLocationPhotoKey = LetDevice.deviceID + 'addFrequentlyLocationPhoto';
// const newWidth = Math.min(Dimensions.get('window').width, Dimensions.get('window').height);

const CruiseControlSetting = props => {
  const [cruiseEnabled, setCruiseEnabled] = useState(false);
  const [cruiseFrequency, setCruiseFrequency] = useState('10');
  const [cruiseMode, setCruiseMode] = useState(0);
  const [showCustomDialog, setShowCustomDialog] = useState(false);
  const [showFrequencyDialog, setShowFrequencyDialog] = useState(false);

  const [customHour, setCustomHour] = useState('00');
  const [customMinute, setCustomMinute] = useState('01');
  const [cruiseStartTime, setCruiseStartTime] = useState('');
  const [cruiseEndTime, setCruiseEndTime] = useState('');
  const [commonAnglesValue, setCommonAnglesValue] = useState('0');
  // const [frequentlyLocationPhotoList, setFrequentlyLocationPhotoList] = useState([]);
  //常用角度小于2个的数据
  const [commonAnglesDialog, setCommonAnglesDialog] = useState(false);
  // 添加refs用于保存选择器的值，避免滑动不灵敏问题
  // const hourRef = useRef('00');
  // const minuteRef = useRef('10');

  // 小时和分钟数据源
  const hourArr = (() => {
    const list = [];
    for (let i = 0; i < 24; i++) {
      list.push(i < 10 ? '0' + i : i);
    }
    return list;
  })();
  const minuteArr = (() => {
    const list = [];
    for (let i = 1; i < 60; i++) {
      list.push(i < 10 ? '0' + i : i);
    }
    return list;
  })();

  // 巡航频率选项
  const frequencyOptions = [
    {key: 'freq_10min', label: stringsTo('every_10_minutes'), value: '10'},
    {key: 'freq_30min', label: stringsTo('every_30_minutes'), value: '30'},
    {key: 'freq_1hour', label: stringsTo('every_60_minutes'), value: '60'},
    {key: 'freq_2hour', label: stringsTo('every_120_minutes'), value: '120'},
    {key: 'freq_24hour', label: stringsTo('every_720_minutes'), value: '720'},
    {key: 'freq_custom', label: stringsTo('do_custom'), value: '自定义'},
  ];

  // 巡航模式选项
  const modeOptions = [
    {label: stringsTo('every_360_degrees'), value: 0, subtitle: stringsTo('every_360_degrees_subtitle')},
    {label: stringsTo('fixed_point_cruise'), value: 1, subtitle: stringsTo('fixed_point_cruise_subtitle')},
  ];

  // 处理自动巡航开关
  const handleCruiseToggle = value => {
    setCruiseEnabled(value);
    const paramJson = JSON.stringify({msg_id: '10032', value});
    console.log('下发参数', paramJson);
    showLoading(stringsTo('commWaitText'), true);
    LetDevice.setProperties(true, LetDevice.deviceID, '10032', paramJson)
      .then(() => {})
      .catch(() => {
        showToast(stringsTo('operationFailed'));
        setCruiseEnabled(!value);
      })
      .finally(() => {
        showLoading(stringsTo('commWaitText'), false);
      });
  };

  // 处理巡航频率选择
  const handleFrequencySelect = option => {
    setShowFrequencyDialog(false);
    if (option.key === 'freq_custom') {
      // 打开自定义对话框前，确保hourRef和minuteRef与当前状态同步

      setShowCustomDialog(true);
    } else {
      const paramJson = JSON.stringify({msg_id: '10033', value: option.value * 1});
      console.log('下发参数', paramJson);
      LetDevice.setProperties(true, LetDevice.deviceID, '10033', paramJson)
        .then(() => {
          const hour = Math.floor(option.value / 60);
          const minute = option.value % 60;
          setCustomHour(hour < 10 ? `0${hour}` : `${hour}`);
          setCustomMinute(minute < 10 ? `0${minute}` : `${minute}`);
          setCruiseFrequency(option.value);
        })
        .catch(() => {
          setCruiseFrequency(cruiseFrequency);
          showToast(stringsTo('operationFailed'));
        });
    }
  };
  const handleFrequencySelectClick = (option, checked) => {
    if (option.key === 'freq_custom' && checked) {
      // hourRef.current = customHour;
      // minuteRef.current = customMinute;

      setShowCustomDialog(true);
    }
  };
  // 处理自定义时间确认
  const handleCustomConfirm = () => {
    // 使用ref中的值，确保获取最新的选择
    const hour = customHour;
    const minute = customMinute;

    let timeText = '';
    if (parseInt(hour, 10) > 0) {
      timeText = parseInt(hour, 10) * 60;
      if (parseInt(minute, 10) > 0) {
        timeText += parseInt(minute, 10);
      }
    } else {
      timeText = parseInt(minute, 10) || 1;
    }
    if (parseInt(minute, 10) < 5 && parseInt(hour, 10) < 1) {
      showToast(stringsTo('cruise_min_interval'));
      setShowCustomDialog(false);
      return;
    }
    setShowCustomDialog(false);
    const paramJson = JSON.stringify({msg_id: '10033', value: timeText});
    console.log('下发参数', paramJson);
    LetDevice.setProperties(true, LetDevice.deviceID, '10033', paramJson)
      .then(() => {
        // setCustomHour(hour);
        // setCustomMinute(minute);
        setCruiseFrequency(timeText);
      })
      .catch(() => {
        // setCustomHour(customHour);
        // setCustomMinute(customMinute);
        setCruiseFrequency(cruiseFrequency);
        showToast(stringsTo('operationFailed'));
      });
  };

  // 处理巡航模式选择
  const handleModeSelect = option => {
    const initCruiseMode = cruiseMode;
    if (option.value === 1 && commonAnglesValue?.length < 2) {
      setCruiseMode(option.value);
      setCommonAnglesDialog(true);
      return;
    }
    setCruiseMode(option.value);

    const params = JSON.stringify({
      msg_id: '10037',
      value: option.value,
    });
    showLoading(stringsTo('commWaitText'), true);
    // 巡航模式
    LetDevice.setProperties(true, LetDevice.deviceID, '10037', params)
      .then(data => {
        if (option.value === 1) {
          const posIdxList = commonAnglesValue.map(item => item.idx);
          const params1 = JSON.stringify({
            msg_id: '10038',
            value: JSON.stringify(posIdxList),
          });
          LetDevice.setProperties(true, LetDevice.deviceID, '10038', params1);
        }
      })
      .catch(() => {
        showToast(stringsTo('operationFailed'));
        setCruiseMode(initCruiseMode);
      })
      .finally(() => {
        showLoading(stringsTo('commWaitText'), false);
      });
  };
  const goCommonAngles = () => {
    props.navigation.push('CommonAngles');
  };
  // 获取频率显示文本
  const getFrequencyDisplayText = () => {
    if (!cruiseFrequency) {
      return '';
    }
    const option = frequencyOptions.find(opt => `${opt.value}` === `${cruiseFrequency}`);

    if (option) {
      return option.label;
    }
    // 把分钟转成小时
    const cruiseFrequencyTime = parseInt(cruiseFrequency, 10);
    const minute = cruiseFrequencyTime % 60;
    const hour = Math.floor(cruiseFrequencyTime / 60);
    if (hour > 0) {
      return stringsTo('every_hours_every_minutes', {hour, minute});
    }
    return stringsTo('every_minutes', {minute});
  };

  // 处理小时选择变化
  const handleHourChange = value => {
    // hourRef.current = value;
    setCustomHour(value);
  };

  // 处理分钟选择变化
  const handleMinuteChange = value => {
    // minuteRef.current = value;
    setCustomMinute(value);
  };

  // 处理频率项点击
  const handleFrequencyItemClick = () => {
    setShowFrequencyDialog(true);
  };
  const getCruiseSpec = () => {
    // 巡航开关
    LetDevice.getSingleProperty('10032')
      .then(data => {
        //0休眠 1关闭
        console.log('巡航开关--------setCruiseEnabled' + JSON.stringify(data));
        if (data?.value?.code == 0) {
          setCruiseEnabled(data.value.value);
        }
      })
      .catch(e => {
        console.log('error111111', e);
      });
    // 巡航周期
    LetDevice.getSingleProperty('10033').then(data => {
      console.log(' 巡航周期--------setCruiseFrequency' + JSON.stringify(data));
      if (data?.value?.code == 0) {
        const hour = Math.floor(data.value.value / 60);
        const minute = data.value.value % 60;
        setCustomHour(hour < 10 ? `0${hour}` : `${hour}`);
        setCustomMinute(minute < 10 ? `0${minute}` : `${minute}`);
        setCruiseFrequency(data.value.value);
      }
    });
    // 巡航模式
    LetDevice.getSingleProperty('10037').then(data => {
      console.log('  巡航模式--------setCruiseMode' + JSON.stringify(data));
      if (data?.value?.code == 0) {
        setCruiseMode(data.value.value || 0); // 默认是0
      }
    });
    // 巡航开始时间
    LetDevice.getSingleProperty('10034').then(data => {
      console.log(' 巡航开始时间--------setCruiseTime' + JSON.stringify(data));
      if (data?.value?.code == 0) {
        console.log(' 巡航开始时间--------setCruiseTime' + data.value.value);

        setCruiseStartTime(data.value.value ? data.value.value : '00:00'); // 默认是00:00
      }
    });
    // 巡航结束时间
    LetDevice.getSingleProperty('10035').then(data => {
      console.log(' 巡航结束时间--------setCruiseTime' + JSON.stringify(data));
      if (data?.value?.code == 0) {
        setCruiseEndTime(data.value.value || '23:59'); // 默认是23:59
      }
    });
    // 常看位置配置
    LetDevice.getSingleProperty('10028')
      .then(data => {
        console.log(' 常看位置配置	--------setCruiseTime' + JSON.stringify(data));
        if (data?.value?.code == 0) {
          setCommonAnglesValue(JSON.parse(data.value.value));
        }
      })
      .catch(error => {
        console.log('error111111', error);
      });
    IMIStorage.load({
      key: AddFrequentlyLocationPhotoKey,
      autoSync: true,
      syncInBackground: true,
    })
      .then(res => {
        if (res) {
          setFrequentlyLocationPhotoList(JSON.parse(res));
        }
      })
      .catch(_ => {
        console.log(222222, _);
      });
  };
  useEffect(() => {
    //回到当前页面
    let focusNav = props.navigation.addListener('focus', () => {
      getCruiseSpec();
    });
    return () => {
      focusNav?.remove && focusNav?.remove();
    };
  }, []);
  const getFrequencyChecked = (value, optionValue) => {
    const list = [10, 30, 60, 120, 720];
    if (list.indexOf(optionValue * 1) !== -1) {
      return value * 1 === optionValue * 1;
    }

    if (list.indexOf(value * 1) !== -1) {
      return false;
    }
    return true;
  };

  const getRadius = (value, checked) => {
    if (value === 0) {
      return 16;
    }
    if (!checked && value === 1) {
      return 16;
    }
    return 0;
  };
  return (
    <View style={styles.container}>
      <NavigationBar
        title={stringsTo('cruise_control')}
        left={[
          {
            key: NavigationBar.ICON.BACK,

            onPress: () => {
              props.navigation.canGoBack() ? props.navigation.goBack() : IMIGotoPage.exit();
            },
            accessibilityLabel: 'cruise_control',
          },
        ]}
        right={[]}
      />

      <ScrollView style={styles.scrollView}>
        {/* 自动巡航开关 */}
        <ListItmeWithSwitch
          title={stringsTo('cruise_control')}
          value={cruiseEnabled}
          onValueChange={handleCruiseToggle}
        />

        {cruiseEnabled && (
          <>
            <View style={styles.lineHightBorder} />
            <View style={styles.titleView}>
              <Text>{stringsTo('cruise_time')}</Text>
            </View>
            {/* 巡航频率设置 */}
            <ListItem
              title={stringsTo('cruise_frequency')}
              value={getFrequencyDisplayText()}
              showArrow={true}
              onPress={handleFrequencyItemClick}
            />

            <ListItem
              title={stringsTo('cruise_time_period')}
              showArrow={true}
              onPress={() => {
                props.navigation.push('CruiseTimePeriod');
              }}
              value={`${cruiseStartTime} -- ${cruiseEndTime}`}
            />
            <View style={styles.lineHightBorder} />
            {/* 巡航模式设置 */}
            <View style={styles.titleView}>
              <Text>{stringsTo('cruise_mode')}</Text>
            </View>

            <View style={styles.choiceItemLabel}>
              {modeOptions.map((option, index) => (
                <View style={styles.choiceItem} key={index}>
                  <ChoiceItemNew
                    title={option.label}
                    subtitle={option.subtitle}
                    backgroundColor={'#F7F7F7'}
                    checked={cruiseMode === option.value}
                    selectIcon={require('../../resources/images/icon_select_choose.png')}
                    unselectIcon={require('../../resources/images/icon_unselect_s.png')}
                    onValueChange={() => handleModeSelect(option)}
                    onlyChecked={true}
                    containerStyle={{
                      paddingTop: 26,
                      paddingBottom: 26,
                      borderRadius: 16,
                      borderBottomLeftRadius: getRadius(option.value, cruiseMode === option.value),
                      borderBottomRightRadius: getRadius(option.value, cruiseMode === option.value),
                    }}
                  />
                  {cruiseMode === 1 && option.value === 1 && (
                    <View style={styles.commonAngleView}>
                      <ListItem
                        title={stringsTo('common_angles')}
                        value={commonAnglesValue?.length || 0}
                        containerStyle={{backgroundColor: '#F7F7F7'}}
                        onPress={() => {
                          goCommonAngles();
                        }}
                        valueStyle={{paddingRight: 20}}
                      />
                    </View>
                  )}
                </View>
              ))}
            </View>
          </>
        )}
      </ScrollView>

      {/* 巡航频率选择弹窗 */}
      <MessageDialog
        visible={showFrequencyDialog}
        title={stringsTo('cruise_frequency')}
        showButton={false}
        canDismiss={true}
        onDismiss={() => setShowFrequencyDialog(false)}>
        <View style={styles.dialogContent}>
          {frequencyOptions.map(option => {
            const checked = getFrequencyChecked(cruiseFrequency, option.value);
            return (
              <ChoiceItemNew
                key={option.key}
                title={option.label}
                backgroundColor={checked ? 'rgba(18,170,156,0.1)' : '#FFFFFF'}
                titleColor={'#000000'}
                selectIcon={require('../../resources/images/icon_select_s.png')}
                unselectIcon={'blank'}
                containerStyle={{paddingTop: 17, paddingBottom: 17, borderRadius: 0}}
                checked={checked}
                onValueChange={() => handleFrequencySelect(option)}
                onPress={() => {
                  handleFrequencySelectClick(option, checked);
                }}
              />
            );
          })}
        </View>
      </MessageDialog>

      {/* 自定义时间选择弹窗 */}
      <MessageDialog
        visible={showCustomDialog}
        title={stringsTo('custom_cruise_frequency')}
        canDismiss={true}
        buttons={[
          {
            text: I18n.t('cancel'),
            callback: _ => {
              setShowCustomDialog(false);
            },
            btnStyle: {
              background: 'rgba(0,0,0,0.04)',
              borderRadius: 23,
              paddingTop: 12,
              paddingBottom: 12,
            },
          },
          {
            text: stringsTo('ok_button'),
            accessibilityLabel: 'okCameraPos',
            callback: _ => handleCustomConfirm(),
            btnStyle: {
              backgroundColor: '#12AA9C',
              borderRadius: 23,
              paddingTop: 12,
              paddingBottom: 12,
            },
          },
        ]}
        onDismiss={() => setShowCustomDialog(false)}>
        <View style={styles.timePickerContainer}>
          <View style={styles.timePickerContent}>
            {/* 小时选择器 */}
            <View style={styles.spinnerContainer}>
              <CustomSpinner
                data={hourArr}
                selectedIndex={hourArr.indexOf(customHour) > 0 ? hourArr.indexOf(customHour) : 0}
                unit={stringsTo('hour')}
                onValueChange={handleHourChange}
                optionsWrapperWidth={100}
                optionHeight={50}
                optionsCount={5}
                highlightBorderColor={'transparent'}
                highlightBorderWidth={1}
                activeItemColor="#12AA9C"
                itemColor="#B4B4B4"
                activeItemFontSize={18}
                itemFontSize={16}
              />
            </View>

            {/* 分钟选择器 */}
            <View style={styles.spinnerContainer}>
              <CustomSpinner
                data={minuteArr}
                selectedIndex={minuteArr.indexOf(customMinute) > 0 ? minuteArr.indexOf(customMinute) : 0}
                unit={stringsTo('minute')}
                onValueChange={handleMinuteChange}
                optionsWrapperWidth={100}
                optionHeight={50}
                optionsCount={5}
                highlightBorderColor={'transparent'}
                highlightBorderWidth={1}
                activeItemColor="#12AA9C"
                itemColor="#B4B4B4"
                activeItemFontSize={18}
                itemFontSize={16}
              />
            </View>
          </View>

          {/* <View style={styles.dialogButtons}>
            <TouchableOpacity
              style={[styles.dialogButton, styles.cancelButton]}
              onPress={() => {
                setShowCustomDialog(false);
              }}>
              <Text style={styles.cancelButtonText}>{stringsTo('cancel')}</Text>
            </TouchableOpacity>
            <TouchableOpacity style={[styles.dialogButton, styles.confirmButton]} onPress={handleCustomConfirm}>
              <Text style={styles.confirmButtonText}>{stringsTo('ok_button')}</Text>
            </TouchableOpacity>
          </View> */}
        </View>
      </MessageDialog>
      {/* 常用角度小于2个 */}
      <MessageDialog
        visible={commonAnglesDialog}
        showTitle={false}
        canDismiss={true}
        buttons={[
          {
            text: I18n.t('cancel'),
            callback: _ => {
               setCruiseMode(0)
              setCommonAnglesDialog(false);
            },
            btnStyle: {
              background: 'rgba(0,0,0,0.04)',
              borderRadius: 23,
              paddingTop: 12,
              paddingBottom: 12,
            },
          },
          {
            text: stringsTo('go_add'),
            accessibilityLabel: 'okCameraPos',
            callback: _ => {
              setCruiseMode(0)
              setCommonAnglesDialog(false);
              props.navigation.push('AddCommonAngles', {anglesName: ''});
            },
            btnStyle: {
              backgroundColor: '#12AA9C',
              borderRadius: 23,
              paddingTop: 12,
              paddingBottom: 12,
            },
          },
        ]}
        onDismiss={() =>{
           setCruiseMode(0)
           setCommonAnglesDialog(false)
        } }>
        <View style={styles.dialogContent}>
          <Text style={styles.anglesLess}>{stringsTo('common_angles_less_than_2')}</Text>
        </View>
      </MessageDialog>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollView: {},
  dialogContent: {
    paddingVertical: 10,
  },
  anglesLess: {
    textAlign: 'center',
    color: 'rgba(0,0,0,0.8)',
    fontSize: 16,
    marginTop: 28,
  },
  dialogChoiceItem: {
    marginHorizontal: 0,
    paddingHorizontal: 20,
  },
  timePickerContainer: {
    paddingVertical: 10,
  },
  timePickerContent: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  spinnerContainer: {
    flex: 1,
    alignItems: 'center',
  },
  dialogButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 20,
    marginTop: 13,
    marginBottom: 28,
  },
  dialogButton: {
    flex: 1,

    alignItems: 'center',
    marginHorizontal: 5,
    background: '#4A70A5',
    borderRadius: 23,
    paddingTop: 12,
    paddingBottom: 12,
  },
  cancelButton: {
    backgroundColor: '#F5F5F5',
  },
  confirmButton: {
    backgroundColor: '#4A70A5',
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#666666',
  },
  confirmButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  lineHightBorder: {
    height: 1,
    backgroundColor: 'rgba(0,0,0,0.15)',
    marginTop: 20,
    marginBottom: 20,
    marginLeft: 14,
    marginRight: 14,
  },
  titleView: {
    fontSize: 12,
    color: '#000000',
    marginLeft: 14,
    marginTop: 7,
    marginBottom: 7,
  },
  choiceItemLabel: {
    marginLeft: 14,
    marginRight: 14,
  },
  choiceItem: {
    marginBottom: 12,
  },
  commonAngleView: {
    backgroundColor: '#F7F7F7',
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
    overflow: 'hidden',
  },
});

export default CruiseControlSetting;
