import {View, StyleSheet, ScrollView, Text, Dimensions} from 'react-native';
import React, {useEffect, useState, useRef} from 'react';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import {IMIGotoPage} from '../../../../imilab-rn-sdk';
import {imiThemeManager, showToast, showLoading} from '../../../../imilab-design-ui';
import ListItmeWithSwitch from '../../../../imilab-design-ui/src/widgets/settingUI/ListItmeWithSwitch';
import ListItem from '../../../../imilab-design-ui/src/widgets/settingUI/ListItem';
import {stringsTo} from '../../../../globalization/Localize';
import {LetDevice} from '../../../../imilab-rn-sdk/native/iot-kit/IMIDevice';
import {CAMERA_CONTROL} from "../../constants/Spec";
import DeviceTemplatesUtils from '../../../../imilab-rn-sdk/core/device/DeviceTemplatesUtils';

// 视图尺寸计算常量 - 完全参考米家SmartFenceSetting.js的实现
const ScreenOptions = Dimensions.get('window');
const viewWidth = ScreenOptions.width - 24 * 2;
const viewHeight = viewWidth * 9 / 16;
// 将直播流播放器分成8*4个矩形块
const itemWidth = viewWidth / 8;
const itemHeight = viewHeight / 4;
const REACT_MARGIN = 3; // 矩形线框到画面边缘的外边距

const VirtualFenceSetting = props => {
  // 获取摄像头类型
  const cameraNumber = DeviceTemplatesUtils.getConfigProject('camera', LetDevice.model).cameraNumber || '1';

  // 状态管理
  const [virtualFenceEnabled, setVirtualFenceEnabled] = useState(false); // 开关UI状态
  const [virtualFenceActualEnabled, setVirtualFenceActualEnabled] = useState(false); // 真实设置状态，用于控制子组件显示
  const [personEnterFence, setPersonEnterFence] = useState(false);
  const [personLeaveFence, setPersonLeaveFence] = useState(false);
  const [showVirtualFence, setShowVirtualFence] = useState(false);
  const [fenceAreaData, setFenceAreaData] = useState(null); //虚拟围栏区域数据 100001
  const [privacyAreaSwitch, setPrivacyAreaSwitch] = useState(false); // 区域隐私功能开关 100006
  const [privacyAreaData, setPrivacyAreaData] = useState(null); // 隐私区域数据 100007

  // 矩形线框的左上角和右下键的x、y坐标轴 - 完全参考米家SmartFenceSetting.js的实现
  const rectDatas = useRef([REACT_MARGIN, REACT_MARGIN, viewWidth - REACT_MARGIN, viewHeight - REACT_MARGIN]);
  const privateRectDatas = useRef([REACT_MARGIN, REACT_MARGIN, viewWidth - REACT_MARGIN, viewHeight - REACT_MARGIN]);

  // 双摄像头支持：为sensor0(球机)和sensor1(枪机)分别存储坐标数据
  const sensor0FenceRectDatas = useRef([REACT_MARGIN, REACT_MARGIN, viewWidth - REACT_MARGIN, viewHeight - REACT_MARGIN]);
  const sensor1FenceRectDatas = useRef([REACT_MARGIN, REACT_MARGIN, viewWidth - REACT_MARGIN, viewHeight - REACT_MARGIN]);
  const sensor0PrivacyRectDatas = useRef([REACT_MARGIN, REACT_MARGIN, viewWidth - REACT_MARGIN, viewHeight - REACT_MARGIN]);
  const sensor1PrivacyRectDatas = useRef([REACT_MARGIN, REACT_MARGIN, viewWidth - REACT_MARGIN, viewHeight - REACT_MARGIN]);

  // 解析新的物模型数据格式，提取pos坐标数据
  const _parseNewAreaDataFormat = (dataValue, type = 0, sensorType = null) => {
    try {
      console.log(`解析新格式数据 - type: ${type}, sensorType: ${sensorType}, dataValue: ${dataValue}`);

      // 解析JSON字符串
      const parsedData = JSON.parse(dataValue);
      console.log(`解析后的JSON数据:`, JSON.stringify(parsedData, null, 2));

      if (cameraNumber === '2') {
        // 双摄像头设备：分别解析sensor0和sensor1的数据
        let sensor0Data, sensor1Data;

        if (type === 0) {
          // 虚拟围栏数据：fence_area
          // 数据结构：fence_area[0].sensor0 和 fence_area[1].sensor1
          sensor0Data = parsedData?.fence_area?.[0]?.sensor0?.[0]?.area?.[0]?.pos;
          sensor1Data = parsedData?.fence_area?.[1]?.sensor1?.[0]?.area?.[0]?.pos;
        } else {
          // 隐私区域数据：privacy_area
          // 数据结构：privacy_area[0].sensor0 和 privacy_area[1].sensor1
          sensor0Data = parsedData?.privacy_area?.[0]?.sensor0?.[0]?.area?.[0]?.pos;
          sensor1Data = parsedData?.privacy_area?.[1]?.sensor1?.[0]?.area?.[0]?.pos;
        }

        // 处理sensor0数据
        if (sensor0Data && Array.isArray(sensor0Data) && sensor0Data.length === 4) {
          const coordsString = `[${sensor0Data[0]},${sensor0Data[1]}],[${sensor0Data[2]},${sensor0Data[3]}]`;
          _parseAreaDataSpecValue(coordsString, type, 0);
          console.log(`解析球机${type === 0 ? '围栏' : '隐私'}区域坐标:`, sensor0Data);
        } else {
          console.log(`球机${type === 0 ? '围栏' : '隐私'}区域数据无效或不存在，使用默认值`);
        }

        // 处理sensor1数据
        if (sensor1Data && Array.isArray(sensor1Data) && sensor1Data.length === 4) {
          const coordsString = `[${sensor1Data[0]},${sensor1Data[1]}],[${sensor1Data[2]},${sensor1Data[3]}]`;
          _parseAreaDataSpecValue(coordsString, type, 1);
          console.log(`解析枪机${type === 0 ? '围栏' : '隐私'}区域坐标:`, sensor1Data);
        } else {
          console.log(`枪机${type === 0 ? '围栏' : '隐私'}区域数据无效或不存在，使用默认值`);
        }

        return sensor0Data || sensor1Data ? 'parsed' : null;
      } else {
        // 单摄像头设备：使用原有逻辑
        let areaData;
        if (type === 0) {
          // 虚拟围栏数据：fence_area
          areaData = parsedData?.fence_area?.[0]?.sensor0?.[0]?.area?.[0]?.pos;
        } else {
          // 隐私区域数据：privacy_area
          areaData = parsedData?.privacy_area?.[0]?.sensor0?.[0]?.area?.[0]?.pos;
        }

        console.log(`提取到的原始数据，type: ${type}, areaData:`, areaData);

        if (areaData && Array.isArray(areaData) && areaData.length === 4) {
          console.log(`提取到的pos数据: [${areaData.join(', ')}]`);

          // 将数组格式转换为字符串格式，供_parseAreaDataSpecValue使用
          const coordsString = `[${areaData[0]},${areaData[1]}],[${areaData[2]},${areaData[3]}]`;
          console.log(`转换为坐标字符串: ${coordsString}`);

          // 调用原有的坐标转换函数
          _parseAreaDataSpecValue(coordsString, type);
          return coordsString;
        } else {
          console.error(`无法提取pos数据，areaData:`, areaData);
          console.error(`areaData类型: ${typeof areaData}, 是否为数组: ${Array.isArray(areaData)}, 长度: ${areaData?.length}`);
          return null;
        }
      }
    } catch (error) {
      console.error(`解析新格式数据失败:`, error);
      return null;
    }
  };

  // 解析Spec协议得出用户框定的线框的左上右下坐标值，并存入rectDatas刷新UI - 完全复制米家SmartFenceSetting.js的实现
  const _parseAreaDataSpecValue = (coordsArrayString, type = 0, sensorType = null) => { // "[0,0],[94,100]" 或 "[17, 17],[83, 83]"
    console.log(`坐标转换输入 - type: ${type}, sensorType: ${sensorType}, coordsArrayString: ${coordsArrayString}`);
    console.log(`视图尺寸 - viewWidth: ${viewWidth}, viewHeight: ${viewHeight}, REACT_MARGIN: ${REACT_MARGIN}`);

    let coordsStringArray = coordsArrayString.replace("],", "]_").split('_');// ["[0,0]","[94,100]"]
    let coordsArray = [...JSON.parse(coordsStringArray[0]), ...JSON.parse(coordsStringArray[1])];
    console.log(`解析后的百分比坐标: [${coordsArray.join(', ')}]`);

    coordsArray = [coordsArray[0] / 100.0 * viewWidth, coordsArray[1] / 100.0 * viewHeight, coordsArray[2] / 100.0 * viewWidth, coordsArray[3] / 100.0 * viewHeight];
    console.log(`转换为像素坐标: [${coordsArray.map(v => v.toFixed(2)).join(', ')}]`);

    // 尝试修正设置spec时转为百分比带来的误差
    coordsArray[0] < REACT_MARGIN ? coordsArray[0] = REACT_MARGIN : null;
    coordsArray[1] < REACT_MARGIN ? coordsArray[1] = REACT_MARGIN : coordsArray[1] - 1.5;
    coordsArray[2] > viewWidth - REACT_MARGIN ? coordsArray[2] = viewWidth - REACT_MARGIN : coordsArray[2] = coordsArray[2] - 2;
    coordsArray[3] > viewHeight - REACT_MARGIN ? coordsArray[3] = viewHeight - REACT_MARGIN : coordsArray[3] = coordsArray[3] - 1.5;

    console.log(`修正后的坐标: [${coordsArray.map(v => v.toFixed(2)).join(', ')}]`);

    // 根据摄像头类型和区域类型存储到对应的rectDatas
    if (cameraNumber === '2' && sensorType !== null) {
      if (type === 0) {
        // 虚拟围栏
        if (sensorType === 0) {
          sensor0FenceRectDatas.current = coordsArray; // 球机围栏区域
        } else if (sensorType === 1) {
          sensor1FenceRectDatas.current = coordsArray; // 枪机围栏区域
        }
      } else {
        // 隐私区域
        if (sensorType === 0) {
          sensor0PrivacyRectDatas.current = coordsArray; // 球机隐私区域
        } else if (sensorType === 1) {
          sensor1PrivacyRectDatas.current = coordsArray; // 枪机隐私区域
        }
      }
    } else {
      // 单摄像头或兼容模式
      type === 0 ? rectDatas.current = coordsArray : privateRectDatas.current = coordsArray;
    }

    console.log(`坐标转换完成 - ${type === 0 ? '虚拟围栏' : '隐私区域'}坐标已更新`);
  };

  // 获取所有虚拟围栏相关的物模型数据
  const loadVirtualFenceSettings = async () => {
    try {
      showLoading(stringsTo('commWaitText'), true);
      console.log('开始获取虚拟围栏设置...');

      // 使用Promise.all等待所有请求完成
      const promises = [
        LetDevice.getSingleProperty(CAMERA_CONTROL.VIRTUAL_FENCE_SWITCH.PIID),
        LetDevice.getSingleProperty(CAMERA_CONTROL.VIRTUAL_FENCE_AREA.PIID),
        LetDevice.getSingleProperty(CAMERA_CONTROL.PEOPLE_FENCE_IN.PIID),
        LetDevice.getSingleProperty(CAMERA_CONTROL.PEOPLE_FENCE_OUT.PIID),
        LetDevice.getSingleProperty(CAMERA_CONTROL.VIRTUAL_FENCE_DISPLAY_SWITCH.PIID),
        LetDevice.getSingleProperty(CAMERA_CONTROL.PRIVATE_AREA_SWITCH.PIID),
        LetDevice.getSingleProperty(CAMERA_CONTROL.PRIVATE_AREA_PARAM.PIID),
      ];

      const results = await Promise.allSettled(promises);

      // 处理虚拟围栏检测开关
      if (results[0].status === 'fulfilled') {
        const data = results[0].value;
        console.log('虚拟围栏检测开关--------' + JSON.stringify(data));
        if (data?.value?.code == 0) {
          const isEnabled = data.value.value || false;
          setVirtualFenceEnabled(isEnabled);
          setVirtualFenceActualEnabled(isEnabled); // 同时设置真实状态
          console.log('虚拟围栏检测开关:', data.value.value);
        } else {
          // 默认值
          setVirtualFenceEnabled(false);
          setVirtualFenceActualEnabled(false);
        }
      } else {
        console.error('获取虚拟围栏检测开关失败:', results[0].reason);
        setVirtualFenceEnabled(false);
        setVirtualFenceActualEnabled(false);
      }

      // 处理虚拟围栏区域数据 (100001)
      if (results[1].status === 'fulfilled') {
        const data = results[1].value;
        console.log('=== 虚拟围栏区域数据 (100001) ===');
        console.log('完整返回数据:', JSON.stringify(data, null, 2));

        if (data?.value?.code == 0 && data.value.value) {
          console.log('虚拟围栏区域数据获取成功');
          console.log('数据内容:', data.value.value);

          // 使用新的解析函数处理嵌套的JSON数据
          const parsedCoords = _parseNewAreaDataFormat(data.value.value, 0);
          if (parsedCoords) {
            setFenceAreaData(parsedCoords);
          } else {
            console.log('解析虚拟围栏数据失败，使用默认值');
            const defaultFenceData = "[25,25],[75,75]";
            setFenceAreaData(defaultFenceData);
            _parseAreaDataSpecValue(defaultFenceData, 0);
          }
        } else {
          console.log('虚拟围栏区域数据获取失败，设置默认值');
          // 设置默认值 [25, 25, 75, 75] 对应 "[25,25],[75,75]"
          const defaultFenceData = "[25,25],[75,75]";
          setFenceAreaData(defaultFenceData);
          _parseAreaDataSpecValue(defaultFenceData, 0);
        }
        console.log('=== 虚拟围栏区域数据处理完成 ===');
      } else {
        console.error('获取虚拟围栏区域数据失败:', results[1].reason);
        // 设置默认值
        const defaultFenceData = "[25,25],[75,75]";
        setFenceAreaData(defaultFenceData);
        _parseAreaDataSpecValue(defaultFenceData, 0);
      }

      // 处理有人进入围栏开关
      if (results[2].status === 'fulfilled') {
        const data = results[2].value;
        console.log('有人进入围栏开关--------' + JSON.stringify(data));
        if (data?.value?.code == 0) {
          const isEnabled = data.value.value || false;
          setPersonEnterFence(isEnabled);
          console.log('有人进入围栏开关:', data.value.value);
        } else {
          // 默认值
          setPersonEnterFence(false);
        }
      } else {
        console.error('获取有人进入围栏开关失败:', results[2].reason);
        setPersonEnterFence(false);
      }

      // 处理有人离开围栏开关
      if (results[3].status === 'fulfilled') {
        const data = results[3].value;
        console.log('有人离开围栏开关--------' + JSON.stringify(data));
        if (data?.value?.code == 0) {
          const isEnabled = data.value.value || false;
          setPersonLeaveFence(isEnabled);
          console.log('有人离开围栏开关:', data.value.value);
        } else {
          // 默认值
          setPersonLeaveFence(false);
        }
      } else {
        console.error('获取有人离开围栏开关失败:', results[3].reason);
        setPersonLeaveFence(false);
      }

      // 处理显示虚拟围栏开关
      if (results[4].status === 'fulfilled') {
        const data = results[4].value;
        console.log('显示虚拟围栏开关--------' + JSON.stringify(data));
        if (data?.value?.code == 0) {
          const isEnabled = data.value.value || false;
          setShowVirtualFence(isEnabled);
          console.log('显示虚拟围栏开关:', data.value.value);
        } else {
          // 默认值
          setShowVirtualFence(false);
        }
      } else {
        console.error('获取显示虚拟围栏开关失败:', results[4].reason);
        setShowVirtualFence(false);
      }

      // 处理区域隐私功能开关 (100006)
      if (results[5].status === 'fulfilled') {
        const data = results[5].value;
        console.log('区域隐私功能开关--------' + JSON.stringify(data));
        if (data?.value?.code == 0) {
          const isEnabled = data.value.value || false;
          setPrivacyAreaSwitch(isEnabled);
          console.log('区域隐私功能开关:', data.value.value);
        } else {
          // 默认值
          setPrivacyAreaSwitch(false);
        }
      } else {
        console.error('获取区域隐私功能开关失败:', results[5].reason);
        setPrivacyAreaSwitch(false);
      }

      // 处理隐私区域数据 (100007)
      if (results[6].status === 'fulfilled') {
        const data = results[6].value;
        console.log('=== 隐私区域数据 (100007) ===');
        console.log('完整返回数据:', JSON.stringify(data, null, 2));

        if (data?.value?.code == 0 && data.value.value) {
          console.log('隐私区域数据获取成功');
          console.log('数据内容:', data.value.value);

          // 使用新的解析函数处理嵌套的JSON数据
          const parsedCoords = _parseNewAreaDataFormat(data.value.value, 1);
          if (parsedCoords) {
            setPrivacyAreaData(parsedCoords);
          } else {
            console.log('解析隐私区域数据失败，使用默认值');
            const defaultPrivacyData = "[17,17],[83,83]";
            setPrivacyAreaData(defaultPrivacyData);
            _parseAreaDataSpecValue(defaultPrivacyData, 1);
          }
        } else {
          console.log('隐私区域数据获取失败，设置默认值');
          // 设置默认值 "[17, 17],[83, 83]"
          const defaultPrivacyData = "[17,17],[83,83]";
          setPrivacyAreaData(defaultPrivacyData);
          _parseAreaDataSpecValue(defaultPrivacyData, 1);
        }
        console.log('=== 隐私区域数据处理完成 ===');
      } else {
        console.error('获取隐私区域数据失败:', results[6].reason);
        // 设置默认值
        const defaultPrivacyData = "[17,17],[83,83]";
        setPrivacyAreaData(defaultPrivacyData);
        _parseAreaDataSpecValue(defaultPrivacyData, 1);
      }

      // 检查是否有请求失败
      const hasFailures = results.some(result => result.status === 'rejected');
      if (hasFailures) {
        console.error('部分虚拟围栏设置获取失败');
        showToast(stringsTo('commLoadingFailText'));
      }

      console.log('虚拟围栏设置加载完成');
    } catch (error) {
      console.error('获取虚拟围栏设置失败:', error);
      showToast(stringsTo('commLoadingFailText'));
    } finally {
      showLoading(false);
    }
  };

  useEffect(() => {
    loadVirtualFenceSettings();
  }, []);


  // 设置物模型属性的通用函数
  const setVirtualFenceProperty = async (propertyId, value, stateSetter, propertyName, currentValue, isMainSwitch = false) => {
    // 立即更新UI状态，给用户即时反馈
    stateSetter(value);

    try {
      showLoading(stringsTo('commWaitText'), true);
      console.log(`设置${propertyName}:`, value);

      const paramJson = JSON.stringify({ msg_id: propertyId, value: value });

      await LetDevice.setProperties(true, LetDevice.deviceID, propertyId, paramJson);

      // 设置成功
      if (isMainSwitch) {
        // 如果是主开关，设置成功后更新真实状态，控制子组件显示
        setVirtualFenceActualEnabled(value);
        // 主开关状态改变时调用回调函数
        if (props.route.params?.callback) {
          props.route.params.callback(value);
        }
      }
      showToast(stringsTo('settings_set_success'));
      console.log(`${propertyName}设置成功:`, value);

    } catch (error) {
      console.error(`设置${propertyName}失败:`, error);
      showToast(stringsTo('operationFailed'));
      // 设置失败时恢复到原来的状态
      stateSetter(currentValue);
      console.log(`${propertyName}设置失败，恢复到原状态:`, currentValue);
    } finally {
      showLoading(false);
    }
  };

  const handleVirtualFenceChange = (value) => {
    setVirtualFenceProperty(
      CAMERA_CONTROL.VIRTUAL_FENCE_SWITCH.PIID,
      value,
      setVirtualFenceEnabled,
      '虚拟围栏检测开关',
      virtualFenceEnabled,
      true // 标记为主开关
    );
  };

  const handlePersonEnterChange = (value) => {
    setVirtualFenceProperty(
      CAMERA_CONTROL.PEOPLE_FENCE_IN.PIID,
      value,
      setPersonEnterFence,
      '有人进入围栏开关',
      personEnterFence
    );
  };

  const handlePersonLeaveChange = (value) => {
    setVirtualFenceProperty(
      CAMERA_CONTROL.PEOPLE_FENCE_OUT.PIID,
      value,
      setPersonLeaveFence,
      '有人离开围栏开关',
      personLeaveFence
    );
  };

  const handleShowVirtualFenceChange = (value) => {
    setVirtualFenceProperty(
      CAMERA_CONTROL.VIRTUAL_FENCE_DISPLAY_SWITCH.PIID,
      value,
      setShowVirtualFence,
      '显示虚拟围栏开关',
      showVirtualFence
    );
  };

  const handleFenceAreaPress = (sensorType = null) => {
    // 跳转到围栏区域设置页面 - 完全参考米家SmartFenceSetting.js的实现
    console.log('跳转到围栏区域设置, sensorType:', sensorType);

    let areaData, privateAreaData;

    if (cameraNumber === '2' && sensorType !== null) {
      // 双摄像头设备：根据sensorType选择对应的数据
      if (sensorType === 0) {
        areaData = [...sensor0FenceRectDatas.current];
        privateAreaData = [...sensor0PrivacyRectDatas.current];
        console.log('传递球机围栏区域坐标:', areaData);
        console.log('传递球机隐私区域坐标:', privateAreaData);
      } else {
        areaData = [...sensor1FenceRectDatas.current];
        privateAreaData = [...sensor1PrivacyRectDatas.current];
        console.log('传递枪机围栏区域坐标:', areaData);
        console.log('传递枪机隐私区域坐标:', privateAreaData);
      }
    } else {
      // 单摄像头设备：使用原有逻辑
      areaData = [...rectDatas.current];
      privateAreaData = [...privateRectDatas.current];
      console.log('传递的围栏区域坐标:', areaData);
      console.log('传递的隐私区域坐标:', privateAreaData);
    }

    console.log('隐私区域开关状态:', privacyAreaSwitch);

    props.navigation.push('VirtualFenceAreaSetting', {
      areaData: areaData, // 使用转换后的围栏区域坐标
      privateAreaData: privateAreaData, // 使用转换后的隐私区域坐标
      privateSwitch: privacyAreaSwitch, // 隐私区域开关状态
      cameraNumber: cameraNumber,
      areaType: sensorType !== null ? sensorType : 0, // 传递区域类型
      callback: (areaDataNew) => _refreshEffectiveMonitorArea([...areaDataNew], sensorType)
    });
  };

  // 通过用户划定的矩形线框的左上和右下角图标，计算出有效区域的矩形框左上和右下角坐标并刷新显示 - 完全复制米家SmartFenceSetting.js的实现
  const _refreshEffectiveMonitorArea = (rectangleCoords, sensorType = null) => {
    console.log('从编辑页面返回的坐标数据:', rectangleCoords, 'sensorType:', sensorType);

    if (cameraNumber === '2' && sensorType !== null) {
      // 双摄像头设备：根据sensorType更新对应的围栏区域数据
      if (sensorType === 0) {
        sensor0FenceRectDatas.current = rectangleCoords; // 更新球机围栏区域
        console.log('更新球机围栏区域坐标:', rectangleCoords);
      } else if (sensorType === 1) {
        sensor1FenceRectDatas.current = rectangleCoords; // 更新枪机围栏区域
        console.log('更新枪机围栏区域坐标:', rectangleCoords);
      }
    } else {
      // 单摄像头设备：使用原有逻辑
      rectDatas.current = rectangleCoords;
      console.log('rectDatas已更新为:', rectDatas.current);
    }
  };

  return (
    <View style={styles.container}>
      <NavigationBar
        title={stringsTo("virtual_Fence")}
        left={[
          {
            key: NavigationBar.ICON.BACK,
            onPress: () => {
              // 返回时调用回调函数，传递虚拟围栏开关状态
              if (props.route.params?.callback) {
                props.route.params.callback(virtualFenceEnabled);
              }
              props.navigation.canGoBack() ? props.navigation.goBack() : IMIGotoPage.exit();
            },
            accessibilityLabel: 'virtual_fence_back',
          },
        ]}
        right={[]}
      />

      <ScrollView style={styles.scrollView}>
        {/* 设置选项区域 */}
        <View style={styles.settingsSection}>
          {/* 虚拟围栏主开关 */}
          <ListItmeWithSwitch
            title={stringsTo('virtual_Fence')}
            value={virtualFenceEnabled}
            onValueChange={handleVirtualFenceChange}
            accessibilityLabel={['virtual_fence_off', 'virtual_fence_on']}
          />
          
          {/* 有人进入围栏 */}
          {virtualFenceActualEnabled && (
            <ListItmeWithSwitch
              title={stringsTo('person_enter_fence')}
              subtitle={stringsTo('person_enter_fence_desc')}
              value={personEnterFence}
              onValueChange={handlePersonEnterChange}
              accessibilityLabel={['person_enter_off', 'person_enter_on']}
            />
          )}

          {/* 有人离开围栏 */}
          {virtualFenceActualEnabled && (
            <ListItmeWithSwitch
              title={stringsTo('person_leave_fence')}
              subtitle={stringsTo('person_leave_fence_desc')}
              value={personLeaveFence}
              onValueChange={handlePersonLeaveChange}
              accessibilityLabel={['person_leave_off', 'person_leave_on']}
            />
          )}

          {/* 围栏区域设置 */}
          {virtualFenceActualEnabled && (
            cameraNumber === '2' ? (
              // 双摄像头设备：显示两个入口（枪机在前，球机在后）
              <>
                <ListItem
                  title={stringsTo('dual_camera_gun_area')}
                  subtitle={stringsTo('default_gun_area_alarm')}
                  value={stringsTo('go_draw')}
                  onPress={() => handleFenceAreaPress(1)}
                  accessibilityLabel="fence_area_setting_sensor1"
                />
                <ListItem
                  title={stringsTo('dual_camera_ptz_area')}
                  subtitle={stringsTo('default_ptz_area_alarm')}
                  value={stringsTo('go_draw')}
                  onPress={() => handleFenceAreaPress(0)}
                  accessibilityLabel="fence_area_setting_sensor0"
                />
              </>
            ) : (
              // 单摄像头设备：显示原有入口
              <ListItem
                title={stringsTo('fence_area')}
                value={stringsTo('go_draw')}
                onPress={() => handleFenceAreaPress()}
                accessibilityLabel="fence_area_setting"
              />
            )
          )}

          {/* 显示虚拟围栏 */}
          {virtualFenceActualEnabled && (
            <ListItmeWithSwitch
              title={stringsTo('show_virtual_fence')}
              value={showVirtualFence}
              onValueChange={handleShowVirtualFenceChange}
              accessibilityLabel={['show_fence_off', 'show_fence_on']}
            />
          )}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: imiThemeManager.theme.pageBg || '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  settingsSection: {
    marginTop: 20,
  },
});

export default VirtualFenceSetting;
