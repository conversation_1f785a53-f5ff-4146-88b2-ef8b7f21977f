import React from 'react';
import {
  View,
  Text,
  Image,
  ImageBackground,
  Dimensions,
  PanResponder,
  Platform,
  BackHandler,
  PixelRatio,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';

import { Surface, Shape, Path } from '@react-native-community/art';

import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import {IMIGotoPage} from '../../../../imilab-rn-sdk';
import {imiThemeManager, MessageDialog, showToast, showLoading} from '../../../../imilab-design-ui';
import ListItmeWithSwitch from '../../../../imilab-design-ui/src/widgets/settingUI/ListItmeWithSwitch';
import ListItem from '../../../../imilab-design-ui/src/widgets/settingUI/ListItem';
import {stringsTo} from '../../../../globalization/Localize';
import {LetDevice} from '../../../../imilab-rn-sdk/native/iot-kit/IMIDevice';

const ScreenOptions = Dimensions.get('window');
const viewWidth = ScreenOptions.width - 24 * 2;
const viewHeight = (viewWidth * 9) / 16;
// 将直播流播放器分成8*4个矩形块
const itemWidth = viewWidth / 8;
const itemHeight = viewHeight / 4;
const REACT_MARGIN = 0; // 矩形线框的到画面边缘的外边距
const CIRCLE_RADIUS = 3; // 矩形线框这个角上实心圆的半径
const DEVIATION_VALUE = 20; // 线框宽度、圆半径、除法取整引起的误差，试用
const VALID_TOUCH_RANGE = 15; // 四个边角的有效触摸范围
const {width: screenWidth, height: screenHeight} = Dimensions.get('screen');
// 使用固定的背景图片和样式选项
const DEFAULT_BG_SOURCE = require("../../resources/images/ai_pic_area.webp");

const options = [
  {
    name: '可爱1',
    source: require("../../resources/images/area_style_lovely2.webp"),
    backgroundColor: 'red',
    type: 3,
    bgSource: DEFAULT_BG_SOURCE,
  },
  {
    name: '可爱2',
    source: require("../../resources/images/area_style_lovely1.webp"),
    type: 2,
     backgroundColor: 'blue',
    bgSource: DEFAULT_BG_SOURCE,
  },
  {
    name: '可爱3',
    source: require("../../resources/images/area_style_pure1.webp"),
    type: 0,
    backgroundColor: 'yellow',
    bgSource: DEFAULT_BG_SOURCE,
  },
  {
    name: '可爱4',
    source: require("../../resources/images/area_style_pure2.webp"),
    type: 1,
     backgroundColor: 'green',
    bgSource: DEFAULT_BG_SOURCE,
  },
];
const TAG = 'PrivacyAreaModifyPage';
export default class PrivacyAreaModifyPage extends React.Component {
  static navigationOptions = navigation => {
    return {headerTransparent: true, header: null};
  };

  constructor(props, context) {
    super(props, context);
    // 默认使用可爱3样式 (type: 0)
    let type = this.props.route?.params?.areaType || 0;
    let index = options.findIndex(item => item.type == type);
    // 如果找不到对应的样式，默认使用可爱3（索引2）
    if (index === -1) {
      index = 2; // 可爱3的索引是2
      type = 0;  // 可爱3的type是0
    }
    this.state = {
      progressing: false,
      showCover: true,
      showBg: true,
      styleType: type,
      canSave: false,
      showSaveDialog: false,
      bgSource: options[index].bgSource||options[index].backgroundColor,
      hidePrivateAreaSwitch: false,
    };
    this.isMoving = false;
    this.timeStamp = Date.now();

    // 获取双摄像头相关参数
    this.cameraNumber = this.props.route?.params?.cameraNumber || '1';
    this.areaType = this.props.route?.params?.areaType || 0;

    // 使用上一级页面传入的真实数据，去掉模拟数据
    this.rectDatas = this.props.route?.params?.areaData || [REACT_MARGIN, REACT_MARGIN, viewWidth - REACT_MARGIN, viewHeight - REACT_MARGIN];
    this.fenceDatas = this.props.route?.params?.fenceData || [REACT_MARGIN, REACT_MARGIN, viewWidth - REACT_MARGIN, viewHeight - REACT_MARGIN];
    this.fenceSwitch = this.props.route?.params?.fenceSwitch || false;

    console.log('PrivacyAreaModifyPage 初始化参数:', {
      cameraNumber: this.cameraNumber,
      areaType: this.areaType,
      rectDatas: this.rectDatas,
      fenceDatas: this.fenceDatas
    });
    // 有效看护区域矩形背景框的左上和右下角坐标
    this.rectBackGround = this.rectBackGround = [
      Math.floor(this.rectDatas[0] / itemWidth) * itemWidth,
      Math.floor(this.rectDatas[1] / itemHeight) * itemHeight,
      Math.ceil(this.rectDatas[2] / itemWidth) * itemWidth,
      Math.ceil(this.rectDatas[3] / itemHeight) * itemHeight,
    ];
    this.distanceData = [0, 0, 0, 0];
    this.touchPosition = 0; // 开始拖拽的点的坐标位或得出的值
    this.existsSettingsImg = false;
    this.panResponder = PanResponder.create({
      onStartShouldSetPanResponder: () => true, // 刚开始的时候
      onMoveShouldSetPanResponder: () => true,
      onShouldBlockNativeResponder: () => true,
      onPanResponderTerminationRequest: () => true, // 不允许其他人抢占。
      onPanResponderGrant: evt => {
        // 按照米家的交互逻辑：拖拽时隐藏背景图和覆盖图
        this.setState({showCover: false, showBg: false});
        let x = evt.nativeEvent.locationX; // 开始触摸点相对于父View的横坐标
        let y = evt.nativeEvent.locationY; // 开始触摸点相对于父View的纵坐标
        this.touchBeginCoordX = x;
        this.touchBeginCoordY = y;

        this.distanceData = [0, 0, 0, 0];
        let smallest = [VALID_TOUCH_RANGE, VALID_TOUCH_RANGE]; // 矩形框四个角的有效触摸范围，x轴和y轴方向均在VALID_TOUCH_RANGE以内
        let positionCoord = [0, 0]; // 用户开始触摸点的x和y轴坐标

        // 触摸点在线框左上角，则设定触摸坐标为[8,4]
        this.distanceData[0] = Math.abs(x - this.rectDatas[0]);
        if (this.distanceData[0] < smallest[0]) {
          // 触摸点在线框左上角坐标的x轴方向的有效范围内
          positionCoord[0] = 8;
        }
        this.distanceData[1] = Math.abs(y - this.rectDatas[1]);
        if (this.distanceData[1] < smallest[1]) {
          // 触摸点在线框左上角坐标y轴方向的有效范围内
          positionCoord[1] = 4;
        }

        // 触摸点在线框右下角，则设定触摸坐标为[2,1]
        this.distanceData[2] = Math.abs(x - this.rectDatas[2]);
        if (this.distanceData[2] < smallest[0]) {
          // 触摸点在线框右下角坐标的x轴方向的有效范围内
          positionCoord[0] = 2;
        }
        this.distanceData[3] = Math.abs(y - this.rectDatas[3]);
        if (this.distanceData[3] < smallest[1]) {
          // 触摸点在线框右下角坐标y轴方向的有效范围内
          positionCoord[1] = 1;
        }
        this.touchPosition = positionCoord[0] | positionCoord[1]; // 通过位或运算得出共有12，3，6，9四个值
      },

      onPanResponderMove: (evt, gestureState) => {
        /* let x = evt.nativeEvent.locationX;
        let y = evt.nativeEvent.locationY; */
        if (Math.abs(gestureState.dx) <= 5 && Math.abs(gestureState.dy) <= 5) {
          console.log('---------------------------没滑动距离');
          return;
        }
        // 通过触摸开始坐标加上横纵方向的位移算出当前坐标位置，可以解决拖动时locationX和locationY跳变问题
        let x = this.touchBeginCoordX + gestureState.dx; // dx 从触摸操作开始时的累计横向位移
        let y = this.touchBeginCoordY + gestureState.dy; // dy 从触摸操作开始时的累计纵向位移
        console.log(
          '===========is moving',
          this.touchBeginCoordX,
          gestureState.dx,
          this.touchBeginCoordY,
          gestureState.dy,
        );
        let pointChange = false;
        switch (this.touchPosition) {
          case 12: {
            // 拖动左上角 触摸点[8,4]

            if (x < this.rectDatas[2] - itemWidth + DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[0] = x < 0 ? 0 : x;
              this.rectBackGround[0] = Math.floor(x / itemWidth) * itemWidth;
            }
            if (y < this.rectDatas[3] - itemHeight + DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[1] = y < 0 ? 0 : y;
              this.rectBackGround[1] = Math.floor(y / itemHeight) * itemHeight;
            }
            break;
          }
          case 6: {
            // 拖动右上角 触摸点[2,4]
            if (x < viewWidth - REACT_MARGIN && x > this.rectDatas[0] + itemWidth - DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[2] = x;
              this.rectBackGround[2] = Math.ceil(x / itemWidth) * itemWidth;
            }
            if (y >= REACT_MARGIN && y < this.rectDatas[3] - itemHeight + DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[1] = y;
              this.rectBackGround[1] = Math.floor(y / itemHeight) * itemHeight;
            }
            break;
          }
          case 3: {
            // 拖动右下角 触摸点[2,1]
            if (x < viewWidth - REACT_MARGIN && x > this.rectDatas[0] + itemWidth - DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[2] = x;
              this.rectBackGround[2] = Math.ceil(x / itemWidth) * itemWidth;
            }
            if (y < viewHeight - REACT_MARGIN && y > this.rectDatas[1] + itemHeight - DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[3] = y;
              this.rectBackGround[3] = Math.ceil(y / itemHeight) * itemHeight;
            }
            break;
          }
          case 9: {
            // 拖动左下角 触摸点[8,1]
            if (x < this.rectDatas[2] - itemWidth + DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[0] = x < 0 ? 0 : x;
              this.rectBackGround[0] = Math.floor(x / itemWidth) * itemWidth;
            }
            if (y < viewHeight - REACT_MARGIN && y > this.rectDatas[1] + itemHeight + DEVIATION_VALUE) {
              pointChange = true;
              this.rectDatas[3] = y;
              this.rectBackGround[3] = Math.ceil(y / itemHeight) * itemHeight;
            }
            break;
          }
        }
        if (pointChange) {
          this.setState({canSave: true});
        }
      },

      onPanResponderRelease: () => {
        console.log('onPanResponderRelease');
        this.cropImage();
      },

      onPanResponderTerminate: () => {
        console.log('onPanResponderTerminate');
      },
    });
  }

  componentDidMount() {
    if (Platform.OS === 'android') {
      BackHandler.addEventListener('hardwareBackPress', this.onBackHandler);
    }

    // 调试信息：打印坐标和绘制区域信息
    console.log('隐私区域编辑页面 - 绘制区域尺寸:', { viewWidth, viewHeight });
    console.log('隐私区域编辑页面 - 隐私区域坐标:', this.rectDatas);
    console.log('隐私区域编辑页面 - 围栏区域坐标:', this.fenceDatas);
    console.log('隐私区域编辑页面 - 围栏开关:', this.fenceSwitch);

    setTimeout(() => {}, 100);
  }

  async cropImage() {
    // 使用固定背景图片，简化截图逻辑
    this.setState({showBg: true, showCover: false});
    return;
  }

  renderTitleBar() {
    // 根据摄像头类型和区域类型显示不同的标题
    let title = stringsTo('area_privacy_edit');
    if (this.cameraNumber === '2') {
      title = this.areaType === 0 ? stringsTo('dual_camera_ptz_area_edit') : stringsTo('dual_camera_gun_area_edit');
    }

    let titleBarContent = {
      title: title,
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: this.state.canSave ? NavigationBar.ICON.CUSTOM : NavigationBar.ICON.BACK,
          n_source: this.state.canSave ? require('../../resources/images/close.png') : undefined,
          p_source: this.state.canSave ? require('../../resources/images/close.png') : undefined,
          onPress: () => {
            if (this.state.canSave) {
              this.setState({showSaveDialog: true});
              return;
            }
            this.props.navigation.goBack();
          },
        },
      ],
      right: this.state.canSave
        ? [
            {
              key: NavigationBar.ICON.COMPLETE,
              onPress: () => {
                this.onSubmit();
              },
            },
          ]
        : [],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500,
      },
    };
    return <NavigationBar {...titleBarContent} />;
  }

  // 点击保存看护区域坐标数据 - 参考VirtualFenceAreaSetting.js的实现
  async onSubmit() {
    console.log('保存隐私区域坐标:', this.rectDatas);
    console.log('摄像头类型:', this.cameraNumber, '区域类型:', this.areaType);

    try {
      // 显示加载状态
      showLoading(stringsTo('commWaitText'), true);

      // 参考VirtualFenceAreaSetting.js：将像素坐标转换为百分比坐标
      let positions = [
        Math.ceil(this.rectDatas[0] / viewWidth * 100),
        Math.ceil(this.rectDatas[1] / viewHeight * 100),
        Math.ceil(this.rectDatas[2] / viewWidth * 100),
        Math.ceil(this.rectDatas[3] / viewHeight * 100)
      ];

      let privacyAreaData;

      if (this.cameraNumber === '2') {
        // 双摄像头设备：需要获取当前数据，然后更新对应的sensor
        console.log('双摄像头设备，获取当前隐私区域数据...');

        try {
          // 获取当前的隐私区域数据
          const currentData = await LetDevice.getSingleProperty('100007');
          console.log('当前隐私区域数据:', JSON.stringify(currentData));

          let existingData = null;
          if (currentData?.value?.code === 0 && currentData.value.value) {
            existingData = JSON.parse(currentData.value.value);
          }

          // 构建双摄像头隐私区域数据格式
          // 数据结构：privacy_area[0].sensor0 和 privacy_area[1].sensor1
          privacyAreaData = {
            "privacy_area": [
              {
                "sensor0": existingData?.privacy_area?.[0]?.sensor0 || [{
                  "area": [{"pos": [0, 0, 100, 100]}],
                  "color": 0
                }]
              },
              {
                "sensor1": existingData?.privacy_area?.[1]?.sensor1 || [{
                  "area": [{"pos": [0, 0, 100, 100]}],
                  "color": 0
                }]
              }
            ]
          };

          // 根据areaType更新对应的sensor数据
          if (this.areaType === 0) {
            // 更新sensor0(球机)数据
            privacyAreaData.privacy_area[0].sensor0[0].area[0].pos = positions;
            console.log('更新球机隐私区域数据:', positions);
          } else if (this.areaType === 1) {
            // 更新sensor1(枪机)数据
            privacyAreaData.privacy_area[1].sensor1[0].area[0].pos = positions;
            console.log('更新枪机隐私区域数据:', positions);
          }

        } catch (error) {
          console.error('获取当前隐私区域数据失败:', error);
          // 如果获取失败，使用默认数据结构
          privacyAreaData = {
            "privacy_area": [
              {
                "sensor0": [{
                  "area": [{"pos": this.areaType === 0 ? positions : [0, 0, 100, 100]}],
                  "color": 0
                }]
              },
              {
                "sensor1": [{
                  "area": [{"pos": this.areaType === 1 ? positions : [0, 0, 100, 100]}],
                  "color": 0
                }]
              }
            ]
          };
        }
      } else {
        // 单摄像头设备：使用原有逻辑
        privacyAreaData = {
          "privacy_area": [{
            "sensor0": [{
              "area": [{
                "pos": positions
              }],
              "color": 0
            }]
          }]
        };
      }

      const valueString = JSON.stringify(privacyAreaData);
      console.log("隐私区域数据:", valueString);

      // 按照物模型格式构建数据
      const paramJson = JSON.stringify({
        msg_id: '100007',
        value: valueString
      });
      console.log("格式化后的物模型数据:", paramJson);

      // 使用物模型方式设置数据
      LetDevice.setProperties(true, LetDevice.deviceID, '100007', paramJson).then((res) => {
        console.log("隐私区域数据设置成功:", JSON.stringify(res));

        // 设置成功后调用回调函数，将编辑后的坐标数据传回PrivacyAreaSetting页面
        const callback = this.props.route?.params?.callback;
        if (callback) {
          callback(this.rectDatas, this.state.styleType, this.state.hidePrivateAreaSwitch);
        }

        showToast(stringsTo('settings_set_success'));
        this.props.navigation.goBack();

      }).catch((err) => {
        console.error("设置隐私区域数据失败:", JSON.stringify(err));
        showToast(stringsTo('operationFailed'));
      }).finally(() => {
        showLoading(false);
      });

    } catch (error) {
      console.error("保存隐私区域数据异常:", error);
      showToast(stringsTo('operationFailed'));
      showLoading(false);
    }
  }

  render() {
    let imageSource = '';

    // 可拖拽线框的绘制路径
    let draggable_rectangle_path = Path()
      .moveTo(this.rectDatas[0], this.rectDatas[1])
      .lineTo(this.rectDatas[2], this.rectDatas[1])
      .lineTo(this.rectDatas[2], this.rectDatas[3])
      .lineTo(this.rectDatas[0], this.rectDatas[3])
      .close();

    // 表示有效看护区域的半透明矩形背景的绘制路径
    let background_path = Path()
      .moveTo(this.rectBackGround[0], this.rectBackGround[1])
      .lineTo(this.rectBackGround[2], this.rectBackGround[1])
      .lineTo(this.rectBackGround[2], this.rectBackGround[3])
      .lineTo(this.rectBackGround[0], this.rectBackGround[3])
      .close();

    let fence_path = Path()
      .moveTo(this.fenceDatas[0], this.fenceDatas[1])
      .lineTo(this.fenceDatas[2], this.fenceDatas[1])
      .lineTo(this.fenceDatas[2], this.fenceDatas[3])
      .lineTo(this.fenceDatas[0], this.fenceDatas[3])
      .close();

    let background_path_all = Path()
      .moveTo(0, 0)
      .lineTo(this.rectDatas[0], 0)
      .lineTo(this.rectDatas[0], viewHeight)
      .lineTo(0, viewHeight)
      .close();

    let background_path_one = Path()
      .moveTo(0, 0)
      .lineTo(this.rectDatas[0], 0)
      .lineTo(this.rectDatas[0], viewHeight)
      .lineTo(0, viewHeight)
      .close();

    let background_path_two = Path()
      .moveTo(this.rectDatas[0], 0)
      .lineTo(viewWidth, 0)
      .lineTo(viewWidth, this.rectDatas[1])
      .lineTo(this.rectDatas[0], this.rectDatas[1])
      .close();

    let background_path_three = Path()
      .moveTo(this.rectDatas[0], this.rectDatas[3])
      .lineTo(viewWidth, this.rectDatas[3])
      .lineTo(viewWidth, viewHeight)
      .lineTo(this.rectDatas[0], viewHeight)
      .close();

    let background_path_four = Path()
      .moveTo(this.rectDatas[2], this.rectDatas[1])
      .lineTo(viewWidth, this.rectDatas[1])
      .lineTo(viewWidth, this.rectDatas[3])
      .lineTo(this.rectDatas[2], this.rectDatas[3])
      .close();

    let top_left_circle = new Path()
      .moveTo(this.rectDatas[0] + CIRCLE_RADIUS, this.rectDatas[1])
      .arc(0, CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .arc(0, -CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .close();

    let top_right_circle = new Path()
      .moveTo(this.rectDatas[2] + CIRCLE_RADIUS, this.rectDatas[1])
      .arc(0, CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .arc(0, -CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .close();

    let bottom_right_circle = new Path()
      .moveTo(this.rectDatas[2] + CIRCLE_RADIUS, this.rectDatas[3] + 2 * CIRCLE_RADIUS)
      .arc(0, -CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .arc(0, CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .close();
    let bottom_left_circle = new Path()
      .moveTo(this.rectDatas[0] + CIRCLE_RADIUS, this.rectDatas[3] + 2 * CIRCLE_RADIUS)
      .arc(0, -CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .arc(0, CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .close();
    let cropWidth = Math.abs(this.rectDatas[2] - this.rectDatas[0]);
    let cropHeight = Math.abs(this.rectDatas[3] - this.rectDatas[1]);

    // TODO 需要替换ImageBackground图片
    return (
      <View
        style={{
          display: 'flex',
          height: '100%',
          width: '100%',
          flex: 1,
          flexDirection: 'column',
          backgroundColor: '#FFFFFF',
        }}>
        {this.renderTitleBar()}
        <Text
          style={{
            fontSize: 12,
            color: '#666666',
            marginLeft: 27,
            marginTop: 28,
          }}>
          {'隐私区域绘制'}
        </Text>
        <View {...this.panResponder.panHandlers}>
          <ImageBackground
            style={{
              width: viewWidth,
              height: viewHeight,
              marginHorizontal: 24,
              marginTop: 13,
              marginBottom: 20,
            }}
            imageStyle={{borderRadius: 0}}>
            {/* 静态状态：显示可爱3背景图；拖拽状态：显示固定图 */}
            <Image
              style={{
                width: viewWidth,
                height: viewHeight,
              }}
              resizeMode={'cover'} // 统一使用 cover 模式
              source={this.state.showBg
                ? (options.find(item => item.type === 0)?.source || require("../../resources/images/area_style_pure1.webp")) // 可爱3背景
                : require("../../resources/images/ai_pic_area.webp") // 拖拽时显示固定图
              }
            />

            {/* 静态状态：隐私区域内显示固定图 - 显示全屏图片的对应部分 */}
            {this.state.showBg ? (
              <View
                style={{
                  position: 'absolute',
                  width: cropWidth,
                  height: cropHeight,
                  left: this.rectDatas[0],
                  top: this.rectDatas[1],
                  overflow: 'hidden',
                }}>
                <Image
                  source={require("../../resources/images/ai_pic_area.webp")}
                  style={{
                    width: viewWidth, // 使用全屏宽度
                    height: viewHeight, // 使用全屏高度
                    left: -this.rectDatas[0], // 偏移到正确位置
                    top: -this.rectDatas[1], // 偏移到正确位置
                    position: 'absolute',
                  }}
                  resizeMode={'cover'}
                />
              </View>
            ) : null}

            <View style={{position: 'absolute'}}>
              <Surface width={viewWidth} height={viewHeight}>
                {/* <Shape d={background_path} fill="#32BAC0" opacity="0.3" /> */}
                {!this.state.showBg ? <Shape d={background_path_one} fill="#000000" opacity="0.5" /> : null}
                {!this.state.showBg ? <Shape d={background_path_two} fill="#000000" opacity="0.5" /> : null}
                {!this.state.showBg ? <Shape d={background_path_three} fill="#000000" opacity="0.5" /> : null}
                {!this.state.showBg ? <Shape d={background_path_four} fill="#000000" opacity="0.5" /> : null}
                <Shape d={draggable_rectangle_path} fill="#32BAC0" opacity="0" />
                <Shape d={draggable_rectangle_path} stroke="#32BAC0" strokeWidth={1} />
                {this.fenceSwitch ? (
                  <Shape d={fence_path} stroke="#32BAC0" strokeDash={[15, 10]} strokeWidth={1} />
                ) : null}
              </Surface>
            </View>
          </ImageBackground>
          <View
            style={{
              width: viewWidth + 6,
              height: viewHeight + 6,
              position: 'absolute',
              marginHorizontal: 21,
              marginTop: 10,
              marginBottom: 20,
            }}
            imageStyle={{borderRadius: 0}}>
            <Surface width={viewWidth + 6} height={viewHeight + 6}>
              <Shape d={top_left_circle} fill="#32BAC0" />
              <Shape d={top_right_circle} fill="#32BAC0" />
              <Shape d={bottom_right_circle} fill="#32BAC0" />
              <Shape d={bottom_left_circle} fill="#32BAC0" />
            </Surface>
          </View>
        </View>
        {/*<ListItemWithSwitch*/}
        {/*  title={LocalizedStrings['area_privacy_hide']}*/}
        {/*  value={this.state.hidePrivateAreaSwitch}*/}
        {/*  showSeparator={false}*/}
        {/*  titleStyle={{ fontWeight: 'bold' }}*/}
        {/*  onValueChange={(value) => this._onHideAreaChange(value)}*/}
        {/*/>*/}



        {/* <LoadingDialog
          visible={this.state.progressing}
          message={LocalizedStrings.c_setting}
          onModalHide={() => this.setState({progressing: false})}
        /> */}
        {this._renderBackDialog()}
      </View>
    );
  }





  _renderBackDialog() {
    return (
      <MessageDialog
        visible={this.state.showSaveDialog}
        message={'退出后，当前修改将不会保存，确定要退出吗？'}
        messageStyle={{textAlign: 'center'}}
        canDismiss={false}
        buttons={[
          {
            text: '取消',
            callback: _ => {
              this.setState({showSaveDialog: false});
            },
          },
          {
            text: '退出',
            callback: _ => {
              this.setState({showSaveDialog: false});
              this.props.navigation.goBack();
            },
          },
        ]}
      />
    );
  }

  /**
   * 隐私区域隐藏开关
   * 更新2023年11月15日
   * 合并spec，这里不做值更改值保存临时值
   * @param value
   */
  _onHideAreaChange(value) {
    console.log('隐私区域开关', value);
    this.setState({hidePrivateAreaSwitch: value, canSave: true});
  }

  componentWillUnmount() {
    if (Platform.OS === 'android') {
      BackHandler.removeEventListener('hardwareBackPress', this.onBackHandler);
    }
  }

  onBackHandler = () => {
    // if (this.state.canSave) {
    //   this.onSubmit();
    //   return true;
    // }
    if (this.state.canSave) {
      this.setState({showSaveDialog: true});
      return true;
    }
    return false;
  };
}

