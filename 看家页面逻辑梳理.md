# 看家页面逻辑梳理文档

## 1. 页面结构概述

看家页面主要由两个核心文件组成：
- **AlarmListPage.js** - 看家页面主入口，负责页面框架和导航
- **AlarmListPlayerComponent.js** - 看家视频列表组件，负责数据展示和播放

## 2. 主要功能模块

### 2.1 页面入口 (AlarmListPage.js)

#### 核心功能：
- **页面导航管理**：标题栏、返回按钮、设置按钮
- **状态管理**：编辑模式、选择状态、VIP状态
- **生命周期管理**：页面聚焦、横竖屏切换

#### 关键状态：
```javascript
this.state = {
  fullScreen: false,        // 是否全屏
  isEdit: false,           // 是否编辑模式
  numAry: [],              // 选中的项目数组
  isSelectAll: false,      // 是否全选
  vipState: -1,            // VIP状态 (-1:未知, 0:无VIP, 1:有VIP)
  alarmDialog: false,      // 报警对话框状态
  length: 0,               // 数据长度
  lifecycleDay: 3,         // 选中日期
  eventTypeList: [],       // 事件类型列表
}
```

### 2.2 视频列表组件 (AlarmListPlayerComponent.js)

#### 核心功能：
- **数据获取**：从云端获取看家视频列表
- **视频播放**：支持云存储和本地存储视频播放
- **筛选功能**：日期筛选、事件类型筛选
- **编辑操作**：多选删除、全选操作

#### 数据流程：
1. **初始化** → 获取VIP状态 → 获取事件类型配置
2. **数据请求** → 根据筛选条件请求视频列表
3. **数据展示** → 渲染视频缩略图和信息
4. **用户交互** → 播放视频、筛选、编辑操作

## 3. 筛选功能架构

### 3.1 现有筛选项

#### 日期筛选：
- 支持日历选择
- 默认显示最近3天数据
- 支持自定义日期范围

#### 事件类型筛选：
```javascript
const eventTypes = [
  ALL,           // 全部事件
  People,        // 人形检测
  MOVE,          // 移动检测
  SOUND,         // 异响检测
  NOBODY,        // 无人检测
  FENCE,         // 围栏检测
  CRY,           // 哭声检测
  KEY_AREA,      // 重点区域
];
```

### 3.2 筛选组件 (AlarmTopSelectBar.js)

#### 功能特点：
- **TopSelectBar组件**：提供下拉选择UI
- **日历组件**：支持日期范围选择
- **事件类型选择**：支持多种事件类型筛选
- **响应式设计**：适配横竖屏切换

## 4. 数据接口

### 4.1 主要API接口：

#### VIP状态获取：
```javascript
imiAlarmEventCloudApi.getVipState(deviceID)
```

#### 视频列表获取：
```javascript
// 云存储视频
imiAlarmEventCloudApi.getEventList(params)

// 本地存储视频  
aliAlarmEventCloudApi.getEventList(params)
```

#### 事件类型配置：
```javascript
DeviceTemplatesUtils.getConfigProject('camera', model)
```

### 4.2 数据结构：

#### 视频项数据：
```javascript
{
  eventId: string,        // 事件ID
  eventType: number,      // 事件类型
  startTime: number,      // 开始时间戳
  endTime: number,        // 结束时间戳
  thumbnailUrl: string,   // 缩略图URL
  videoUrl: string,       // 视频URL
  duration: number,       // 视频时长
}
```

## 5. 双摄设备扩展需求

### 5.1 新增筛选项需求：
- **双摄画面**：显示双摄像头合成画面的视频
- **固定画面**：显示枪机（固定摄像头）画面的视频  
- **云台画面**：显示球机（云台摄像头）画面的视频

### 5.2 实现方案：

#### UI层面：
1. 在AlarmTopSelectBar组件中新增摄像头类型筛选
2. 筛选项样式与"全部事件"保持一致
3. 支持下拉选择和选中状态显示

#### 数据层面：
1. 扩展API请求参数，支持摄像头类型筛选
2. 根据筛选条件过滤视频数据
3. 更新数据刷新逻辑

#### 逻辑层面：
1. 检测设备是否为双摄设备
2. 动态显示/隐藏摄像头类型筛选
3. 处理筛选条件变更的回调

## 6. 技术要点

### 6.1 性能优化：
- **懒加载**：视频列表分页加载
- **缓存机制**：缩略图本地缓存
- **内存管理**：及时释放视频播放资源

### 6.2 用户体验：
- **加载状态**：显示loading和空状态
- **错误处理**：网络异常和数据异常处理
- **交互反馈**：选择、播放等操作反馈

### 6.3 兼容性：
- **设备适配**：不同型号设备功能差异
- **版本兼容**：新老版本API兼容
- **横竖屏适配**：界面布局自适应

## 7. 关键文件路径

```
projects/com.chuangmi.camera/src/alarmList/
├── AlarmListPage.js                    # 看家页面主入口
├── AlarmListPlayerComponent.js         # 视频列表组件
└── ...

imilab-modules/com.chuangmi.camera.moudle/components/camera/alarm/
├── AlarmTopSelectBar.js               # 顶部筛选栏组件
└── ...
```

## 8. 下一步实现计划

1. **UI实现**：在AlarmTopSelectBar中添加双摄筛选UI
2. **逻辑实现**：添加筛选回调和数据处理逻辑
3. **API扩展**：扩展数据接口支持摄像头类型筛选
4. **测试验证**：在双摄设备上测试功能完整性
