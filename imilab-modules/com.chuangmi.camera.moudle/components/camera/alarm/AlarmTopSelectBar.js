/*
 * 作者：sunhongda
 * 文件：AlarmTopSelectBar.js  模块：imihome_rn  项目：imihome_rn
 * 上海创米科技有限公司  http://www.imilab.com/
 * Copyright (c) 2020
 */

import React, {Component} from 'react';

import {TouchableOpacity, Text, View, StyleSheet, TouchableWithoutFeedback, FlatList, Dimensions} from 'react-native';

import {imiThemeManager, colors, TopSelectBar, showToast} from '../../../../../imilab-design-ui';

import {DateUtils, LetDevice} from '../../../../../imilab-rn-sdk';

import {Calendar, CalendarList, LocaleConfig} from 'react-native-calendars';

import PropTypes from 'prop-types';

import * as RNLocalize from 'react-native-localize';
import {stringsTo} from '../../../../../globalization/Localize';
import {getScreenWidth} from '../../../../../imilab-rn-sdk/utils/Utils';
import DeviceTemplatesUtils from '../../../../../imilab-rn-sdk/core/device/DeviceTemplatesUtils';
import moment from 'moment';
import Orientation from 'react-native-orientation';

const {width, height} = Dimensions.get('window');
let SCREEN_WIDTH = height > width ? width : height;
/*
LocaleConfig.locales['en'] = {
    formatAccessibilityLabel: 'dddd d \'of\' MMMM \'of\' yyyy',
    monthNames: [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December'
    ],
    monthNamesShort: [
        'jan',
        'feb',
        'mar',
        'apr',
        'may',
        'jun',
        'jul',
        'aug',
        'sep',
        'oct',
        'nov',
        'dec'
    ],
    dayNames: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
    dayNamesShort: ['Sun', 'Mon', 'Tue', 'Wed', 'Thur', 'Fri', 'Sat']
};*/

/*LocaleConfig.locales['cn'] = {
    formatAccessibilityLabel: 'dddd d \'of\' MMMM \'of\' yyyy',
    monthNames: [
        '一月',
        '二月',
        '三月',
        '四月',
        '五月',
        '六月',
        '七月',
        '八月',
        '九月',
        '十月',
        '十一月',
        '十二月'
    ],
    monthNamesShort: [
        '一月',
        '二月',
        '三月',
        '四月',
        '五月',
        '六月',
        '七月',
        '八月',
        '九月',
        '十月',
        '十一月',
        '十二月'
    ],
    dayNames: ['日', '一', '二', '三', '四', '五', '六'],
    dayNamesShort: ['日', '一', '二', '三', '四', '五', '六']
};
*/
//写死的
export const locales = RNLocalize.getLocales() || [{languageCode: 'zh'}];
if (!locales[0]) {
  locales[0] = {
    languageCode: 'zh',
  };
}

const systemLanguage =
  locales[0].languageCode == 'zh' || locales[0].languageCode == 'zh-Hant' ? 'cn' : locales[0].languageCode; // 用户系统偏好语言

LocaleConfig.locales[systemLanguage] = stringsTo('calendarLocaleConfig');
LocaleConfig.defaultLocale = systemLanguage;

/**
 * @Description:  看家选择业务UI   后续修改者根据需求向外暴漏属性 ，目前只暴漏了 两个点击事件
 * @Author:   sunhongda
 * @CreateDate: 2020/9/28 18:02
 * @UpdateUser:     更新者
 * @UpdateDate:  2020/9/28 18:02
 * @UpdateRemark:   更新说明
 */
export default class AlarmTopSelectBar extends Component {
  constructor(props) {
    super(props);

    this.state = {
      /** 获取当前时间 **/
      dateTime: this.getDataFormatTime(),
      hideTitleSelect: this.props.hideTitleSelect,
    };
    this.topSelectBarRoot = undefined;

    this.eventCurrentTitles = props.eventCurrentTitles;

    this.devCurrentTitles = props.devCurrentTitles;
    console.log('AlarmTopSelectBar', this.devCurrentTitles);

    this.currentIndex = 0;
    this.currentCameraTypeIndex = 0; // 摄像头类型选择索引，默认选中第一个（双摄画面）
    this.dayData = [];
    this.curDate = new Date();
  }

  render() {
    // 检测是否为双摄设备
    const cameraNumber = DeviceTemplatesUtils.getConfigProject('camera', LetDevice.model).cameraNumber || '1';
    const isDualCamera = cameraNumber === '2';

    let data = [
      // [this.getSelectListView.bind(this, this._onDevItemPress, this._getDevCurrentTitle)],
      [this.props.isSevenDay ? this._renderDay.bind(this) : this.getCalendarView.bind(this)],
      [this.getSelectEventListView.bind(this)],
    ];

    // 如果是双摄设备，添加摄像头类型筛选
    if (isDualCamera) {
      data.push([this.getCameraTypeSelectView.bind(this)]);
    }

    return (
      <TopSelectBar
        ref={this._assignTopSelectBarRoot}
        style={{flex: 1}}
        bgColor={colors.page_bg}
        tintColor={colors.gray}
        activityTintColor={imiThemeManager.theme.primaryColor}
        // arrowImg={}
        // checkImage={}
        titleStyle={{color: imiThemeManager.theme.primaryColor}}
        // maxHeight={300}
        handler={(selection, row) => {
          console.log('----row-----' + data[selection][row]);
          this.setState({firstName: data[selection][row]});
        }}
        hideTitleSelect={this.props.hideTitleSelect}
        externalSelectText={this.props.initData}
        data={data}>
        {/*<View style={{backgroundColor: "black", height: 1}}/>*/}

        {this.props.renderView && this.props.renderView()}
      </TopSelectBar>
    );
  }

  getDataFormatTime() {
    return `${DateUtils.doHandleYear()}-${DateUtils.doHandleMonth()}-${DateUtils.doHandleDay2()}`;
  }

  _assignTopSelectBarRoot = component => {
    this.topSelectBarRoot = component;
  };

  /**
   *获取事件选择特殊布局
   * @returns {*}
   */
  getSelectEventListView() {
    let norStyle = styles.eventItem;

    let clickStyle = [
      styles.eventItem,
      {
        backgroundColor: '#EDF0FC',
        color: imiThemeManager.theme.primaryColor,
      },
    ];

    let eventItemWidth = width < 360 ? 120 : 159; //事件选项的宽度，低于1080P的屏幕宽度为120
    let eventItemNum = parseInt(width / eventItemWidth); //每行事件选项的个数
    let containerWidth = width - 14; //事件列表容器宽度为屏幕宽度减去左右内边距7*2
    //每行除了事件选项留下的空白区域
    let eventItemMarginTotal =
      containerWidth - eventItemWidth * eventItemNum > 10
        ? containerWidth - eventItemWidth * eventItemNum
        : containerWidth - eventItemWidth * (eventItemNum - 1);
    eventItemNum = eventItemMarginTotal >= eventItemWidth ? eventItemNum - 1 : eventItemNum;
    //每个事件选项的水平边距
    let eventItemMarginHorizontal = eventItemMarginTotal / (eventItemNum * 2);

    return (
      <View
        style={{
          padding: 7,
          flexWrap: 'wrap',
          flexDirection: 'row',
          //justifyContent: 'space-between' //废除这个属性，计算每个时间选项的水平边距因为不能适配所有屏幕，见bug#6972
        }}>
        {this.props.eventCurrentTitles.map((content, index) => (
          <TouchableOpacity key={index} activeOpacity={1} onPress={this._onEventItemPress.bind(this, content, index)}>
            <View
              style={[
                {
                  marginHorizontal: eventItemMarginHorizontal,
                  marginVertical: 7,
                  width: eventItemWidth,
                },
                this.currentIndex === index ? styles.selectedEventItemBg : styles.eventItemBg,
              ]}>
              {/*<Text style={this.currentIndex === index ? styles.selectedEventItem : styles.eventItem}*/}
              {/*       text={this._getEventCurrentTitle(index)}/>*/}
              <Text style={this.currentIndex === index ? styles.selectedEventItem : styles.eventItem}>
                {this._getEventCurrentTitle(index)}
              </Text>
            </View>
          </TouchableOpacity>
        ))}
      </View>
    );
  }

  /**
   * 获取摄像头类型选择布局（双摄设备专用）
   * @returns {*}
   */
  getCameraTypeSelectView() {
    // 摄像头类型选项（固定画面在中间）
    const cameraTypes = [
      { name: stringsTo('dual_camera_view'), value: 'dual' },
      { name: stringsTo('fixed_camera_view'), value: 'fixed' },
      { name: stringsTo('ptz_camera_view'), value: 'ptz' }
    ];

    // 三行布局，每行一个选项，参考家人守护时间段设置的重复弹窗样式
    return (
      <View>
        {cameraTypes.map((cameraType, index) => (
          <TouchableOpacity
            key={index}
            activeOpacity={1}
            onPress={this._onCameraTypePress.bind(this, cameraType, index)}>
            <View
              style={{
                maxWidth: "100%",
                width: width,
                height: 54,
                backgroundColor: "#ffffff",
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "flex-start",
                paddingHorizontal: 30,
              }}>
              <Text
                style={{
                  fontSize: 16,
                  color: this.currentCameraTypeIndex === index ? imiThemeManager.theme.primaryColor : "#000000",
                  fontWeight: "500"
                }}>
                {cameraType.name}
              </Text>
            </View>
          </TouchableOpacity>
        ))}
      </View>
    );
  }

  /**
   * 摄像头类型选择监听按下
   * @param cameraType
   * @param index
   */
  _onCameraTypePress(cameraType, index) {
    if (this.props.isListLoading) {
      showToast('正在加载中');
      return;
    }
    this.currentCameraTypeIndex = index;

    this.topSelectBarRoot.funOnPress(cameraType.name);

    // 调用摄像头类型筛选的回调
    this.props.onCameraTypePress && this.props.onCameraTypePress(cameraType, index);
    console.log('摄像头类型筛选回调:', cameraType, index);
  }

  /**
   * 获取选择设备
   * @returns {*}
   */
  getSelectListView(onPress, getText) {
    let norStyle = styles.eventItem;
    let clickStyle = [
      styles.eventItem,
      {
        backgroundColor: '#EDF0FC',
        color: imiThemeManager.theme.primaryColor,
      },
    ];
    return (
      <View
        style={{
          padding: 7,
          flexWrap: 'wrap',
          flexDirection: 'row',
          justifyContent: 'space-between',
        }}>
        {this.props.eventCurrentTitles.map((content, index) => (
          <TouchableOpacity
            key={index}
            activeOpacity={1}
            style={{margin: 7}}
            onPress={onPress.bind(this, content, index)}>
            <Text style={this.currentIndex === index ? clickStyle : norStyle} text={getText(index)} />
          </TouchableOpacity>
        ))}
      </View>
    );
  }

  /**
   * 事件选择监听按下
   * @param currentTitles
   * @param index
   */
  _onEventItemPress(currentTitles, index) {
    if (this.props.isListLoading) {
      showToast('正在加载中');
      return
    }
    this.currentIndex = index;

    this.topSelectBarRoot.funOnPress(this._getEventCurrentTitle(index));

    this.props.onEventItemPress && this.props.onEventItemPress(this.props.eventCurrentTitles, index);
  }

  _getEventCurrentTitle(index) {
    return this.props.eventCurrentTitles[index].eventTypeName;
  }

  /**
   * 方式选择监听按下
   * @param currentTitles
   * @param index
   */
  _onDevItemPress = (currentTitles, index) => {
    this.currentIndex = index;

    this.topSelectBarRoot.funOnPress(this._getDevCurrentTitle(index));

    this.props.onDevItemPress && this.props.onDevItemPress(this.props.onDevItemPress, index);
  };

  _getDevCurrentTitle = index => {
    console.log('_getDevCurrentTitle', this.props);
    return this.props.devCurrentTitles[index][0];
  };

  /***
   * 选择日期按钮按下
   * @private
   */
  _onDayPress() {
    console.log('_onDayPress', this.state.dateTime);

    let m = this.state.dateTime.substring(5, 7);
    let d = this.state.dateTime.substring(8);
    let dayTitle = stringsTo('day') != '日' ? m + '/' + d : `${m}${stringsTo('month')}${d}${stringsTo('day')}`;
    this.topSelectBarRoot.funOnPress(dayTitle);

    this.props.onDayPress && this.props.onDayPress(this.state.dateTime);
  }

  /**
   * 日历控件逻辑
   * @returns {*}
   * @constructor
   */
  getCalendarView() {
    let {showSynchronous056} = DeviceTemplatesUtils.getConfigProject('camera', 'a1FKrifIRwH');
    const date1 = moment(this.state.dateTime);
    const date2 = moment();
    const date3 = moment().subtract(1, 'months').startOf('month');
    // 计算月份差
    const pastScrollRange = date1.diff(date3, 'months');
    let futureScrollRange = date2.diff(date1, 'months');
    // 判断是否需要加上不满一个月的部分
    if (futureScrollRange === 0 && date2.month() !== date1.month()) {
      futureScrollRange += 1; // 不满一月的部分算作一个月
    }
    return (
      <CalendarList
        maxDate={new Date().toDateString()}
        minDate={this.props.minDate ? this.props.minDate : null}
        horizontal={true}
        // style = {{marginHorizontal:25,marginTop:64}}
        // width = {Dimensions.get('window').width - 50}
        // current={this.state.dateTime}
        calendarWidth={SCREEN_WIDTH}
        onDayPress={day => {
          console.log('AlarmListPlayerPage onDayPress pressed formatDate' + day.dateString);
          //刷新完成后调用确保取值正确
          this.setState({dateTime: day.dateString}, () => {
            this._onDayPress();
          });
        }}
        pagingEnabled={true}
        onVisibleMonthsChange={month => {
          console.log('AlarmListPlayerPage onDayPress pressed formatDate', month);
          this.setState({
            dateTime: month[0].dateString
          }, () => {
            this.props.onVisibleMonthsChange && this.props.onVisibleMonthsChange(month);
          })        
        }}
        current={this.props.current ? new Date(this.props.current) : this.state.dateTime} //设置选中时间
        // 允许滚动到过去的最大月份数。Default = 50
        pastScrollRange={pastScrollRange}
        // 允许滚动到未来的最大月份数。默认值 = 50
        futureScrollRange={futureScrollRange} // 允许滚动到未来的最大月份数。Default = 50 0再选择上月后无法滑动到本月
        markedDates={
          this.props.dateTimeDotSelect || {
            [this.state.dateTime]: {
              selected: true,
              marked: false,
              disableTouchEvent: true,
              selectedColor: imiThemeManager.theme.primaryColor,
            },
          }
        }
        theme={{
          todayTextColor: imiThemeManager.theme.primaryColor,
          // todayBackgroundColor: this.props.showTodayBackGroundColor ? '#D9E2F8' : 'transparent',
          selectedDayBackgroundColor: imiThemeManager.theme.primaryColor,
          textDayHeaderFontSize: 12, //星期的字体大小
          /*'stylesheet.calendar.header': {
                        week: {
                            marginTop: 5,
                            flexDirection: 'row',
                            justifyContent: 'space-around',
                        }
                    }*/
        }}
      />
    );
  }

  getDays() {
    let now = new Date();
    let now_000 = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);
    let add = now_000.getDay() == 6 ? 0 : now_000.getDay() + 1;
    let dayData = [];
    for (let i = 6 + add; i >= 0; i--) {
      let day = {};
      day.date = new Date(now_000.getTime() - i * (24 * 60 * 60 * 1000));
      dayData.push(day);
    }
    this.dayData = dayData;
    this.curDate = now_000;
  }

  _renderWeekItem = ({item, index}) => {
    // let screenWidth = Dimensions.get('window').width>Dimensions.get('window').height?Dimensions.get('window').height:Dimensions.get('window').width;
    let widthItem = SCREEN_WIDTH / 7;
    console.log('日期宽度均分---', widthItem);

    return (
      <View style={{height: 50, width: widthItem}}>
        <Text
          allowFontScaling={false}
          style={{
            width: '100%',
            lineHeight: 70,
            height: '100%',
            textAlignVertical: 'center',
            textAlign: 'center',
            color: '#7F7F7F', // shenyonggang@20200804 add for select color change
          }}>
          {item}
        </Text>
      </View>
    );
  };
  _renderDayItem = ({item, index}) => {
    // let screenWidth = Dimensions.get('window').width>Dimensions.get('window').height?Dimensions.get('window').height:Dimensions.get('window').width;
    let widthItem = SCREEN_WIDTH / 7;
    console.log('日期宽度均分-riqi--', widthItem);
    let weekDay = this.dayData[this.dayData.length - 1];
    let week = weekDay.date;
    let isSelected = this.curDate.getDate() == item.date.getDate();
    let date = item.date;
    let dayTxt = (date.getDate() < 10 ? '0' : '') + date.getDate();
    if (index < week.getDay() && this.dayData.length != 7) {
      dayTxt = '';
    }
    // console.log("_renderDayItem",item);

    return (
      <TouchableOpacity
        style={{
          height: 70,
          width: widthItem,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
        onPress={() => {
          if (dayTxt != '') {
            this.curDate = date;
            this.setState({dateTime: DateUtils.dateFormat(date, 'yyyy-MM-dd')}, () => {
              this._onDayPress();
            });
          }
        }}>
        {isSelected ? (
          <View
            style={{
              position: 'absolute',
              backgroundColor: '#1E5BA9',
              width: widthItem,
              height: widthItem,
              borderRadius: widthItem / 2,
            }}
          />
        ) : null}

        <View style={{height: 70, width: widthItem}}>
          <Text
            allowFontScaling={false}
            style={{
              width: '100%',
              lineHeight: 70,
              height: '100%',
              textAlignVertical: 'center',
              textAlign: 'center',
              color: isSelected ? 'white' : '#7F7F7F', // shenyonggang@20200804 add for select color change
            }}>
            {dayTxt}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  _renderDay() {
    let calendarLocaleConfig = stringsTo('calendarLocaleConfig');
    if (this.dayData.length == 0) {
      this.getDays();
    }
    return (
      <View style={{width: SCREEN_WIDTH}}>
        <View style={{height: 50, width: SCREEN_WIDTH, backgroundColor: 'white'}}>
          <FlatList
            data={calendarLocaleConfig.dayNames}
            horizontal={true}
            renderItem={this._renderWeekItem}
            keyExtractor={(item, index) => `key_${index}`}
          />
        </View>
        <View
          style={{
            height: this.dayData.length == 7 ? 70 : 140,
            width: SCREEN_WIDTH,
            backgroundColor: 'white',
          }}>
          <FlatList
            data={this.dayData}
            numColumns={7}
            // horizontal={true}
            renderItem={this._renderDayItem}
            keyExtractor={(item, index) => `key_${index}`}
          />
        </View>

        <TouchableWithoutFeedback onPress={() => {}}>
          <View
            style={{
              flex: 1,
              opacity: 0.5,
              width: SCREEN_WIDTH,
              backgroundColor: 'black',
            }}
          />
        </TouchableWithoutFeedback>
      </View>
    );
  }

  componentDidMount() {
    Orientation.lockToPortrait();
  }
}

AlarmTopSelectBar.propTypes = {
  onEventItemPress: PropTypes.func,
  onDayPress: PropTypes.func,
  onDevItemPress: PropTypes.func,
  onVisibleMonthsChange: PropTypes.func,
  onCameraTypePress: PropTypes.func, // 摄像头类型筛选回调

  eventCurrentTitles: PropTypes.array,
  devCurrentTitles: PropTypes.array,
  dateTimeDotSelect: PropTypes.object,

  initData: PropTypes.array,

  renderView: PropTypes.func,
  hideTitleSelect: PropTypes.bool,
  showTodayBackGroundColor: PropTypes.bool, // 是否显示今天日期背景色
  isSevenDay: PropTypes.bool, //是否是7天循环云存储 默认为否
  minDate: PropTypes.string,
  pastScrollRange: PropTypes.number,
  futureScrollRange: PropTypes.number,
};

const styles = StyleSheet.create({
  eventItem: {
    textAlign: 'center',
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.gray,
    lineHeight: 21,
  },

  eventItemBg: {
    padding: 14,
    // width: 159,
    borderRadius: 8,
    backgroundColor: '#F2F3F5',
  },

  selectedEventItem: {
    textAlign: 'center',
    fontSize: 14,
    fontWeight: 'bold',
    lineHeight: 20,
    color: imiThemeManager.theme.primaryColor,
  },
  selectedEventItemBg: {
    padding: 14,
    // width: 159,
    borderRadius: 8,
    backgroundColor: '#EDF0FC',
  },
});
