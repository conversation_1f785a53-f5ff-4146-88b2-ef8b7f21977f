/*
 * 作者：sunhongda
 * 文件：AlarmListPlayerComponent.js  模块：imihome_rn  项目：imihome_rn
 * 上海创米科技有限公司  http://www.imilab.com/
 * Copyright (c) 2020
 */

import React from 'react';

import {
    BackHandler,
    DeviceEventEmitter,
    StyleSheet,
    View,
    TouchableOpacity,
    StatusBar,
    Image,
    Text,
    Modal, AbsoluteFillStyle
} from "react-native";

import {XFlatList, XImage, XText, XView} from 'react-native-easy-app';

import {
    CheckBoxButton,
    colors,
    CONST,
    IMIDesignEmptyView,
    imiThemeManager,
    RNLine,
    showLoading,
    showToast
} from '../../../../../imilab-design-ui';

import {
    AlarmType,
    BaseDeviceComponent,
    DateUtils,
    imiAlarmEventCloudApi,
    LetDevice,
    IMIGotoPage
} from '../../../../../imilab-rn-sdk';
import DeviceTemplatesUtils from '../../../../../imilab-rn-sdk/core/device/DeviceTemplatesUtils';
var moment = require('moment');
import NetInfo, {NetInfoStateType} from "@react-native-community/netinfo";

import AlarmTopSelectBar from "./AlarmTopSelectBar";
import iconDot from '../../../resources/images/icon_dot.png';
import iconBlueDot from '../../../resources/images/icon_blue_dot.png'
import iconPeople from '../../../resources/images/alarm_people_icon.png';
import iconPlay from '../../../resources/images/icon_play.png';
import iconPause from '../../../resources/images/icon_pause.png';

import iconPlayDisabled from '../../../resources/images/icon_play_disabled.png';
import iconPauseDisabled from '../../../resources/images/icon_pause_disabled.png';
import iconVoiceClose from '../../../resources/images/icon_voice_close.png';
import iconVoiceOpen from '../../../resources/images/icon_voice_open.png';
import iconVoiceOpenDisabled from '../../../resources/images/icon_voice_open_disabled.png';
import iconSave from '../../../resources/images/icon_saveforever.png';
import iconSaveDisabled from '../../../resources/images/icon_saveforever_disabled.png';
import iconFull from '../../../resources/images/icon_full.png';
import iconFullDisabled from '../../../resources/images/icon_full_disabled.png';
import iconEmpty from '../../../resources/images/police_pic_empty_p.png';
import iconBubbleTop from '../../../resources/images/icon_bubble_top.png';
import iconDeleteItem from  '../../../resources/images/icon_delete_item.png'
import iconChoose from '../../../resources/images/icon_choose.png'
import iconChooseUn from '../../../resources/images/icon_choose_un.png'
import Orientation from "react-native-orientation";
import PropTypes from 'prop-types';
import Swipeout from 'react-native-swipeout';

import Utils, {
    isEmpty,
    isIos,
    objHasKey,
    getScreenWidth,
    getScreenHeight, isIphoneXSeries
} from "../../../../../imilab-rn-sdk/utils/Utils";
import I18n, {stringsTo} from "../../../../../globalization/Localize";

import {
    CoveLoading,
    CoverVideoImage,
    CoverVideoToolProgress,
    IMICloudVideo,
    VideoPlayerContainer
} from "../../../../../imilab-rn-sdk/components";
import {dateFormat} from "../../../../../imilab-rn-sdk/utils/DateUtils";
import CoveError from "../../player/cover/CoveError";
import {PLAYER_EVENT_CODE} from "../../../../../imilab-rn-sdk/native/camera-kit/IMIVideoView";
import IMIVideoUtils from "../../../../../imilab-rn-sdk/native/local-kit/IMIVideoUtils";
import IMIFile from "../../../../../imilab-rn-sdk/native/local-kit/IMIFile";
import CoveRetry from "../../player/cover/CoveRetry";
import {PlayingImiCloudImageRootView, PlayingRootView} from "../ui/BusinessICon/BusinessPlayerIcon";
import IMIImageDownLoader
    from "../../../../../imilab-rn-sdk/components/av/imicloud/IMIImageDownLoader";
import * as Typography from "../../../../../imilab-design-ui/src/style/Typography";
import IMIPlayerViewBase from "../../../../../imilab-rn-sdk/components/av/player/IMIPlayerViewBase";
import IMIPermission from "../../../../../imilab-rn-sdk/native/local-kit/IMIPermission";
import IMIDownload from "../../../../../imilab-rn-sdk/native/local-kit/IMIDownload";
import {INTELLIGENT_TYPE} from "../../../../../imilab-rn-sdk/components/camera/alarm/api/ALiAlarmEventCloudApi";
import IMIAlarmEventCloudApi
    from "../../../../../imilab-rn-sdk/components/camera/alarm/api/IMIAlarmEventCloudApi";
import {RoundedButtonView} from "../../../../../imilab-design-ui";
import CoverJSCloudImage from "../../player/cover/CoverJSCloudImage";
import {IMINativeLifeCycleEvent} from "../../../../../imilab-rn-sdk/native/local-kit/IMINativeLifeCycle";
import IMIImageViewCloud from "../../../../../imilab-rn-sdk/components/av/imicloud/ui/IMIImageViewCloud";
import IMILogUtil from "../../../../../imilab-rn-sdk/native/local-kit/IMILogUtil";
import {CalendarList} from "react-native-calendars";

const topDistance = getScreenWidth()/3*16/9+28; //竖屏时下载进度气泡的top为播放器高度到下载按钮底部的值
const rightDistance = getScreenWidth()*1/4+10; //竖屏时下载的气泡尖尖距离右边的距离。如果实时获取，横竖屏切换时值会变导致尖尖漂移

/**
 * @Description: 摄像机看家模块
 * @Author:   sunhongda
 * @CreateDate: 2020/9/28 18:02
 * @UpdateUser:     更新者
 * @UpdateDate:  2020/9/28 18:02
 * @UpdateRemark:   更新说明
 */
const defaultEventCurrentTitles = [
    [stringsTo('all_events_str'), AlarmType.ALL], [stringsTo('peopleDetectStr'), AlarmType.Stay],
    /*[stringsTo('stayDetectStr'), AlarmType.Stay],*/ //2021.4.28 改为人形事件
];


const LIMIT = 10;

const TAG = " AlarmListPlayerComponent- ";
let isDownloading = false;

let pageInForeground = true;

export default class AlarmListPlayerComponent extends BaseDeviceComponent {

    static propTypes = {
        /* 事件选择title 数组 */
        eventCurrentTitles: PropTypes.array,
        /* 设备选择title 数组 */
        devCurrentTitles: PropTypes.array,

        onClickFullScreen: PropTypes.func,
        isCloudStorageValid: PropTypes.bool, //云存是否生效(领取、购买后)中
      //  showExpiredHint: PropTypes.bool, //覆盖在视频播放器上的云存过期提醒
        isSupportThumbWhenExpired: PropTypes.bool, //是否支持没云存时显示图片，目前仅031支持  /*20211203*/
        isPlaying: PropTypes.bool,
        /* ["1/27", "全部事件"] */
        initDate: PropTypes.array,
        isSevenDay: PropTypes.bool,
        /*多选删除新增*/
        dataListLengthChangeListener:PropTypes.func,
        isOpenSelectMode:PropTypes.bool,//是否处理编辑模式
        /*多选删除新增*/
        triggerRef:PropTypes.func
    };

    static defaultProps = {
        eventCurrentTitles: null,
        onClickFullScreen: null,
        isCloudStorageValid: true,
       // showExpiredHint: false,
        isSupportThumbWhenExpired: false,
        isPlaying: false,
        initDate: [stringsTo('day') != "日"?DateUtils.dateFormatMMDD:`${DateUtils.doHandleMonth()}${stringsTo('month')}${DateUtils.doHandleDay2()}${stringsTo('day')}`, stringsTo('all_events_str')],
        isSevenDay: false,
        isOpenSelectMode:null, //是否开启多选删除模式，为null代表不支持多选删除，使用侧滑删除
    };

    constructor(props) {
        super(props);
        this.state = {
            /* 数据集合  */
            dataList: [],

            /* 是否静音状态   */
            muted: false,
            videoCoverPath: undefined,
            dataSourceImageItem: undefined,
            flatListHeight: 0,
            isDataEmpty: false,
            fullScreen: Orientation.getInitialOrientation() !== 'PORTRAIT',
            isCloudStorageValid: this.props.isCloudStorageValid,
          //  showExpiredHint: this.props.showExpiredHint,
            isSupportThumbWhenExpired: this.props.isSupportThumbWhenExpired,
            isPlaying: props.isPlaying ? props.isPlaying : false,
            dataSource: {},
            /** 图片缓存 **/
            thumbPathMap: new Map(),
            /** 是否加载进度 **/
            indicatorLoading: this.props.indicatorLoading || false,
            showCover: this.props.showCover || false,
            /** 某个日期下日历小圆点 **/
            dateTimeDotSelect: this.props.dateTimeDotSelect,
            downloadingProgress:stringsTo("video_downloading") +"  0%", //当前视频下载进度，
            isDownloading:false,
            closeDelete:false,//关闭侧滑删除组件
            /*多选删除新增*/
            isSelectAll:false,//是否全选
            isOpenSelectMode:this.props.isOpenSelectMode,//是否打开编辑
            isSelectBtn:false,//全选与单选冲突  在点击选中单个item后  再取消全选
            enableDeleteBtn:false, //底部删除按钮是否可用，不可用，置灰
            refreshing:false, //只刷新item 不刷新视频
            /*多选删除新增*/
            currentItemOnlyShowImage:false, //当前选中的是否只显示图片，无法播放视频
            isPlayFromBeginning:false,//单选删除进度条不是0

            currentItemSupportSave:false, //用于标记031云存过期后显示的图片是否可以下载

            showErrorView:false,
            showVideoCover:false,
            progressDuration:0,
            progressCurrentTime:0,

        };
        this.reqTime = this._getDataFormatTime();
        this.reqIntelligentTypeArray = [];

        this.mCurItem = null;
        this.mCurIndex = 0;
        this.isCompletion = false;
        /* 请求结果原始集合  */
        this.dataListNet = [];
        /*********多选删除新增********/
        this.dataSelect=[],//选中多个  选中数组  item选中后的id
        this.isOnClick=1;//是否选中  2状态为item已点击
        this.timeSelect=0;//删除时的时间
        /*********多选删除新增********/
        this.onEndReachedCalledDuringMomentum = true;
        this.mDownloadVideoListener = null;

        this.isConnectNet = true; //当前是否有网络连接
        this.selectType = null;
    }

    _getDataFormatTime() {
        return `${DateUtils.doHandleYear()}-${DateUtils.doHandleMonth()}-${DateUtils.doHandleDay2()}`; // return `2021-03-09`;

    };

    componentWillReceiveProps(newProps) {
        if (newProps.isCloudStorageValid !== this.state.isCloudStorageValid) {
            this.setState({isCloudStorageValid: newProps.isCloudStorageValid});
            if (newProps.isCloudStorageValid) { //买了云存回来需要重新拉取数据
                this._getAlarmListData();
            }
        }
        if (newProps.isSupportThumbWhenExpired !== this.state.isSupportThumbWhenExpired) {
            this.setState({isSupportThumbWhenExpired: newProps.isSupportThumbWhenExpired});
        }
        if (newProps.isOpenSelectMode !== this.state.isOpenSelectMode) {
            this.setState({isOpenSelectMode: newProps.isOpenSelectMode});
        }
    }

    render() {
        console.log(TAG + "render  - > props", this.props);
        console.log(TAG + "render  - > state", this.state);

        let {devCurrentTitles, initDate, eventCurrentTitles} = this.props;

        console.log(TAG + " devCurrentTitles+devCurrentTitles", devCurrentTitles);

        // 检测是否为双摄设备
        const cameraNumber = DeviceTemplatesUtils.getConfigProject('camera', LetDevice.model).cameraNumber || '1';
        const isDualCamera = cameraNumber === '2';

        // 动态构建初始化数据，顺序要与data数组保持一致
        let dayTitle = stringsTo('day') != "日" ? DateUtils.dateFormatMMDD : `${DateUtils.doHandleMonth()}${stringsTo('month')}${DateUtils.doHandleDay2()}${stringsTo('day')}`;
        let dynamicInitDate = [dayTitle]; // 第一个：时间筛选

        // 如果是双摄设备，第二个位置添加摄像头类型选项（默认选中双摄画面）
        if (isDualCamera) {
            dynamicInitDate.push(stringsTo('dual_camera_view')); // 第二个：摄像头类型筛选
        }

        // 最后添加事件筛选
        dynamicInitDate.push(stringsTo('all_events_str')); // 最后一个：事件筛选

        //动态计算当前选中日期和今天日期间隔几个月，以此来确定可以向前滑和向后滑几个月
        let monthNumber = DateUtils.getMonthDurationNubmer(new Date(),new Date(this.reqTime));

        return (
            <XView style={styles.container}>
                <AlarmTopSelectBar
                    initData={dynamicInitDate}
                    dateTimeDotSelect={this.state.dateTimeDotSelect}
                    minDate={new Date(new Date().setDate(new Date().getDate()-30)).toDateString()}//包括当天，一共可以点击31天
                    pastScrollRange={monthNumber == 0 ? 1 : 0} //031,033目前只能滑到上个月
                    futureScrollRange={monthNumber}

                    isSevenDay={this.props.isSevenDay}
                    devCurrentTitles={devCurrentTitles}
                    eventCurrentTitles={eventCurrentTitles ? eventCurrentTitles : defaultEventCurrentTitles}
                    onEventItemPress={(currentTitles, index) => {
                        //切换事件 重置播放器
                        this._resetPlayer();
                        console.log(TAG + " typeParams type =" , currentTitles[index][1]);
                        IMILogUtil.uploadClickEventValue({HouseEvent:currentTitles[index][1]});
                        let typeParams = imiAlarmEventCloudApi._eventType2Info(currentTitles[index][1]);
                        console.log(TAG + " typeParams " + typeParams);
                        this.selectType = typeParams;
                        //根据产品特殊要求，只要存在人形均算为逗留
                        if (typeParams === INTELLIGENT_TYPE.stay) {
                            this.reqIntelligentTypeArray = [INTELLIGENT_TYPE.stay, INTELLIGENT_TYPE.humanFace, INTELLIGENT_TYPE.humanShape]
                        } else {
                            this.reqIntelligentTypeArray = typeParams ? [typeParams] : [];
                        }

                        this._getAlarmListData();
                    }}
                    onCameraTypePress={(cameraType, index) => {
                        // 摄像头类型筛选回调处理
                        console.log(TAG + " 摄像头类型筛选:", cameraType, index);
                        // 这里可以根据需要添加具体的筛选逻辑
                        // 例如：根据摄像头类型过滤视频数据
                        this._resetPlayer();
                        this._getAlarmListData();
                    }}
                    onVisibleMonthsChange={(month) => {
                        // this._updateCalendarInfo(month[0]?.dateString);
                    }}
                    hideTitleSelect={this.state.fullScreen}
                    onDayPress={(date) => {
                        this.reqTime = date;
                        IMILogUtil.uploadClickEventValue({HouseDate:date})
                        //刷新可点击状态
                        this._updateCalendarInfo();
                        //切换天数 重置播放器
                        this._resetPlayer();


                        this._getAlarmListData();
                    }}
                    renderView={()=>this._renderView()}
                />
                {/*{  //编辑模式遮挡视频相关 防止在做操作
                    this.state.isOpenSelectMode? <View style={{zIndex:500,position: 'absolute', width:"100%",backgroundColor:"#678",height:getScreenHeight()*0.30}}/>:null}
*/}
            </XView>
        );
    }

    /**
     * 重置当前播放器 和当前播放状态
     * @private
     */
    _resetPlayer() {
        /* 当前播放的item position  {int }     */
        this.mCurIndex = 0;
        /* 当前播放的item          { object }  */
        this.mCurItem = null;
        /* 下载使用缓存            {object }   */
        this.mCurItemLocal = null;

        //20211210 因为在静音情况下，切换日期和事件，android会有声音，所以直接开启声音
        this.setState({
            isPlaying: false,
            dataSource: {},
            muted: false,
            showCover:false,
            progressDuration: 0,
            progressCurrentTime: 0
        }, () => this.setState({progressDuration: -1, progressCurrentTime: -1}));

        this.videoPlayer && this.videoPlayer.reset();
        this.videoPlayer = null;
    }

    /**
     * 获取当前数据           **  可以进行子类重写 **
     * @param isPullDown
     * @private
     */
    _queryDataList = (isPullDown) => {
        console.log(" _queryDataList START ------------------ " + (isPullDown ? "下拉刷新" : "上拉加载"));
        this.refreshList && this.refreshList.refreshPreLoad(isPullDown);
        let paramsToken = isPullDown ? "" : this.reqToken;
        this._changeEventTime(this.reqTime, paramsToken, this.reqIntelligentTypeArray, isPullDown);
        console.log(" _queryDataList  END   ------------------ ");
    };


    /**
     * 切换事件的时间点，
     * @param time  格式化过后的 yyy/mm/dd 格式时间
     * @param paramsToken
     * @param intelligentTypeArray 需要查询的事件类型
     * @param isPullDown
     * @private
     */
    _changeEventTime(time, paramsToken, intelligentTypeArray, isPullDown) {
        let dataList = this.state.dataList;

        this.getDataTimeoutTimer = setTimeout(()=>{
           // showToast("Network Exception");
            this.refreshList && this.refreshList.refreshLoaded(false, isPullDown, false, true);
        },6000);

        let curStartTime = DateUtils.startUnix(time);
        let curEndTime = DateUtils.endUnix(time);
        this.timeSelect=curStartTime;
        console.log(" eventStorageSessions  curStartTime " + curStartTime + " curEndTime " + curEndTime + " time  " + time + " intelligentTypeArray " + intelligentTypeArray);

        imiAlarmEventCloudApi.eventStorageSessions(LetDevice.model,
            LetDevice.deviceID,
            curStartTime,
            curEndTime,
            paramsToken,
            LIMIT, intelligentTypeArray).then((data) => {
            console.log("优化优化优优化-----本次拉取到的原始数据",data);
            this.getDataTimeoutTimer && clearTimeout(this.getDataTimeoutTimer); //拉取数据有返回立马清除6秒超时定时器

            let {sessions, token} = data;

            this.reqToken = token;

            console.log('eventStorageSessions  then-> json ', data);

            let isSuccess = true;

            //当前获取数据是否为空
            let isDataEmpty = !objHasKey(sessions);

            console.log('eventStorageSessions  dataList  isDataEmpty  1 -> ' + isDataEmpty);

            //是否还可以上拉加载
            let noMoreData = isEmpty(token);
            console.log('eventStorageSessions  dataList  noMoreData   -> ' + noMoreData);

            //如果此时没有数据，不认为没有更多数据
            //一条数据时会不断去转圈 IMI_IPC031_A01-173
            // isDataEmpty && (noMoreData = false);

            console.log('eventStorageSessions  dataList  isDataEmpty  2 -> ' + isDataEmpty + " noMoreData " + noMoreData + " isPullDown " + isPullDown);

            this.refreshList && this.refreshList.refreshLoaded(isSuccess, isPullDown, noMoreData, false);

            console.log('eventStorageSessions  dataList  isDataEmpty dataListNet  -> ', this.dataListNet);

            let rawData = [];
            //取出map到array中形成List 用于列表的展示
            for (let value in sessions) {
                if (!sessions.hasOwnProperty(value)) {
                    continue
                }
                rawData.push(sessions[value]);
            }

            let itemList = isPullDown ? rawData : [...dataList, ...rawData];

            //去重 并筛选时长低于或等于0的视频
            let result = [];
            let obj = {};
            for (let i = 0; i < itemList.length; i++) {
                                               //startTime和endTime只精确到秒，所以会出现0秒视频但是可以播的问题(#9236)
                if (!obj[itemList[i].sessionId] && itemList[i].startTime != itemList[i].endTime) {
                    result.push(itemList[i]);
                    obj[itemList[i].sessionId] = true;
                }
            }
            itemList = result;

            //对数据进行排序
            itemList.sort(function (a, b) {
                return b.startTime - a.startTime;
            });

            console.log('结果--------------',itemList);

            //图片加载 使用排序后的集合，保证先加载前面的图片
            for (let i = 0; i < itemList.length; i++) {
                IMIImageDownLoader.instance.addDownLoadImageInfo(LetDevice.model, "", itemList[i]);
            }

            //如果不是下拉刷新，則继续使用之前的
            if (!isPullDown) {
                this.setState({
                    dataList: itemList,
                });
            } else { //下拉刷新后，默认加载一个视频封面

                let videoCoverPath = itemList.length > 0 ? itemList[0]['thumbPath'] : null;
                let currentShowImage = itemList.length > 0 ? itemList[0]['bucket'].indexOf("meta-free")!=-1 : null;
                let dataSourceImageItem = itemList.length > 0 ? itemList[0] : null;

                if(currentShowImage){ //如果是云存过期，且有图片,默认选择和显示头一张图片
                    this.delay && clearTimeout(this.delay);
                    this.delay = setTimeout(()=>{
                        this._onItemPress(itemList[0],0);
                        this.delay && clearTimeout(this.delay);
                    },2000);
                }

                this.setState({
                    dataList: itemList,
                    isDataEmpty: isDataEmpty,
                    videoCoverPath: videoCoverPath,
                    showVideoCover: this.mCurItem?false:true,//防止正在播放视频时下拉刷新，出现封面遮挡画面(bug#10179)
                    currentItemSupportSave: this.mCurItem ? true : false,//避免某些情况下切换事件，全屏和保存按钮置灰的问题(#10881)
                    dataSourceImageItem: dataSourceImageItem,
                });

            }

            //多选编辑模式下
            this.props.dataListLengthChangeListener && this.props.dataListLengthChangeListener(itemList.length);


        }).catch((error) => {
            console.log(' eventStorageSessions error ' + JSON.string(error));
            this.refreshList && this.refreshList.refreshLoaded(false, isPullDown,false,true);
            this.getDataTimeoutTimer && clearTimeout(this.getDataTimeoutTimer);
        });
    }


    _renderView() {
        console.log(" _renderView  fullScreen " + this.state.fullScreen);
        console.log(' _renderView  _renderView  isDataEmpty  ' + this.state.isDataEmpty);

        return (
            /*横竖屏使用不同的背景色，为了防止在新RN0.71.4框架，横屏时视频未覆盖区域为白色的问题*/
            <View style={{flex: 1,backgroundColor:this.state.fullScreen?"black":'white'}}>

                {/*渲染播放器*/}
                {!this.state.isDataEmpty || !this.state.isCloudStorageValid ? this._renderPlayerVideoViewComponents() : null}
                {/*渲染播放器*/}

                {/*渲染图片播放器 支持无云存显示图片*/}
                {this.state.isSupportThumbWhenExpired&&this.state.currentItemOnlyShowImage?this._renderCloudThumbImageWhenExpired():null}
                {!LetDevice.isShareUser&&!this.state.isCloudStorageValid?this._renderGoBuyCloudStorage():null}
                {/*渲染图片播放器,支持无云存显示图片*/}


                {/*<View >*/}
                    {(!this.state.isDataEmpty || !this.state.isCloudStorageValid)&&!this.state.fullScreen ? this._renderVideoToolBar() : null}

                    {(!this.state.isDataEmpty || !this.state.isCloudStorageValid)&&!this.state.fullScreen ? <RNLine/> : null}

                    {/*渲染播放列表，如果横屏时显示为null，竖屏后则会丢失用户当前用户滑动的距离，需要手动滑到之前选择的视频处*/}
                    {this.state.isCloudStorageValid || this.state.isSupportThumbWhenExpired? this._renderListView() : null}
                    {/*渲染播放列表*/}

                    {!this.state.fullScreen &&this._renderDownloadingView()}
                {/*</View>*/}

                {this._bottomOfTheChoice()}

                {/*不支持云存到期显示图片的设备，当云存过期时的空提示View是在视频播放器中间显示播放按钮，视频列表显示为空且有提示*/}
                {!this.state.fullScreen ? this._renderEmptyListHintView() : null}

            </View>
        )
    }

//云存到期列表为空的提示
    _renderEmptyListHintView() {

        if (!this.state.isCloudStorageValid && !this.state.isSupportThumbWhenExpired
            || !this.state.isCloudStorageValid && this.state.isSupportThumbWhenExpired && this.state.isDataEmpty) {
            return (
                <View style={{flex: 1, alignItems: "center", justifyContent: "center"}}>
                    <XText style={{
                        textAlign: 'center',
                        color: "#7F7F7F",
                        fontSize: 15,
                    }}
                           text={I18n.t('noAlarmVideo')}
                    />

                </View>);
        }
        return null;
    }


    /*视频播放器，如果支持云存未生效时显示图片的，则是图片展示区域*/
    _renderPlayerVideoViewComponents() {
        //设备支持到期显示云存图片，且当前的item是图片，就渲染视频播放器，而是显示图片显示器
        if(this.state.isSupportThumbWhenExpired&&this.state.currentItemOnlyShowImage){ //支持显示图片时,不渲染播放器
            return null;
        }
        return (
            <View>

                {!this.state.fullScreen ? <RNLine/> : null}

                {this.getPlayerVideoView()}

               {/* {!this.state.fullScreen ? this._renderVideoToolBar() : null}

                {!this.state.fullScreen ? <RNLine/> : null}*/}


            </View>
        )
    }

    /*云存过期且不支持显示云存图片，覆盖在视频播放区域的提示*/
    _renderCloudVideoExpiredHintView() {
        return (
            <View style={{
                position: "absolute",
                width: "100%",
                height: "100%",
                alignItems: "center",
                justifyContent: "center"
            }}>
                <XText style={{
                    textAlign: 'center',
                    color: colors.white,
                    fontSize: 15,
                }}
                       text={I18n.t('cloud_time_out_str')}
                />

                <RoundedButtonView buttonText={stringsTo('buy_again')}
                                   buttonStyle={{
                                       backgroundColor: "#D8B08A",
                                       margin: 14,
                                       paddingHorizontal: 20,
                                       height: 40,
                                       fontWeight: "500"
                                   }}
                                   buttonTextStyle={{textAlign: 'center', fontWeight: "500"}}
                                   onPress={() => {
                                       IMIGotoPage.starCloudBuyPage(LetDevice.deviceID)
                                   }}/>

            </View>
        );

    }

    /*云存未生效时，显示云存缩略图*/
    _renderCloudThumbImageWhenExpired(){
        return (
            <IMIImageViewCloud
                style={{width:getScreenWidth(),height:getScreenWidth()*9/16}}
                source={ {
                    "productKey": LetDevice.model,
                    "dataSource": this.state.dataSourceImageItem
                }}/>
        );
    }

    /*云存未生效(未领或到期)时，吸附在展示云存图片的右上角区域，提示购买云存储*/
    _renderGoBuyCloudStorage() {
        return (
            <XView style={{position: "absolute", right: 0, top: 24, maxWidth: 2607, backgroundColor: "#496EE0",borderTopLeftRadius:25,borderBottomLeftRadius:25}}
                   onPress={() => {
                       IMIGotoPage.starCloudBuyPage(LetDevice.deviceID);
                   }}>
                <Text numberOfLines={4} style={{marginVertical: 12, marginLeft: 16, marginRight: 6,textAlign:"right",color:"#FFFFFF",fontSize:12}}>
                    {stringsTo("cloudExpireShowImageHint")}
                </Text>
            </XView>);
    }

    getPlayerVideoView() {

        console.log(TAG + " getPlayerVideoView  dataSourceImageItem ", {
            "productKey": LetDevice.model,
            "dataSource": this.state.dataSourceImageItem
        });

        return (
            <View style={this.state.fullScreen?{flex: 1, alignItems:"center"}:{}}>
                <VideoPlayerContainer
                    style={{width: '100%', height: '100%'}}
                    ref={ref => this.videoPlayer = ref}
                    muted={this.state.muted}
                    PlayerElement={IMICloudVideo}
                    isPlaying={this.state.isPlaying}
                    dataSource={this.state.dataSource}
                    cover={[
                        {
                            "instance": CoveLoading,
                            "props": {
                                "indicatorLoading": this.state.indicatorLoading,
                            }
                        },
                        {
                            "instance": CoverJSCloudImage,
                            "props": {
                                "dataSource": {
                                    "productKey": LetDevice.model,
                                    "dataSource": this.state.dataSourceImageItem
                                },
                                "showVideoCover": this.state.showVideoCover

                            }
                        },
                        {
                            "instance": CoverVideoToolProgress,
                            "props": {
                                "isPlayFromBeginning":this.state.isPlayFromBeginning,
                                "duration":this.state.progressDuration,
                                "currentTime":this.state.progressCurrentTime,
                            }
                            /*"props":{
                                "onBackPress" : ()=>{this._onBackAndroid();StatusBar.setBarStyle('dark-content')}
                            }*/
                        },
                        {
                            "instance": CoveError,
                            "props": {
                                "buttonPressed": () => {
                                    this.setState({indicatorLoading: true,showErrorView:false,showVideoCover:false});
                                    this.videoPlayer.reset();
                                    this.videoPlayer.start();
                                },
                                "showCover":this.state.showErrorView
                            }
                        },
                        {
                            "instance": CoveRetry,
                            "props": {
                                "buttonPressed": () => {
                                    this.setState({showCover:false,showVideoCover:true,indicatorLoading: true,showErrorView:false});
                                    this.videoPlayer.start();
                                },
                                "showCover": this.state.showCover
                            }
                        }
                    ]}
                    onErrorChange={(error) => {

                        //如果播放出现错误停止播放器
                        this.setState({isPlaying: false,indicatorLoading:false,showErrorView:true});
                    }}
                    onEventChange={this._doHandleOnEventChange.bind(this)}
                    onInteriorStateCall={this._onInteriorStateCall}
                    onPlayCompletion={() => {
                        this.isCompletion = true;
                        console.log('onPlayCompletion 播放完成了');
                        //重置播放状态
                        this.setState({isPlaying: false, indicatorLoading: false, showCover: true});
                        // this.videoPlayer && this.videoPlayer.reset();
                    }}
                    onUpdateOrientation={(orientation) => {
                        //如果切换到竖屏
                        // orientation !== 'PORTRAIT' && this._startOrPause();
                        this.setState({fullScreen: orientation !== 'PORTRAIT'});
                        this.props.onClickFullScreen && this.props.onClickFullScreen(orientation !== 'PORTRAIT');
                    }
                    }/>

                {/*{!LetDevice.isShareUser && !this.props.isCloudStorageValid && !this.state.isSupportThumbWhenExpired? this._renderCloudVideoExpiredHintView() : null}*/}
            </View>
        )
    }


    _onBackAndroid() {
        if (isDownloading) {
            return true;
        } else if (this.state.fullScreen) {
            this._toggleOrientation(!this.state.fullScreen);
            return true;
        }
        return super.onBackAndroid();
    }

    /**
     * 视频功能工具栏
     * @private
     */
    _renderVideoToolBar() {
        console.log("    _renderVideoToolBar " + JSON.stringify(this.state.isPlaying));

        return (
            <View style={{
                backgroundColor: colors.white,
                flexDirection: 'row',
                paddingVertical: 10,
                justifyContent: 'space-around',
                //paddingHorizontal: 15
            }}>

                <CheckBoxButton
                    checked={this.state.isPlaying}
                    buttonStyle={styles.videoToolBtn}
                    buttonImage={[iconPlay, iconPause]}
                    disabled={this.state.isDataEmpty||this.state.isOpenSelectMode||this.state.currentItemOnlyShowImage}
                    buttonDisabledImage={!this.state.isPlaying?iconPlayDisabled:iconPauseDisabled}
                    onValueChange={(isPlaying) => {
                        console.log("CheckBoxButton 暂停/播放  checkValue  " + (!isPlaying));
                        if(!this.isConnectNet){
                            showToast(stringsTo("network_available_need_check"));
                            return;
                        }
                        this._startOrPause();
                    }}/>

                <CheckBoxButton
                    checked={this.state.muted}
                    buttonStyle={styles.videoToolBtn}
                    buttonImage={[iconVoiceOpen, iconVoiceClose]}
                    disabled={this.state.isDataEmpty||this.state.isOpenSelectMode||this.state.currentItemOnlyShowImage}
                    buttonDisabledImage={iconVoiceOpenDisabled}
                    onValueChange={_ => {
                        this.setState({muted: !this.state.muted});
                        IMILogUtil.uploadClickEventValue({HouseSound:this.state.muted?"1":"0"});
                        // this.videoPlayer.muted(!this.state.muted)
                    }}/>

                    <CheckBoxButton
                        buttonStyle={styles.videoToolBtn}
                        buttonImage={[iconSave]}
                        disabled={!this.state.currentItemSupportSave||this.state.isDownloading||this.state.isOpenSelectMode}
                        buttonDisabledImage={iconSaveDisabled}
                        onValueChange={() => {
                            IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, (status) => {
                                if (status === 0) {
                                    if(this.state.currentItemOnlyShowImage){
                                       this._saveImageToLocal();
                                    }else{
                                        this._saveVideoToLocal();
                                    }

                                } else if (status === -1) {
                                    showToast(stringsTo('storage_permission_denied'))
                                }
                            });
                        }}/>


                <CheckBoxButton
                    checked={this.state.fullScreen}
                    buttonStyle={styles.videoToolBtn}
                    buttonImage={[iconFull]}
                    disabled={!this.state.currentItemSupportSave || this.state.isOpenSelectMode || this.state.currentItemOnlyShowImage}
                    buttonDisabledImage={iconFullDisabled}
                    onValueChange={_ => {
                        this._toggleOrientation(!this.state.fullScreen);
                        IMILogUtil.uploadClickEventForCount("HouseFullScreen");
                    }}/>

            </View>
        )
    }

    _startOrPause() {
        console.log("_startOrPause  this.state.isPlaying " + this.state.isPlaying + "   this.mCurItem " + this.mCurItem);
        /***判断是否第一次播放，默认播放第一条数据**/
        if ( this.mCurItem == null && !this.state.isPlaying) {
            this._onItemPress(this.state.dataList.length > 0 ? this.state.dataList[0] : null, 0);
        } else {
            // this.videoPlayer.togglePlayPause();
            this.setState({isPlaying: !this.state.isPlaying});
        }
    }

   //进入删除模式暂停
    _pauseVideo(){
        this.setState({isPlaying: false});
    }

    //取消删除清空
    _clearDataSelect(){
        this.dataSelect=[];
        this.isOnClick = 1;
        this.setState({
            isSelectAll:false,
            enableDeleteBtn:false
        });
    }

    _toggleOrientation(fullScreen) {
        if(!this.mCurItem || this.mCurItem.bucket.indexOf("meta-free")!=-1){//未选择item时和只有云存图片时，不支持横竖屏切换
            return;
        }
        this.videoPlayer.toggleOrientation();
        console.log("_toggleOrientation   " + this.state.fullScreen + " checkValue " + fullScreen);
        this.setState({fullScreen: fullScreen});
        this.props.onClickFullScreen && this.props.onClickFullScreen(fullScreen);
    }


    /**
     * 事件展示列表
     * @returns {*}
     * @private
     */
    _renderListView() {
        if (!this.state.isCloudStorageValid && this.state.isSupportThumbWhenExpired && this.state.isDataEmpty) {
            return null;
        }
        //let dataList = this.state.dataList;
       // console.log(" _renderListView dataList  -> " + JSON.stringify(dataList));
        return (
            <XFlatList
                style={{
                   // height: '100%',
                    backgroundColor: "colors.white",
                    marginTop: this.state.fullScreen?getScreenWidth():0 //这个代码是为了避免iOS在横屏时有大面积泛白区域下移的问题
                }}
                data={this.state.dataList}
                onMomentumScrollBegin={() => {
                    console.log('eventStorageSessions onMomentumScrollBegin');
                    this.onEndReachedCalledDuringMomentum = true;
                }}
                /*onScrollBeginDrag={() => {  //开始滑动列表，先于onMomentumScrollBegin回调
                    console.log("onScrollBeginDrag");
                    this.onEndReachedCalledDuringMomentum = true;
                }}*/
                /*onEndReached={() => { //上拉列表到底可正常触发，但是拉个8遍左右，这玩意会回调3次
                    alert("onEndReached---------"+this.num++);
                    console.log('eventStorageSessions onMomentumScrollBegin ' + this.onEndReachedCalledDuringMomentum);
                    //判断是否用户主动操作滚动
                    if (!this.onEndReachedCalledDuringMomentum) {
                        this._queryDataList(false);
                        this.onEndReachedCalledDuringMomentum = false;
                    } else {
                        this.refreshList && this.refreshList.refreshLoaded(true, false, true);
                    }
                }}*/
                onRefresh={() => this._queryDataList(true)}
                refreshing={this.state.refreshing}
                onEndReachedThreshold={0.1}
                onLoadMore={() => {
                    console.log('eventStorageSessions onMomentumScrollBegin ' + this.onEndReachedCalledDuringMomentum);
                    //判断是否用户主动操作滚动
                     if (this.onEndReachedCalledDuringMomentum) {
                        this._queryDataList(false);
                        this.onEndReachedCalledDuringMomentum = false;
                    } else {
                        this.refreshList && this.refreshList.refreshLoaded(true, false, true,false);
                    }
                }}

                refreshStatus={{
                    PreLoading: {text: I18n.t('housekeeping_no_event')},//不传值默认是first loading，在当天视频为空且续费成功回来后必现
                    RefreshingData: {text: I18n.t('commLoadingText')},
                    NoData: {text: I18n.t('housekeeping_no_event')},
                    LoadFailure: {text: I18n.t('commLoadingFailText')},
                    //列表底部提示文案
                    LoadingMoreData: {moreText: I18n.t('commLoadingMoreDataText')},
                    NoMoreData: {moreText: I18n.t('commNoMoreDataText')},
                    LoadMoreFailure: {moreText: I18n.t('commLoadingClickText')},
                    NetException: {moreText: I18n.t('commLoadingFailText')},
                }}
                renderEmptyViewFunc={(status, isEmpty) => this._renderEmptyView(status, isEmpty)}

                onLayout={e => {
                    let height = e.nativeEvent.layout.height;
                    if (this.state.flatListHeight != height) {
                        this.setState({flatListHeight: height})
                    }
                }}
                ref={refreshList => this.refreshList = refreshList}

                renderItem={({item, index}) => this._renderItem(item, index)}/>
        );
    }

    //附着在下载按钮下方，显示下载进度的View
    _renderDownloadingView(){
       if(!this.state.isDownloading||this.state.dataList.length==0) return null; //对dataList判空是防止下载时切换到无数据日期



        return (<View style={{
                position: "absolute",
                top: topDistance, //竖屏时下载进度气泡的top为播放器高度到下载按钮底部的值
                right: 28,
                maxWidth: 200,
                alignItems: "flex-end"
            }}>
                <XImage style={{width: 5, height: 4, marginRight: rightDistance}} icon={iconBubbleTop}/>
                <View style={{
                    marginTop:-0.3,
                    minWidth: 150,
                    height: 45,
                    backgroundColor: "#B2B2B2F2",
                    justifyContent: "center",
                    borderRadius: 6,
                    padding: 10 //之前为14，iOS上最后的%显示不全，原因未知
                }}>
                    <XText style={{fontSize: 12, color: "#FFFFFF"}}
                           text={this.state.downloadingProgress}/>
                </View></View>
        );

    }


    //数据为空时，页面的刷新提示和加载为空的提示
    _renderEmptyView(status, isEmpty) {
        console.log(' _renderEmptyView  isEmpty ：' + isEmpty + " status " + JSON.stringify(status));
        let icon = isEmpty ? iconEmpty : null;
        return (
            <IMIDesignEmptyView rootStyle={{height: this.state.flatListHeight}} defaultIcon={icon}
                                defaultText={status.text}/>
        )
    }

    _assignTopSelectBarRoot = (component) => {
        this.topSelectBarRoot = component;
    };

    /**
     * 获取看护列表数据
     * @private
     */
    _getAlarmListData() {
        this.setState({ dataList: [],isDataEmpty:false,currentItemSupportSave:false});//清空当前数据,然而isDataEmpty为false是为了显示FlatList的loading和提示
        this._queryDataList(true);
    }

    /**
     * 绘制item view    **  可以进行子类重写 **
     * 根据产品需求 侦测事件ICON的优先级为 人形、人脸、宠物、车辆
     *              重合事件的时候如果出现人形/人脸 文案都是一句，没有出现人形/人脸再看宠物和车的文案
     * @param item
     * @param index
     * @returns {*}
     * @private
     */
    _renderItem = (item, index) => {
        console.log('_renderItem' + JSON.stringify(item));

        //根据是否是编辑模式打开不同的item UI
        if (this.state.isOpenSelectMode){ //渲染多选删除模式下的item
            if (this.isOnClick===2){
                if (this.state.isSelectAll){
                    this.dataSelect.push(item.sessionId);
                    this.dataSelect= [...new Set(this.dataSelect)];
                }else {
                    if (this.state.isSelectBtn){
                        this.dataSelect=[];
                    }
                }
            }else  if (this.isOnClick===1){
                if (this.state.isSelectAll){
                    this.dataSelect.push(item.sessionId);
                    this.dataSelect= [...new Set(this.dataSelect)];
                }else {
                    this.dataSelect=[];
                }
            }
            return(<XView>
                {this._renderItemContent(index,item,true)}
            </XView>);
        }else if(this.state.isOpenSelectMode==null){ //未设置isOpenSelect属性，默认为左滑删除
            return(<Swipeout right={[
                {
                    component: <TouchableOpacity onPress={() => this._deleteVideoItem(item, index)}>
                        <XImage style={{width: 40, height: 40, marginTop: 25}} icon={iconDeleteItem}/>
                    </TouchableOpacity>,
                    backgroundColor: "#fff"
                }
            ]} backgroundColor={"#fff"} buttonWidth={60} auto={true} close={this.state.closeDelete}>
                { this._renderItemContent(index,item)}
            </Swipeout>);
        }else{ //支持多选删除非编辑模式下的UI
            return(<XView>
                {this._renderItemContent(index,item)}
            </XView>);
        }
    };

    /**
     * 根据demolition的值获取对应异常事件的字符串
     * @param demolition
     * @returns {*}
     * @private
     */
    _getWarnEvent(demolition){
        let eventTxt = "";
        if(demolition.indexOf("FireWarning") != -1){
            eventTxt = stringsTo('abnormal_event_fire');
        }else if(demolition.indexOf("LockWasDamaged") != -1){
            eventTxt = stringsTo('abnormal_event_damaged');
        }else if(demolition.indexOf("AntiPassword") != -1){
            eventTxt = stringsTo('abnormal_event_anti_password');
        }else if(demolition.indexOf("AntiFingerprint") != -1){
            eventTxt = stringsTo('abnormal_event_anti_fingerprint');
        }else if(demolition.indexOf("WrongPassword") != -1){
            eventTxt = stringsTo('abnormal_event_password');
        }else if(demolition.indexOf("WrongFingerprint") != -1){
            eventTxt = stringsTo('abnormal_event_fingerprint');
        }else if(demolition.indexOf("WrongNFC") != -1){
            eventTxt = stringsTo('abnormal_event_nfc');
        }else if(demolition.indexOf("WrongFace") != -1){
            eventTxt = stringsTo('abnormal_event_face');
        }else if(demolition.indexOf("WrongMultipleMethods") != -1){
            eventTxt = stringsTo('abnormal_event_multiple');
        }else if(demolition.indexOf("LeavingOpenTimeout") != -1){
            eventTxt = stringsTo('abnormal_event_door_time_out');
        }else {
            eventTxt = stringsTo('abnormal_event');
        }
        return eventTxt;
    }
    /*渲染每个item的内容*/
    _renderItemContent(index,item,isSupportMultiplySelect=false) {
        //thumbPath 为缩略图云端存储路径 非能真实使用的url

        let {startTime, endTime, thumbPath, thumbKey, duration, sessionId, move, humanShape, humanFace, linger, animal, goods, cry, demolition,bucket,bell} = item;

        let formatStarTime = dateFormat(new Date((startTime)), "HH:mm:ss");
        let topLineWidth = index === 0 ? 0 : CONST.onePixel;

        //目前只支持两种不是移动则是逗留 ，只要存在人形/人脸就认为是逗留
        let people = (humanFace || linger || humanShape);
        let eventText = people ? stringsTo('lingerChangeText') : stringsTo('alarm_change_string');
        //添加按门铃文案
        eventText = bell?stringsTo('doorbellPressTitle'):eventText;

        console.log("jeff","demolition = ",demolition);
        if ((this.selectType === INTELLIGENT_TYPE.stay || this.selectType === INTELLIGENT_TYPE.bell)
            && demolition != undefined && !demolition.toString().equals("")) {
            return ;
        }
        if(demolition != undefined && !demolition.toString().equals("")){
            eventText = this._getWarnEvent(demolition);
        }
        console.log("jeff","eventText = ",eventText);
        let isCurrentPlaying = this.mCurItem && this.mCurItem.sessionId === sessionId;


        let onlyShowImageThumb = item.bucket.indexOf("meta-free")!=-1;//"sg-ipc031-sub-meta-free"//"ipc031-meta-free";
        return (
            <XView key={index} style={styles.itemParent}
                   onPress={isSupportMultiplySelect ? this._onItemPressSelectMode.bind(this,item) : this._onItemPress.bind(this, item, index)}>

                {/*左侧时间轴or左侧勾选按钮*/}
                {isSupportMultiplySelect ?
                    <XView style={{justifyContent: 'center', alignItems: 'center'}}>
                        <XImage style={{width: 25, height: 25}}
                                icon={this.dataSelect.includes(sessionId) ? iconChoose : iconChooseUn}/>
                    </XView>
                    : <XView style={{justifyContent: 'center', alignItems: 'center'}}>
                        <RNLine vertical={true} style={{flex: 1, width: topLineWidth}}/>
                        <XImage style={{width: 14, height: 14}} icon={isCurrentPlaying ? iconBlueDot : iconDot}/>
                        <RNLine vertical={true} style={{flex: 1}}/>
                    </XView>}


                {/*看护类型与时间点*/}
                <XView style={{flex: 1, justifyContent: 'center'}}>

                    <XView style={{flexDirection: 'row', alignItems: 'center'}}>

                        <XText
                            style={[styles.itemTitle, {color: isCurrentPlaying ? "#496EE0" : colors.black}, Typography.textFix]}
                            text={formatStarTime}/>
                        {
                            people && !onlyShowImageThumb ? <XImage
                                style={{marginLeft: 5, width: 14, height: 14}}
                                icon={iconPeople}/> : null
                        }
                    </XView>

                    {onlyShowImageThumb ? null :
                        <XText style={[styles.itemDesc, {color: isCurrentPlaying ? "#496EE0" : colors.black}]}
                               numberOfLines={4} text={eventText}/>}
                </XView>
                {/*右侧图片显示组件*/}
                <PlayingImiCloudImageRootView isShow={onlyShowImageThumb ? false : isCurrentPlaying} source={{
                    "productKey": LetDevice.model,
                    "dataSource": item
                }}/>

            </XView>
        );
    }

    /**
     * 事件选择监听按下,如果play为false，只装载资源，不播放
     * @param item
     * @param index
     * @param play
     */
    _onItemPress(item, index, play = true){
        if (isEmpty(item)) {
            return
        }
        IMILogUtil.uploadClickEventForCount("VideoBrowserNumber");

        //再次点击已经选中的视频item
        let repeatSelect = this.mCurItem && this.mCurItem.sessionId === item.sessionId && item.bucket.indexOf("meta-free")==-1;
        if(repeatSelect){
            //点击已经选中的视频,进行暂停或者播放操作。防止视频播放中再点击，显示loading但正常播放(#10144)
            if(!this.isConnectNet){
                showToast(stringsTo("network_available_need_check"));
                return;
            }
            this._startOrPause();
            return;
        }
        this.isCompletion = false;
        this.mCurItem = item;
        this.mCurIndex = index;
        //清空上一次下載所用緩存变量
        this.mCurItemLocal = null;

        this.setState({
            progressDuration:0,
            progressCurrentTime:0,
            isPlayFromBeginning:false,
            dataSource: item,
            isPlaying: play,
            indicatorLoading: true,
            showErrorView: false,
            showVideoCover:false,
            showCover: false,
            dataSourceImageItem: item,
            currentItemOnlyShowImage: item.bucket.indexOf("meta-free")!=-1,
            currentItemSupportSave:IMIImageDownLoader.instance.getCacheValue(item.thumbPath)!=undefined //&&item.bucket.indexOf("meta-free")!=-1
        },()=>this.setState({ progressDuration:-1, progressCurrentTime:-1}));
    };

    /**
     * 编辑模式下列表item的点击事件
     * @param item
     * @private
     */
    _onItemPressSelectMode (item){
        this.dataSelect.includes(item.sessionId)?this.dataSelect.splice(this.dataSelect.indexOf(item.sessionId),1): this.dataSelect.push(item.sessionId);
        this.dataSelect= [...new Set(this.dataSelect)];
        //this.mCurItem = item;
          this.setState({
              refreshing:true,
           //   dataList:item,
            //  dataSource: item,
          });

        this.isOnClick=2;
        this.setState({
            isSelectAll:false,
            isSelectBtn:false,
        });

        if(this.dataSelect.length==this.state.dataList.length){ //点击后，勾选了所有记录
            this.setState({isSelectAll:true,enableDeleteBtn:true});
        }else if(this.dataSelect.length==0){
            this.setState({enableDeleteBtn:false});
        }else{
            this.setState({enableDeleteBtn:true});
        }

    };

    /**
     * 删除多选视频
     * @param item
     * @param index
     */
    _deleteMultiplyVideoItem(){
        let isPlayingInSelectData = false; //选中的数据是否包含正在播放的视频
        //如果删除的是正在下载的
        if(this.dataSelect.includes(this.downloadingSessionId)){
            showToast(stringsTo('downloadingDeleteError'));
            return;
        }
        if(this.mCurItem&&this.dataSelect.includes(this.mCurItem.sessionId)){
            isPlayingInSelectData = true;
        }
        showLoading(stringsTo('commWaitText'), true);
        IMIAlarmEventCloudApi.getInstance().deleteVideoData(LetDevice.model, LetDevice.deviceID, moment(this.timeSelect).year() + "",
            (moment(this.timeSelect).month() + 1 + "").padStart(2, '0'), this.dataSelect).then((resp) => {
            let newEventData = this.state.dataList.filter((vo) => {
                if ( this.dataSelect.includes(vo.sessionId)) {
                    return false;
                }
                return true;
            });

            //刷新显示删除后的数据
            this.setState({dataList: newEventData}, () => {
                //如果删除了正在播放的数据，先默认加载第一个视频准备播放
                if(isPlayingInSelectData&&newEventData.length>0){
                    this.setState({isPlayFromBeginning:true});
                    //TODO 优化：删除后如果播放的index还在，仍然播放那个index，如果没有了，则播放最后一个
                    if(newEventData.length-1>=this.mCurIndex){ //删除后如果播放的index还在，仍然播放那个index
                        this._onItemPress(newEventData[this.mCurIndex],this.mCurIndex,false);
                    }else{
                        this._onItemPress(newEventData[newEventData.length-1],newEventData.length-1,false);
                    }

                }
                //多选编辑模式下
                this.props.dataListLengthChangeListener && this.props.dataListLengthChangeListener(newEventData.length);
            });

            if(newEventData.length==0){ //本页数据删除完，重新加载数据
                //首先更新flatListHeight值，方便emptyView可以居中显示
                this.setState({flatListHeight:getScreenHeight()/2},()=>{
                    this.queryDataAfterDeletallAll = setTimeout(()=>{ //不加延时，直接拉取到的是删除前的数据
                        showLoading(false);
                        showToast(stringsTo('delete_success'));
                        this._queryDataList(true);
                        this.queryDataAfterDeletallAll && clearTimeout(this.queryDataAfterDeletallAll);
                    },1600);
                });
            }else{
                showLoading(false);
                showToast(stringsTo('delete_success'));

            }


            this.dataSelect=[];
            this.setState({
                isSelectAll:false,
                isSelectBtn:false,
                enableDeleteBtn:false
            });
        }).catch(error => {
            console.log('deleteVideoData catch -> ' + JSON.stringify(error))
            showLoading(false);
         //   showToast(stringsTo('delete_failed'));
        });
    }


    /**
     * 删除指定视频，单个删除
     * @param item
     * @param index
     */
    _deleteVideoItem(item, index){
        let {startTime, sessionId} = item;
        //如果删除的是正在下载的
        if(sessionId == this.downloadingSessionId ){
            showToast(stringsTo('downloadingDeleteError'));
            return;
        }

        showLoading(stringsTo('commWaitText'), true);
        IMIAlarmEventCloudApi.getInstance().deleteVideoData(LetDevice.model, LetDevice.deviceID, moment(startTime).year() + "",
            (moment(startTime).month() + 1 + "").padStart(2, '0'), [sessionId]).then(_ => {
            let deleteIndex = 0;
            let newEventData = this.state.dataList.filter((vo, index) => {
                if (item.startTime == vo.startTime && item.sessionId == vo.sessionId) {
                    deleteIndex = index;
                    return false;
                }
                return true;
            });

            this.setState({dataList: newEventData,isDataEmpty:newEventData==0, closeDelete: true});//isPlaying: false, dataSource: {}});
            showLoading(false);
            showToast(stringsTo('delete_success'));

            // 如果删除的是正在播放的
            if(this.mCurItem&&this.mCurItem.sessionId==sessionId){
                if(newEventData.length==0){ //视频列表清空，播放器也要重置

                }else if(newEventData.length==1){ //删除有只有一个视频了，播放第一个
                    this.setState({isPlayFromBeginning:true});
                    this.mCurItem = newEventData[0];
                    this.mCurIndex = 0;
                    this._onItemPress(this.mCurItem, this.mCurIndex,false);
                }else if(newEventData.length==index){//删除的是最后一个,则依然播放最后一个
                    this.setState({isPlayFromBeginning:true});
                    this.mCurItem = newEventData[index-1];
                    this.mCurIndex = index-1;
                    this._onItemPress(this.mCurItem, this.mCurIndex,false);
                }else if(newEventData.length>index){ //依然默认选中index的视频，但是不播放
                    this.setState({isPlayFromBeginning:true});
                    this.mCurItem = newEventData[index];
                    this.mCurIndex = index;
                    this._onItemPress(this.mCurItem, this.mCurIndex,false);
                }
            }


        }).catch(error => {
            console.log('deleteVideoData catch -> ' + JSON.stringify(error))
            showLoading(false);
            showToast(stringsTo('delete_failed'));
        });
    }


    /**
     * 保存当前播放视频到本地相册中
     * @private
     */
    _saveVideoToLocal() {
        if (this.mCurItem == null) {
            return;
        }
        IMILogUtil.uploadClickEventForCount("HouseSave");
        //showLoading(stringsTo('commWaitText'), true);
        let isDownloadingImage = this.state.currentItemOnlyShowImage; //下载的是图片
        isDownloading = true;
        this.setState({
            isDownloading :true
        });

        let {bucket, duration, startTime, endTime, sessionId} = this.mCurItem;
        this.downloadingSessionId = sessionId;

        let videoInfo = {
            bucket: bucket,
            duration: duration,
            //使用string 接受 ，底层无法传递long
            startTime: startTime.toString(),
            endTime: endTime.toString(),
            sessionId: sessionId,
            productKey: LetDevice.model,
            deviceId: LetDevice.deviceID,
        };

        let fileName = sessionId + (isDownloadingImage ? ".jpeg" : IMIVideoUtils.SUFFIX_MP4);
        IMIDownload.downloadImiCloudVideo(sessionId, videoInfo, fileName);

        this.mDownloadVideoListener = DeviceEventEmitter.addListener(sessionId, (event) => {

            if (event.status === IMIDownload.STATUS_DOWNLOADING) {
                this.setState({
                    downloadingProgress: event.progress == 100 ? stringsTo("video_downloaded")+"  100%"
                        : stringsTo("video_downloading") + event.progress + "%"
                });
            }
            if (event.status === IMIDownload.STATUS_CANCEL ||
                event.status === IMIDownload.STATUS_SUCCESS ||
                event.status === IMIDownload.STATUS_ERROR) {
                //用过一次必须释放
                this.mDownloadVideoListener && this.mDownloadVideoListener.remove();
                this.mDownloadVideoListener = null;
            }
            if (event.status === IMIDownload.STATUS_CANCEL ||
                event.status === IMIDownload.STATUS_ERROR) {
               // showLoading(stringsTo('commWaitText'), false);
                this.setState({
                    isDownloading :false,
                    downloadingProgress:"0%"
                });
                return;
            }

            if (event.status === IMIDownload.STATUS_SUCCESS) {
                let downloadFilePath = event.downloadPath + '/' + event.fileName;
                //下載成功后保存视频到系统路径
                IMIFile.saveVideoToPhotosAlbum(downloadFilePath, LetDevice.deviceID).then(r => {
                    console.log('_saveVideoToLocal saveVideoToPhotosAlbum  videoListMixer then  r -> ', r);
                    isDownloading = false;
                    this.setState({
                        isDownloading: false,
                        downloadingProgress: stringsTo("video_downloading") + "  0%"
                    });

                    showToast(stringsTo('saved_system_album'));
                    this.downloadingSessionId = 0;
                }).catch(e=>{
                    isDownloading = false;
                    this.setState({
                        isDownloading: false,
                        downloadingProgress: stringsTo("video_downloading") + "  0%"
                    });
                    showToast(stringsTo('save_failed'));
                    this.downloadingSessionId = 0;
                });
            }
        });
    }

    /*保存图片到相册*/
    _saveImageToLocal(){
        if (this.mCurItem == null) {
            return;
        }
        isDownloading = true;
        this.setState({
            isDownloading :true
        });

        let {bucket, duration, startTime, endTime, sessionId,thumbPath} = this.mCurItem;
        this.downloadingSessionId = sessionId;
        let imageCachePath = IMIImageDownLoader.instance.getCacheValue(thumbPath);
        IMIFile.saveImageToPhotosAlbum(imageCachePath, LetDevice.deviceID).then(r => {
            console.log('_saveVideoToLocal saveVideoToPhotosAlbum  videoListMixer then  r -> ', r);
            isDownloading = false;
            // showLoading(false);
            this.setState({
                isDownloading: false,
                downloadingProgress: stringsTo("video_downloading") + "  0%"
            });

            showToast(stringsTo('saved_system_album'));
            this.downloadingSessionId = 0;
        });
    }

    /**
     * 处理当前播放器事件
     */
    _doHandleOnEventChange = (data) => {
        console.log(TAG + 'doHandleOnEventChange ', data + "  IMIPlayerViewBase.EVENT_PLAYER.RN_PLAYER_EVENT_ON_MUTED.code" + IMIPlayerViewBase.EVENT_PLAYER.RN_PLAYER_EVENT_ON_MUTED.code);
        if (!data.hasOwnProperty('extra')) {
            return
        }
        let {extra = {}, code = undefined} = data;
        //如果缓存完毕
        if (code === PLAYER_EVENT_CODE.PLAYER_EVENT_ON_BUFFER_FINISH) {
            this.mCurItemLocal = extra;
            console.log('_saveVideoToLocal  ', this.mCurItemLocal);

            //判断 此处下载完成后 用户是否需要保存视频
            if (this.waitingSaveVideo) {
                this._saveVideoToLocal();
            }
        }else if(code == PLAYER_EVENT_CODE.PLAYER_EVENT_ON_PREVIEW){ //开始播放画面了
            this.setState({currentItemSupportSave:true,indicatorLoading:false,showVideoCover:false,showErrorView:false}); //都可以播放了，当然可以缓存啦
        }else if(code == PLAYER_EVENT_CODE.PLAYER_EVENT_ON_BUFFERING){
            this.setState({indicatorLoading:true,showVideoCover:false,showErrorView:false});
        }
    };

    /**
     * 播放器内部状态变化监听
     */
    _onInteriorStateCall = (data) => {
        let {extra = {}, code = undefined} = data;
        //如果缓存完毕
        if (code === IMIPlayerViewBase.EVENT_PLAYER.RN_PLAYER_EVENT_ON_START().code ||
            code === IMIPlayerViewBase.EVENT_PLAYER.RN_PLAYER_EVENT_ON_STOP().code) {
            this._startOrPause();
        } else if (code === IMIPlayerViewBase.EVENT_PLAYER.RN_PLAYER_EVENT_ON_MUTED().code) {
            let {data} = extra;
            this.setState({muted: data.muted});
            console.log(TAG + 'doHandleOnEventChange data.muted ', data.muted);
        }
    };

    //开启编辑模式后，底部的全选和删除按钮
    _bottomOfTheChoice() {
        if (!this.state.isOpenSelectMode) {
            return null;
        }
        return (
            <View style={styles.bottomChoiceTab}>
                <RNLine style={{height: 0.5}}/>
                <View style={styles.bottomChoice}>

                    <TouchableOpacity onPress={() => {
                        console.log("底部---------全选-----");
                        this.setState({
                            isSelectAll: this.state.isSelectAll ? false : true,
                            isSelectBtn: true,
                            enableDeleteBtn: this.state.isSelectAll ? false : true
                        });
                    }}>
                        <View style={styles.bottomChoiceView}>
                            {this.state.isSelectAll ?
                                <Image source={require("../../../resources/images/icon_check_select.png")}
                                       style={styles.bottomChoiceImage}/> :
                                <Image source={require('../../../resources/images/icon_un_check.png')}
                                       style={styles.bottomChoiceImage}/>}
                            {!this.state.isSelectAll ? <Text adjustsFontSizeToFit={true}
                                                             style={styles.bottomChoiceText}>{stringsTo("select_all")}</Text> :
                                <Text style={styles.bottomChoiceText}>{stringsTo("unselect_all")}</Text>}
                        </View>

                    </TouchableOpacity>


                    <TouchableOpacity onPress={() => {
                        console.log("底部---------删除-----");
                        this._deleteMultiplyVideoItem();
                    }} disabled={!this.state.enableDeleteBtn}>
                        <View style={styles.bottomChoiceView}>
                            <Image source={this.state.enableDeleteBtn?require('../../../resources/images/icon_delete_black.png')
                                : require('../../../resources/images/icon_delete_black_disable.png')}
                                   style={styles.bottomChoiceImage}/>
                            <Text adjustsFontSizeToFit={true}
                                  style={[styles.bottomChoiceText,{color:this.state.enableDeleteBtn?"#333333":"#3333334c"}]}>{stringsTo("delete")}</Text>
                        </View>
                    </TouchableOpacity>

                </View>
            </View>

        )

    }

    /***           系統函數   START    ****/

    componentWillUnmount() {
        super.componentWillUnmount();
        this._subscribeFocus && this._subscribeFocus();
        this._subscribeBlur && this._subscribeBlur();
        this._enterBackground && this._enterBackground.remove();
        this._enterForeground && this._enterForeground.remove();
        //用过一次必须释放
        this.mDownloadVideoListener && this.mDownloadVideoListener.remove();
        this.mDownloadVideoListener = null;
        IMIImageDownLoader.instance.release();
        this.backHandler && this.backHandler.remove();
        this.unsubscribe();
        Orientation.removeOrientationListener(this._orientationDidChange);
        this.delayFullScreen && clearTimeout(this.delayFullScreen);
    }

    componentWillMount() {
        super.UNSAFE_componentWillMount()
        //this.backHandler && this.backHandler.remove();
        this.unsubscribe = NetInfo.addEventListener(state => {
           // alert(JSON.stringify(state));
            this.isConnectNet = state.isConnected;
            if (!state.isConnected) {
                showToast(stringsTo('network_not_connected'));
            }
            if(!state.isConnected && state.type== NetInfoStateType.none){ //断网了,如果在播放，则停止
                this.state.isPlaying && this.setState({isPlaying: false});
                this.autoStopForNetError = true;
            }else{
                this.autoStopForNetError&& !this.state.isPlaying && pageInForeground && this.setState({isPlaying: true});
                this.autoStopForNetError = false;
            }

        });
    }

    /**
     * 刷新日历哪天存在视频UI
     * @private
     */
    _updateCalendarInfo(monthData) {
        console.log("获取蓝点数据", 'componentDidMount getCalendarInfo  this.reqTime -> ', this.reqTime + " monthData ", monthData);

        const timeArr = monthData ? monthData.split(" ")[0].split("-") : this.reqTime.split(" ")[0].split("-");
        const year = timeArr[0];

        IMIAlarmEventCloudApi.getInstance().getCalendarInfoV2(LetDevice.model, LetDevice.deviceID, year).then(data => {
            console.log('获取蓝点数据---------原始数据>', data);

            let timeDotSelect = {};

            const style = {marked: true, dotColor: imiThemeManager.theme.primaryColor};

            let currentDayStyle = {
                [this.reqTime]: {
                    selected: true,
                    marked: false,
                    disableTouchEvent: true,
                    selectedColor: imiThemeManager.theme.primaryColor
                },
            };
            Object.getOwnPropertyNames(data).forEach((key) => {
                const value = data[key];
                value && Object.assign(timeDotSelect, {[key]: style});
                console.log('获取蓝点数据--------遍历>',key + '---' + data[key])
            });
            Object.assign(timeDotSelect, currentDayStyle);

            this.setState({dateTimeDotSelect: timeDotSelect});

            // alert(">>>>" + JSON.stringify(this.state.dateTimeDotSelect));
            console.log('获取蓝点数据--------遍历>',  timeDotSelect)
        }).catch(error => {
            console.log(TAG, 'componentDidMount getCalendarInfo catch -> ', JSON.stringify(error))
        });
    }

    _orientationDidChange = (orientation) => {
        if(!pageInForeground){
            return;
        }
        if (orientation === 'LANDSCAPE') {
            if (!this.state.fullScreen) {
                //不先全屏和不加延时，直接走点击全屏按钮逻辑无法正确切换全屏
                Orientation.lockToPortrait();
                this.delayFullScreen = setTimeout(()=>{
                    this._toggleOrientation(true);
                    this.delayFullScreen && clearTimeout(this.delayFullScreen);
                },50);
            }
        } else {
            this.state.fullScreen && this._toggleOrientation(false);
        }
    };

    componentDidMount(){
        console.log(`componentDidMount componentDidMount : `);
        this.props.triggerRef && this.props.triggerRef(this);
        this.backHandler = BackHandler.addEventListener("hardwareBackPress", _ => this._onBackAndroid());

        this._updateCalendarInfo();
        //需要判空处理 防止部分项目不需要 未添加
        console.log("this.props.navigation:" + this.props.navigation);
        if (this.props.isOpenSelectMode){
            if (this.dataSelect.length > 0){
                this.setState({enableDeleteBtn:true})
            }else {
                this.setState({enableDeleteBtn:false})
            }
        }

        /*本页面获得焦点*/
        this._subscribeFocus = this.props.navigation.addListener('focus', () => {
            console.log(`addListener _subscribeFocus : `);
            pageInForeground = true;
            this.needResumePlay && this.setState({isPlaying: true});
            this.needResumePlay = false;

            /*插件进入前台监听*/
            this._enterForeground = IMINativeLifeCycleEvent.enterForegroundListener.addListener(() => {
                pageInForeground = true;
            });
            /*插件进入后台监听*/
            this._enterBackground = IMINativeLifeCycleEvent.enterBackgroundListener.addListener(() => {
                pageInForeground = false;
            });

            Orientation.addOrientationListener(this._orientationDidChange);

        });

        /*本页面失去焦点*/
        this._subscribeBlur = this.props.navigation.addListener('blur', () => {
            console.log(`addListener _subscribeBlur : `);
            pageInForeground = false;
            if (this.state.isPlaying) {
                this.setState({isPlaying: false});
                this.needResumePlay = true;
            }

            //*跳转其他页面，移除进入前后台的监听*   因为app进入前后台，手机息屏亮屏也会走这些逻辑
            this._enterBackground && this._enterBackground.remove();
            this._enterForeground && this._enterForeground.remove();

            Orientation.removeOrientationListener(this._orientationDidChange);

        });


        //云存失效、设备不支持云存到期显示图片，那么列表展示区域为空且有文字提示，所以没必要去拉看家数据了
        if (this.state.isCloudStorageValid || this.state.isSupportThumbWhenExpired) {
            this._getAlarmListData();
        }

        /*手机横竖屏监听，仅对iOS生效*/
        //注释掉 与focus走重复了， //bug IMI_HMI510_A01-2361 iOS看家视频,无法进入全屏模式
        //Orientation.addOrientationListener(this._orientationDidChange);
    }

    /***           系統函數   END    ****/
}


const styles = StyleSheet.create({
    container: {
        flex: 1,
        //position: 'absolute',
    },

    itemParent: {
        alignItems: 'center',
        paddingHorizontal: 14,
        flexDirection: 'row',
        backgroundColor: colors.white
    },

    itemIcon: {
        borderRadius: 4, width: 140, height: 78,
    },

    itemTitle: {
        fontSize: 15,
        fontWeight: 'bold',
       // color: colors.black,
        paddingLeft: 14
    },

    itemDesc: {
        fontSize: 12,
        paddingLeft: 14,
        marginTop: 3,
        //color: colors.black,
    },

    videoToolBtn: {

        width: 30,
        height: 30,
    },

    bottomChoiceTab:{
        width: "100%",
        height: isIphoneXSeries()?70:60,
        backgroundColor: colors.white,
        flexDirection:'column'
    },
    bottomChoice:{
        backgroundColor: colors.white,
        flexDirection:'row' ,
        justifyContent:'center',
        alignItems:'center'
    },
    bottomChoiceView:{
        marginTop:5,
        justifyContent:'center',
        alignItems:'center',
    },
    bottomChoiceText:{
        minWidth:80,
        textAlign:"center",
        color:"#333333",
        fontSize:12,
    },
    bottomChoiceImage:{
        width:30,
        height:30,
    },
});

