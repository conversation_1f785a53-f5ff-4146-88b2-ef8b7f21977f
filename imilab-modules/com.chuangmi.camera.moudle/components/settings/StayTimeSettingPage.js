/*
 * 作者：sunhongda
 * 文件：StayTimeSettingPage.js  模块：imihome_rn  项目：imihome_rn
 * 上海创米科技有限公司  http://www.imilab.com/
 * Copyright (c) 2020
 */

import React, {PureComponent} from 'react';

import {DeviceEventEmitter,StyleSheet, ScrollView, View} from 'react-native';

import {ChoiceItem, RoundedButtonView} from '../../../../imilab-design-ui'
import NavigationBar from "../../../../imi-rn-commonView/NavigationBar/NavigationBar"
import {XText} from 'react-native-easy-app';
import {LetDevice} from "../../../../imilab-rn-sdk";
import {showToast} from "../../../../imilab-design-ui";
import I18n from "../../../../globalization/Localize";

export default class StayTimeSettingPage extends PureComponent {

    constructor(props, context) {
        super(props, context);
        this.state = {
            stayTimeValue: null, //当前保存的停留时长的设置值
            StayTimeSelectedIndexArray: [false, false, false], //三个逗留时长的选中情况
            selectIndexArray: [0, 0, 0, 0], //一般耗电的选项选中状态
            enableNextButton: false
        }
    }

    componentDidMount() {
        let stateProps = {};
        LetDevice.getPropertyCloud('PirDetectionMechanism').then((value) => { //取得的值是字符串'0'，'-1'，'5'...
            console.log('getPropertyCloud---------' + value + typeof (value));
            value = parseInt(value);
            stateProps.stayTimeValue = value;
            if (value == 0) {
                stateProps.StayTimeSelectedIndexArray = [true, false, false];
                stateProps.enableNextButton = true;
            } else if (value == -1) {
                stateProps.StayTimeSelectedIndexArray = [false, false, true];
                stateProps.enableNextButton = true;
            } else if (value >= 5 && value <= 20) {
                stateProps.StayTimeSelectedIndexArray = [false, true, false];
                switch (value) {
                    case 5:
                        stateProps.selectIndexArray = [1, 0, 0, 0];
                        stateProps.enableNextButton = true;
                        break;
                    case 10:
                        stateProps.selectIndexArray = [0, 1, 0, 0];
                        stateProps.enableNextButton = true;
                        break;
                    case 15:
                        stateProps.selectIndexArray = [0, 0, 1, 0];
                        stateProps.enableNextButton = true;
                        break;
                    case 20:
                        stateProps.selectIndexArray = [0, 0, 0, 1];
                        stateProps.enableNextButton = true;
                        break;
                    default:
                        break;
                }
            }
            this.setState(stateProps);
        }).catch(error => {
            console.log(JSON.stringify(error))
        });
    }

    componentWillUnmount() {
        DeviceEventEmitter.emit('backFromGuidePage',{});
    }

    render() {
        global.navigation = this.props.navigation;

        return (<View style={{flex: 1, backgroundColor: "#FFFFFF"}}>

            {/*<NavigationBar title={I18n.t('stayTimeStr')} noLine={true}/>*/}
            <NavigationBar
                title={I18n.t('stayTimeStr')}
                left={[{key: NavigationBar.ICON.BACK, onPress: ()=>  this.props.navigation.pop()}]}
                right={[]}
            />
            <ScrollView>
            <XText style={styles.text} allowFontScaling={true}
                   text={I18n.t("houseDetectedPeopleStr")}
                   numberOfLines={1}/>

            <ChoiceItem title={I18n.t('peopleRecordPowerMoreStr')}
                        headIcon={require("../../resources/images/icon_person.png")}
                        subtitle={I18n.t('peopleRecordPowerMoreSubStr')}
                        containerStyle={{margin: 14}}
                        onlyChecked={true}
                        checked={this.state.StayTimeSelectedIndexArray[0]}
                        onValueChange={(value) => {
                            console.log("ChoiceItem------checked", value);
                            if (value) {
                                this.setState({
                                    StayTimeSelectedIndexArray: [true, false, false],
                                    stayTimeValue: 0,
                                    selectIndexArray:[0,0,0,0],
                                    enableNextButton: true
                                });
                            } else {
                                this.setState({
                                    stayTimeValue: null,
                                    enableNextButton: false
                                });
                            }
                        }}/>

            <ChoiceItem title={I18n.t('peopleRecordPowerCommonStr')}
                        headIcon={require("../../resources/images/icon_stay.png")}
                        subtitle={I18n.t('peopleRecordPowerCommonSubStr')}
                        containerStyle={{marginHorizontal: 14}}
                        onlyChecked={true}
                        checked={this.state.StayTimeSelectedIndexArray[1]}
                        showChoiceArray={true}
                        selectIndexArray={this.state.selectIndexArray}
                        choiceArrayDefaultIndex={LetDevice.model == "a1EVaCvJ43g"?1:2} //033默认选中10秒，031默认选15秒
                        onValueChange={(value) => {
                            console.log("ChoiceItem------checked", value);
                            if (!value) { //再次点击已经选中的项来取消本ChoiceItem的选中状态
                                this.setState({
                                    stayTimeValue: null,
                                    enableNextButton: false
                                });
                            }
                        }}
                        onSelectValueChange={(index) => {
                            console.log("ChoiceItem------indexSelected", index);
                            let selectedArray = [5, 10, 15, 20];
                            this.setState({
                                StayTimeSelectedIndexArray: [false, true, false],
                                stayTimeValue: selectedArray[index],
                                enableNextButton: true
                            });
                        }}/>

            <ChoiceItem title={I18n.t('peopleRecordPowerLessStr')}
                        headIcon={require("../../resources/images/icon_close.png")}
                        subtitle={I18n.t('peopleRecordPowerLessSubStr')}
                        containerStyle={{margin: 14}}
                        onlyChecked={true}
                        checked={this.state.StayTimeSelectedIndexArray[2]}
                        onValueChange={(value) => {
                            console.log("ChoiceItem------checked", value);
                            if (value) {
                                this.setState({
                                    StayTimeSelectedIndexArray: [false, false, true],
                                    stayTimeValue: -1,
                                    selectIndexArray:[0,0,0,0],
                                    enableNextButton: true
                                });
                            } else {
                                this.setState({
                                    stayTimeValue: null,
                                    enableNextButton: false
                                });
                            }
                        }}/>

            </ScrollView>

                <RoundedButtonView buttonText={I18n.t('account_next')}
                                   disabled={!this.state.enableNextButton}
                                   buttonStyle={{margin: 14}}
                                   onPress={() => {
                                       let params = {PirDetectionMechanism: this.state.stayTimeValue};
                                       console.log('逗留时长-设置参数', JSON.stringify(params));
                                       LetDevice.setPropertyCloud(JSON.stringify(params)).then(() => {
                                           console.log('逗留时长-设置成功，跳转到下一步');
                                           this.props.navigation.pop();
                                       }).catch(() => {
                                           showToast(I18n.t('firstSetStayTimeFailed'));
                                           this.props.navigation.pop();
                                       });

                                   }}/>



        </View>);
    }
}


const styles = StyleSheet.create({
    container: {
        flex: 1,
        alignItems: "center",
        backgroundColor: "transparent",
        borderWidth: 2,
    },
    text: {
        color: '#808080',
        fontSize: 12,
        fontWeight: "500",
        marginTop: 20,
        marginLeft: 14
    },
});
