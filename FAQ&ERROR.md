##调试中遇到错误总结：

### 1.启动报错 Error: listen EADDRINUSE: address already in use :::8081

   > 1.查询占用的id --打开命令行窗口输入 netstat -ano|findstr xxx（比如当前错误中的8081）
 
   > 2..再输入taskkill /f /t /im xxx（展示出来id）
   
 ### 2.win 系统下启动报错(基于RN 0.59版本)（error Invalid regular expression: /(.*\\__fixtures__\\.*...
   >   修改node_modules\metro-config\src\defaults\blacklist.js文件 var sharedBlacklist = [
                                                                /node_modules[\/\\]react[\/\\]dist[\/\\].*/,
                                                                /website\/node_modules\/.*/,
                                                                /heapCapture\/bundle\.js/,
                                                                /.*\/__tests__\/.*/
                                                              ]; 
   >  端口8081被占用win下使用命令：netstat -aon |findstr "8081 查看占用进程 ,找到对应 tskill PID 杀死进程
   
### 3. 'rm' 不是内部或外部命令，也不是可运行的程序 或批处理文件。
   >  你的电脑是windows 解决办法：把rm -rf 命令替换为 rimraf 命令 

### 4 yarn install报错 error This project's package.json defines "packageManager": "yarn@3.3.1". However the current global version of Yarn is 1.22.21.
   >  删除 "packageManager": "yarn@3.3.1",在运行命令 yarn install



### Android 启动项目流程 
  
   >* ### 1.1.1启动服务
      - 反向代理调试本地
      
      - adb reverse tcp:8081 tcp:8081 -适用于电脑无法开启热点
      
      - react-native start –host xxx.xxx.xx.xx –port 8081
      
      - 项目编译需要依赖RN工程  RN工程位于 git- >  [imihome_RN](<http://dev.imilab.com:14847/sunhongda/imihome_RN.git>}  [imihome_rn-2020.6.9](<http://dev.imilab.com:14847/yanmin/imihome_rn.git>} 将此功能clone 下来后放置上级目录平级于本工程)

   >* ###2.0项目相关接口SDK说明
     
### 报错为Unknown argument type '__attribute__' in method -[RCTAppState getCurrentAppState:error:]. Extend RCTConvert to support this type.
    
    `node_modules/react-native/React/base/RCTModuleMethod.mm` 这个位置，
    
    ```
    static BOOL RCTParseUnused(const char **input)
    {
      return RCTReadString(input, "__unused") ||
             RCTReadString(input, "__attribute__((__unused__))") ||
             RCTReadString(input, "__attribute__((unused))");
    }
    ```
    插入 `RCTReadString(input, "__attribute__((__unused__))") ||` 
### 点插件进首页 加载index到100%就会弹这个错误:Error: Unable to resolve module ./debugger-ui/debuggerWorker.aca173c4 from D:\xiaomiRN_project\imihome_rn\argus-imihome_rn/.
> 删除手机上的imilab智慧生活APP 重新安装解决了

### 主工程创建脚本npx react-native init imihome_rn --version 0.59.8

### 报错：ERROR  TypeError: Cannot read property 'style' of undefined, js engine: hermes 
> 在项目根目录下执行：git apply ./patches/react-native-swipeout+2.3.6.patch
### WIN环境执行git apply ./patches/react-native-swipeout+2.3.6.patch可能存在补丁无法应用问题（error: patch failed: node_modules/react-native-swipeout/dist/index.js:32 error: node_modules/react-native-swipeout/dist/index.js: patch does not apply）
   > 执行git apply --reject ./patches/react-native-swipeout+2.3.6.patch -允许补丁在无法直接应用时进行部分应用

### WIN环境打包踩坑（命令sh jenkinsBuild/jenkinsBuild.sh 060camera false false false false false false false false false)
1、确保有安装全局依赖react-native（没有的话可以执行npm i react-native -g 备注：脚本运行报错jenkinsBuild/jenkinsBuild.sh: line 114: react-native: command not found）
2、确保git安装目录的\usr\bin目录下有zip.exe、bzip2.dll（没有的话可以去https://sourceforge.net/projects/gnuwin32/files/链接中下载zip-3.0-bin.zip、bzip2-1.0.5-bin.zip）
3、确保有安装python（已确认python3.x版本可使用）
### MAC环境打包踩坑
1、  放开./node_modules/react-native/sdks/hermesc/osx-bin/hermesc 
2、 放开alias python='/usr/bin/python3'

### Linux环境踩坑
1、not found main.jsbundle.hbc 编译无文件 提示react-native depends on @react-native-community/cli for cli commands. To fix update your package.json to include:
  "devDependencies": {
    "@react-native-community/cli": "latest",
  }
  解决 npm install @react-native-community/cli -g
2、not zip  yum install zip -y

