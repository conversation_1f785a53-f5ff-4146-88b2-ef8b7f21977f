/**
 * HomePageLivePlayerComponent.js 首页直播模块组件
 *
 * @property {func} videoRef - 播放器videoView的ref
 * @property {func} navBar - 自定义导航条，
 * @property {NavigationBar.right} navBarRight-使用默认导航条，只需要传入右侧按钮
 * @property {LivePlayerToolBarView.moreItems} toolBarMoreItems 增加功能条的按钮
 * @property {LivePlayerToolBarView.moreItems} fullScreenToolBarMoreItems 横屏增加功能条的按钮
 * @property {func} videoSubView - 增加视频区域更多功能，
 * @property {JSON} lensCorrect 畸变校正{use: false,x: 0,y: 0}
 * @property {func} onVideoClick 视频区域点击事件
 * @property {string}  pageBackgroundColor 直播播放器下方空白页面的颜色
 *
 * 示例:
 * <HomePageLivePlayerComponent
 *  {...this.props}
 *  videoRef={ref => this.IMIVideoView = ref}
 *  navBar={(bps, isFullScreen) => this._renderPortraitScreenVideoViewArea(bps, isFullScreen)}>
 *  navBarRight={[]}
 *  videoSubView={(isFullScreen)=><View style={{position: 'absolute', bottom: 0,width:"100%",height:50,backgroundColor:'red'}}/>}
 *  {this._renderDoorArea()}
 * </HomePageLivePlayerComponent>
 *
 * @author: yanmin
 * @date: 2020/11/25
 */

import React, {Component} from 'react';
import {
    View,
    Text,
    BackHandler,
    ActivityIndicator,
    StyleSheet,
    TouchableOpacity,
    NativeModules,
    findNodeHandle,
    Dimensions,
    StatusBar,
    Animated,
    Image,
    ScrollView,
    TouchableWithoutFeedback,
    Modal
} from 'react-native';
import {IMIGotoPage, LetDevice,IMIPackage,IMIStorage} from "../../imilab-rn-sdk";
import IMICameraVideoView, {CAMERA_PLAYER_MODE} from "../../imilab-rn-sdk/native/camera-kit/IMICameraVideoView";
import IMIFile from "../../imilab-rn-sdk/native/local-kit/IMIFile";
import I18n, {stringsTo,locales} from "../../globalization/Localize";
import LivePlayerToolBarView from "../PlayerToolBarView/LivePlayerToolBarView";
import LivePlayerNewToolBarView from "../PlayerToolBarView/LivePlayerNewToolBarView";
import NavigationBar from "../NavigationBar/NavigationBar";
import Orientation from 'react-native-orientation';
import TouchableOpacityText from "../TouchableOpacityText/TouchableOpacityText";
import ModalView from "../ModalView/ModalView";
import {
    getScreenHeight,
    getScreenWidth,
    isAndroid,
    isIos,
    isIphoneXSeries
} from "../../imilab-rn-sdk/utils/Utils";
import PropTypes from 'prop-types';
import Utils from "../../imilab-rn-sdk/utils/Utils";
import IMIToast from "../../imilab-design-ui/src/widgets/IMIToast";
import TouchableWithoutFeedbackView from "../TouchableWithoutFeedbackView/TouchableWithoutFeedbackView";
import LivePlayerFullScreenToolBarView from "../PlayerToolBarView/LivePlayerFullScreenToolBarView";
import {XText} from "react-native-easy-app";
import {colors, RoundedButtonView, showLoading} from "../../imilab-design-ui";
import ImageButton from "../ImageButton/ImageButton";
import {PLAYER_EVENT_CODE} from "../../imilab-rn-sdk/native/camera-kit/IMIVideoView";
import IMIPermission from "../../imilab-rn-sdk/native/local-kit/IMIPermission";
import {showToast} from "../../imilab-design-ui/src/widgets/Loading";
import Toast from "react-native-root-toast";
import * as Typography from "../../imilab-design-ui/src/style/Typography";
import {timeFilter} from "../../imilab-rn-sdk/utils/DateUtils";
import {CONST} from "../../imilab-design-ui/src/style/ThemeManager";
import IMILog from "../../imilab-rn-sdk/native/local-kit/IMILog";
import IMILogUtil from "../../imilab-rn-sdk/native/local-kit/IMILogUtil";
import moment from "moment";
import {IMINativeLifeCycleEvent} from "../../imilab-rn-sdk/native/local-kit/IMINativeLifeCycle";
import DeviceTemplatesUtils from "../../imilab-rn-sdk/core/device/DeviceTemplatesUtils";
import {screenHeight, screenWidth} from "react-native-calendars/src/expandableCalendar/commons";
import GlobalUtil from "../../projects/com.chuangmi.camera.v2/src/utils/GlobalUtil";
import {LIVE_NETWORK_MODE} from "../../projects/com.chuangmi.camera.v2/Constants";
import CustomerServicePage from "../../projects/com.chuangmi.camera.v2/src/setting/CustomerServicePage";

const LIVE_PLAYER_STATUS = {
    PREPARED: 'prepared',//准备完毕，调用start即可播放
    LOADING: 'loading' ,
    PLAYING: 'playing',//正在播放
    PAUSE: "pause",
    ERROR: "error"
};
// import RNFS from 'react-native-fs';

Object.freeze(LIVE_PLAYER_STATUS);
const TMP_SNAPSHOT_PATH_PREFIX = `${IMIFile.storageBasePath}/tmp/`;
const appendZero = n => parseInt(n).toLocaleString({}, {minimumIntegerDigits: 2});

const VEDIO_RECORD_PATH = `${IMIFile.storageBasePath}/tmp/videorecord.mp4`;
let lastClickSnapPhoto = 0; //上一次点击截图的时间
let time;
let bpsArray = []; //存放最新5次网速值的数组
let timeErr10005=0;// 1005连接次数错误
const {width,height} = Dimensions.get('window');
let Screen_Width = width;
let Screen_Height = height;
let Screen_HalfHeight = height/2;
let StatusBarHeight = 70;
// const fixControlBarHeight = height > 600 ? 120 : 110;
const fixControlBarHeight = 110;
let mIsAutoHideVideoCtrView = true;
let hide_playToolBar_timeID = -1; //隐藏工具栏时间ID

let COMMON_HELP_URL="link://feedback/pages/CommonHelp";//帮助与反馈
let COMMON_REBIND_URL="link://app/pages/toWaitConnectPage";//帮助与反馈

export default class HomePageNewLivePlayerComponent extends Component {
    static LIVE_PLAYER_STATUS = LIVE_PLAYER_STATUS;

    constructor(props, context) {
        super(props, context);

        GlobalUtil.isRecording = false;

        this.state = {
            // qualityData: [ stringsTo("quality_sd"), stringsTo("quality_fhd"),stringsTo("quality_2k"),],
            qualityIndex: this.props.qualityData.length>0?this.props.qualityData.length-1:0,
            qualityVisible: false,
            bps: -1,
            isFullScreen: false,
            //BYH 2021年12月28日15:24:37修正 !LetDevice.category == "doorbell"-->LetDevice.category != "doorbell"
            //此判断就可以了，不用加pk限定，现在除了门铃进来打开声音其他都是默认静音
            mute: LetDevice.category != "doorbell", //门铃产品默认打开监听
            recording: false,
            calling: false,
            recordDuration: 0,
            showFullScreenTools: false,

            isLoading: true,
            isPlaying: false,
            showErrorView: false,
            showPauseView: false,
            errorCode: null,

            snapshotVisible: false,
            screenShotPath: null,
            isShowSecLandToolBar:false,
            isShowZoomScale:false,//展示缩放比例小窗
            zoomScale:1.0,
            saveScale:1.0,//本地保存缩放比例,20220913@byh 这个比例设置给播放器，不能使用zoomScale,否则会一直缩放
            showPlayToolBar:true,//显示竖屏工具栏
            snapImgType:-1,
            isShowFullScale:false,// 从全屏退出要重新设置scale
            isShowOffLineHelp:false,// 设备离线
            isShowErrorHelp:false,//连接失败帮助
        }
        this.isForegroundPage = true;//是否显示在前台
        this.isNewFullScreen = false;// 从后台进前台是否全屏
        this.startScaleTime = 0;
        this.isShowFullScale = false;
        this.fullScale = 1.0;
        this.liveNetMode = LIVE_NETWORK_MODE.RELAY;
        this.macAddress = "";//重新绑定、重新连接跳到配网页面，需要从个Mac地址过去
        this.isPrepare = false;//标记是否初始化过播放器
        //标记，视频开始播放的开始时间,单位毫秒
        this.liveVideoStartTime = 0;
        this.connectType = "",
        this.getStatusBarHeight();
    }

    static propTypes = {
        navBar: PropTypes.func,
        videoRef: PropTypes.func,
        navBarRight: PropTypes.array,

        toolBarMoreItems: PropTypes.array,
        videoSubView: PropTypes.func,

        coverView: PropTypes.func,
        loadingView: PropTypes.func,
        pauseView: PropTypes.func,
        errorView: PropTypes.func,
        sleepView: PropTypes.func,
        isSleepStatus: PropTypes.bool,//休眠状态
        isMove: PropTypes.bool,//全屏触摸转动云台
        isOnLine: PropTypes.bool,//在线状态
        isCalling:PropTypes.bool,//通话是否打开
        onLivePlayerStatusChange: PropTypes.func,

        fullScreenToolBarMoreItems: PropTypes.array,
        lensCorrect: PropTypes.shape({
            use: PropTypes.bool.isRequired,
            x: PropTypes.number,
            y: PropTypes.number
        }),
        dataSource: PropTypes.object,
        onVideoClick: PropTypes.func,
        pageBackgroundColor: PropTypes.string,
        qualityData:PropTypes.array,//清晰度数据
        albumName:PropTypes.string,//相册名称
        onCheckPermissionStatusChange:PropTypes.func, //回调是否处于权限检测状态中
        onVoiceCallErrorOccurred: PropTypes.func,
        componentContainerRef:PropTypes.func,  //回调组件容器的引用，方便调用者以此来获取容器的宽高等参数
        onDeviceStatusChange:PropTypes.func, // 回调设备当前的状态
        isShowPanoramicView:PropTypes.bool,//圆盘还是通话显示,056/059
        isCallingStatue:PropTypes.bool,//是否点击通话
        netConnected:PropTypes.bool,//是否断网,同步双端显示
        isDataUsage:PropTypes.bool,//是否开启流量保护
        onGetRecordStatus:PropTypes.func,//录像
        onGetQualityIndex:PropTypes.func,//选中录像第几个
        onGetZoomScale:PropTypes.func,//获取缩放比例
        onLiveStatusErrorChange:PropTypes.func,//获取缩放比例
        onHideFullScreenTool:PropTypes.func,//全屏时，云台、工具消失时的回调
    };

    static defaultProps = {
        navBar: undefined,
        navBarRight: [],
        toolBarMoreItems: [],
        fullScreenToolBarMoreItems: [],
        lensCorrect: {use: false, x: 0, y: 0},
        dataSource: {playerClass: IMICameraVideoView.PlayerClass.LIVE, did: LetDevice.deviceID},
        qualityData:[{title:stringsTo("quality_sd"),index:1,accessibilityLabel:"home_page_clarity_show_sd"},
            {title:stringsTo("quality_fhd"),index:2,accessibilityLabel:"home_page_clarity_show_fhd"}],
        defaultQualityIndex:0,
        isSleepStatus:false,
        isOnLine:true,
        isCalling:false,
        isMove:false,
        albumName:LetDevice.devNickName,
        isShowPanoramicView:false,//圆盘还是通话显示,056/059
        isCallingStatue:false,//是否点击通话
        netConnected:true,//是否断网,同步双端显示
        isDataUsage:false,//是否开启流量保护
    };

    getStatusBarHeight(){
        if (isIos()) {
            StatusBarHeight = isIphoneXSeries()?47+50:20+50;
        } else {
            StatusBarHeight = parseInt(StatusBar.currentHeight)+50;
        }
    }

    UNSAFE_componentWillMount() {

        Orientation.lockToPortrait();
        // Orientation.unlockAllOrientations();

        LetDevice.getPropertyCloud('StreamVideoQuality').then(data => {
            console.log('进来就获取当前清晰度--',data);
            let  selectedIndex = this.props.qualityData.length>0?this.props.qualityData.length-1:0;
            for (let i = 0; i < this.props.qualityData.length; i++){
                let qualityData = this.props.qualityData[i];
                if (qualityData.index == data){
                    console.log('相等时的当前第几个清晰度---',i)
                    selectedIndex = i;
                }
            }
            this.props.onGetQualityIndex&&this.props.onGetQualityIndex(data);
            this.setState({qualityIndex: selectedIndex});
        }).catch(error => {
            // alert(JSON.stringify(Utils.parseError(error)));
        });

        //获取设备的mac地址
        LetDevice.getAssignedPropertyCloud("MAC").then(data=>{
            console.log("mac",typeof (data),data);
            this.macAddress = data;
        }).catch(error=>{

        })


        /*本页面获得焦点*/
        this._subscribeFocus = this.props.navigation.addListener('focus', () => {
            console.log("focus--");
            this.backHandler = BackHandler.addEventListener("hardwareBackPress", _ => this._onPressBack()); //解决031、038无法单步返回的问题

            this._onPressPlayToolBar();
            Orientation.addOrientationListener(this._orientationDidChange);
        });

        /*本页面失去焦点*/
        this._subscribeBlur = this.props.navigation.addListener('blur', () => {
            console.log("blur--");
            this.backHandler && this.backHandler.remove();
            Orientation.removeOrientationListener(this._orientationDidChange);
        });

        /*退到后台*/
        this._enterBackground = IMINativeLifeCycleEvent.enterBackgroundListener.addListener(() => {
            console.log("enterBackgroundListener---xy-进入后台--全屏状态",this.state.isFullScreen);
            if(this.state.recording){ //Android因为跳转其他页面、进入后台导致直播流暂停时，因为setState的时延isPlaying此时为true，需要手动停止录像
                if(CONST.isAndroid&&this.state.isPlaying){
                    this._stopRecord();
                }else{ //录像停止IOS会自动停止视频录制，所以直接转存即可。Android因为直播流异常也只需直接转存视频即可
                    this._saveVideoToPhotosAlbum();
                    console.log('后台停止播放');
                }

            }
        });

    }

    componentDidMount() {
        this.scaleTimeout && clearTimeout(this.scaleTimeout);
        this.scaleTimeout = setTimeout(()=>{
            IMIStorage.load({
                key: LetDevice.deviceID+'zoomScale',
                autoSync: true,
                syncInBackground: true,
            }).then(res => {
                // console.log('本地保存缩放比例',res,res.zoomScale,this.state.zoomScale);
                this.setState({saveScale:res.zoomScale,zoomScale:res.zoomScale});
            }).catch(_=> {
                this.setState({saveScale:1.0,zoomScale:1.0})
            });
        },200);

        IMIStorage.load({
            key: LetDevice.deviceID+'liveNetMode',
            autoSync: true,
            syncInBackground: true,
        }).then(res => {
            this.liveNetMode = res.liveNetMode;
        }).catch(_=> {
            this.liveNetMode = LIVE_NETWORK_MODE.RELAY;
        });

    }

    componentWillUnmount() {
        Orientation.removeOrientationListener(this._orientationDidChange);
        this._subscribe_focus && this._subscribe_focus();
        this._subscribe_blur && this._subscribe_blur();
        this.IMIVideoView && this.IMIVideoView.stop();
        this.IMIVideoView && this.IMIVideoView.destroy();
        this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
        this.playTooltsTimer && clearTimeout(this.playTooltsTimer);
        this.backHandler && this.backHandler.remove();
        clearTimeout(this.angleViewTimeout);
        this.scaleTimeout && clearTimeout(this.scaleTimeout);
    }

    //判断当前是否可以操作
    _canStepIn(){

        if (!this.props.isOnLine){
            console.log("设备离线，不可操作");
            showToast(stringsTo('device_offline'));
            return false;
        }
        if (this.props.isSleepStatus){
            console.log("设备休眠，不可操作");
            showToast(stringsTo('power_off'));
            return false;
        }
        if(!this.state.isPlaying || this.state.isLoading){
            console.log("直播流未开始，不可操作");
            showToast(I18n.t('onlyDoInLive'),Toast.positions.BOTTOM);
            return false;
        }
        return true;
    }

    _canStepInWithNoToast(){

        if (!this.props.isOnLine){
            showToast(stringsTo('device_offline'));
            return false;
        }
        if (this.props.isSleepStatus){
            return false;
        }
        if(!this.state.isPlaying || this.state.isLoading){
            console.log("直播流未开始，不可操作");
            showToast(I18n.t('onlyDoInLive'),Toast.positions.BOTTOM);
            return false;
        }
        return true;
    }

    _canIsCalling(){
        console.log("通话状态，state:",this.props.isCalling);
        if (this.props.isCalling)
        {
            showToast(stringsTo('imi_speaking_block'));
            return false;
        }
        return true;
    }


    _orientationDidChange = (orientation) => { //加此监听逻辑用来解决方向锁定关闭后，横放手机再点全屏按钮，页面显示不对的问题
        console.log("方向改变：",orientation);
        // if (orientation === 'LANDSCAPE') {
        //     if (!this.state.isFullScreen&&this.state.isPlaying) {
        //         //不先全屏和不加延时，直接走点击全屏按钮逻辑无法正确切换全屏
        //         Orientation.lockToPortrait();
        //         this.delayFullScreen = setTimeout(()=>{
        //             this._onPressFullScreen();
        //             this.delayFullScreen && clearTimeout(this.delayFullScreen);
        //         },50);
        //     }
        // } else {
        //     console.log('监听退出全屏');
        //     if (this.isNewFullScreen){
        //         // 原来进入后台时是全屏状态，再次进入也要设置为全屏状态
        //         this.enterFullScreen();
        //         console.log('前台全屏');
        //     }else {
        //         this.state.isFullScreen && this._exitFullScreen();
        //     }
        // }
    };


    _onPressFullScreen = () => {
        if(!this._canStepInWithNoToast())  return;
        IMILogUtil.uploadClickEventForCount("FullScreen");
        Orientation.lockToPortrait();
        isAndroid()?Orientation.lockToLandscape():Orientation.lockToLandscapeRight();
        // Orientation.unlockAllOrientations();
        this.setState({isFullScreen: true});
        NavigationBar.setStatusBarHidden(true);
        this.props.navigation.setOptions({tabBarVisible: false});
        this._onPressFullScreenTools();
        this.IMIVideoView.standardViewRatio()
        IMILogUtil.uploadClickEventForCount("FullScreen"); //统计点击全屏的情况
    };

    _exitFullScreen = () => {
        Orientation.lockToPortrait();
        // Orientation.unlockAllOrientations();
        this.fullScale = JSON.parse(JSON.stringify(this.state.zoomScale));
        this.isShowFullScale = true;
        console.log('当前缩放比例==',this.fullScale);

        this.setState({isFullScreen: false});
        this.props.onGetZoomScale&&this.props.onGetZoomScale(this.fullScale);
        NavigationBar.setStatusBarHidden(false);
        this.props.navigation.setOptions({tabBarVisible: true});
        this._onCloseFullScreenTools();
    }

    _onPressBack = () => {
        if (this.state.isFullScreen) {
            this._exitFullScreen();
            return true;
        }
        IMIGotoPage.exit();
        return false;
    };

   //  全屏工具栏显示与隐藏
    _onPressFullScreenTools = () => {
        this.setState({showFullScreenTools: true});
        this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
        this.fullScreenTooltsTimer = setTimeout(() => {
            if (this.props.isMove)return;//全屏长按触摸 取消消失
            this._onCloseFullScreenTools();
        }, 8000);
    }

    //暴露给父组件使用的方法
    _doToolsShowHide(){
        this.state.isFullScreen && this.state.showFullScreenTools ? this._onCloseFullScreenTools() : this._onPressFullScreenTools();
        !this.state.isFullScreen && this.state.showPlayToolBar ? this._onClosePlayToolBar() : this._onPressPlayToolBar();
    }

    _onCloseFullScreenTools() {
        this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
        this.setState({showFullScreenTools: false});
        //回调出去，用于结束转动电机的请求
        this.props.onHideFullScreenTool && this.props.onHideFullScreenTool();
    }
   // 竖屏工具栏显示与隐藏
    _onPressPlayToolBar = () => {
        this.setState({showPlayToolBar: true});
        this.playTooltsTimer && clearTimeout(this.playTooltsTimer);
        this.playTooltsTimer = setTimeout(() => {
            this._onClosePlayToolBar();
        }, 5000);
    }

    _onClosePlayToolBar() {
        this.playTooltsTimer && clearTimeout(this.playTooltsTimer);
        this.setState({showPlayToolBar: false});
    }

    _onPressMute = () => {
        if(!this._canStepInWithNoToast())  return;
        IMILogUtil.uploadClickEventForCount("VoiceFuc");
        this.setState({mute: !this.state.mute})
    };
    _onSleepClick = () => {
        console.log('休眠');
    };


    /*告知外部调用者监听状态*/
    getMute() {
        return this.state.mute;
    }

    /*外部调用者通知该组件监听状态的变化*/
    setMute(mute) {
        this.setState({mute: mute});
    }

    /*外部调用者通知该组件是否在通话*/
    setCalling(isCalling) {
        this.setState({calling: isCalling});
    }

    //获取录屏状态
    getRecordStatus(){
        return this.state.recording;
    }
    //获取是否展示异常
    getErrorStatus(){
        return this.state.showErrorView;
    }
    //设置是否显示暂停按钮
    setPauseView(showPauseView){
        this.setState({showPauseView: showPauseView,isLoading:false});
    }

    //设置是否显示加载中状态
    setLoadingView(showLoadingView){
        if (showLoadingView){
            this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.LOADING);
        }
        this.setState({isLoading: showLoadingView});
    }
    //同步清晰度
    setQualityIndex(index){
        console.log('王--setQualityIndex',index);
        this.setState({qualityIndex: index});
        if (this.state.recording) {
            console.log("录屏结束-------------");
            this._stopRecord();
        }
    }
    setIsForegroundPage(isForegroundPage){
        this.isForegroundPage=isForegroundPage;
    }

    setIsNewFullScreen(isNewFullScreen){
        this.isNewFullScreen=isNewFullScreen;
        console.log('当前Home全屏状态',this.isNewFullScreen,isNewFullScreen);
    }

    // 052全屏状态是有报警按钮，如果看家助手/一键警告 未打开，
    //  需要退出全屏进入看家助手/一键警告
    quitFullScreen() {
        Orientation.lockToPortrait();
        this.setState({isFullScreen: false,showPlayToolBar:true})
        NavigationBar.setStatusBarHidden(false);
        this.props.navigation.setOptions({tabBarVisible: true});
        this._onCloseFullScreenTools();
    }

    /*告知外部调用者监听状态*/
    getFullscreen() {
        return this.state.isFullScreen;
    }

    enterFullScreen() {
        isAndroid()?Orientation.lockToPortrait():null;
        isAndroid()?Orientation.lockToLandscape():Orientation.lockToLandscapeRight();
        // Orientation.unlockAllOrientations();
        this.setState({isFullScreen: true});
        NavigationBar.setStatusBarHidden(true);
        this.props.navigation.setOptions({tabBarVisible: false});
        this._onPressFullScreenTools();
        this.IMIVideoView.standardViewRatio();
        this.isNewFullScreen = false;
        console.log('设置全屏');
    }
    // 更新报错状态
    updateErrStatus() {
        this.setState({showPauseView: false, showErrorView: false});
    }
    toBase64(currentPath){
        console.log('当前截图路径',currentPath);
        RNFS.readFile(currentPath, 'base64')
            .then((content) => {
                // 得到的结果就可以 传给接口了 ，如果想要在网页上预览效果不要忘记格式转换
                console.log('转为base64',content);
                this.writeFile(currentPath,content);
            })
            .catch((err) => {
                console.log("图片读取失败");
            });
    }

    writeFile(tempPath,tempContent){
        let currentSnapshotPath = `${TMP_SNAPSHOT_PATH_PREFIX}IMG_10000001.jpg`;
        console.log('新的路径',currentSnapshotPath);
        console.log('原来路径',tempPath);
        RNFS.writeFile(currentSnapshotPath,tempContent,'base64')
            .then((result) => {
                // 得到的结果就可以 传给接口了 ，如果想要在网页上预览效果不要忘记格式转换
                this.showImg(currentSnapshotPath);
            })
            .catch((err) => {
                console.log("图片写失败");
            });
    }

    showImg(imgPath) {
        console.log('显示路径',imgPath);
        this.snapshotTimeoutNew && clearTimeout(this.snapshotTimeoutNew);
        this.snapshotTimeoutNew = setTimeout(() => { //3秒后截屏缩略图自动隐藏
            this.setState({ snapshotVisible: true});
            console.log('读转化后文件');
        }, 6000);
    }

    //点击截屏按钮
    _onPressScreenShot = () => {

        IMILogUtil.uploadClickEventForCount("VideoSnapImage"); //统计点击截屏的情况
        if(!this._canStepInWithNoToast())   return;
        /*if(!this._canIsCalling())  return;*/
        if(new Date().getTime()-lastClickSnapPhoto<1000) { //为了避免快速点击导致的截屏白屏问题，每秒只给截图一次
            return;
        }
        lastClickSnapPhoto = new Date().getTime();
        this.props.onCheckPermissionStatusChange&&this.props.onCheckPermissionStatusChange(true);


        if(this.state.snapshotVisible){
            this.setState({ snapshotVisible: false});
        }
        //20220909@byh 读写权限的请求不需要去请求完读权限，再去请求写权限，只需要去请求一个就可以
        //请求读、写都请求的话，会导致前后台切换执行两个前台-后台-前台-后台
        let {showCustomQuality} = DeviceTemplatesUtils.getConfigProject(LetDevice.category, LetDevice.model);
        console.log(this.state.qualityIndex)
        let quality = showCustomQuality[this.state.qualityIndex];
        IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, (status2) => {
            if (status2 === 0) {
                let currentSnapshotPath = `${TMP_SNAPSHOT_PATH_PREFIX}IMG_${new Date().getTime()}.jpg`;
                if (IMIPackage.minApiLevel>=10009 && quality.width && quality.height){
                    //截图根据分辨率截图
                    this.IMIVideoView.screenShotWithResolution(currentSnapshotPath,quality.width,quality.height).then(_ => {
                        console.log("排查页面卡死问题------截图成功");
                        IMIFile.saveImageToPhotosAlbum(currentSnapshotPath, this.props.albumName).then(_ => {
                            this.setState({screenShotPath:currentSnapshotPath,snapshotVisible: true,snapImgType:1},callback=>{
                                // this.toBase64(currentSnapshotPath);
                            });
                            Toast.show(stringsTo("saved_system_album"),{
                                duration:1500,
                                position:Toast.positions.CENTER
                            });

                            console.log("排查页面卡死问题------截图成功-----保存截图成功",currentSnapshotPath);
                            this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
                            this.snapshotTimeout = setTimeout(() => { //3秒后截屏缩略图自动隐藏
                                this.setState({ snapshotVisible: false});
                            }, 3000);
                        }).catch(error => {
                            IMILog.logD("saveImageToPhotosAlbum error","error:"+error)
                        })
                    }).catch(error => {
                        IMILog.logD("screenShotWithResolution error","error:"+error)
                    });;
                }else {
                    this.IMIVideoView.screenShot(currentSnapshotPath).then(_ => {
                        console.log("排查页面卡死问题------截图成功");
                        IMIFile.saveImageToPhotosAlbum(currentSnapshotPath, this.props.albumName).then(_ => {
                            this.setState({screenShotPath:currentSnapshotPath,snapshotVisible: true,snapImgType:1},callback=>{
                                // this.toBase64(currentSnapshotPath);
                            });
                            Toast.show(stringsTo("saved_system_album"),{
                                duration:1500,
                                position:Toast.positions.CENTER
                            });

                            console.log("排查页面卡死问题------截图成功-----保存截图成功",currentSnapshotPath);
                            this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
                            this.snapshotTimeout = setTimeout(() => { //3秒后截屏缩略图自动隐藏
                                this.setState({ snapshotVisible: false});
                            }, 3000);
                        }).catch(error => {
                            IMILog.logD("saveImageToPhotosAlbum error","error:"+error)
                        });
                    }).catch(error => {
                        IMILog.logD("screenShot error","error:"+error)
                    });;
                }

                this.props.onCheckPermissionStatusChange&&this.props.onCheckPermissionStatusChange(false);
            } else if (status2 === -1) {
                showToast(stringsTo('storage_permission_denied'));
                this.props.onCheckPermissionStatusChange&&this.props.onCheckPermissionStatusChange(false);
            }
        });
    };

    //点击录屏按钮
    _onPressRecord = () => {
        console.log("录屏-----点击录屏按钮",this.state.recording);
        if(!this._canStepInWithNoToast())   return;
        /*    if(!this._canIsCalling())  return;*/
        IMILogUtil.uploadClickEventForCount("VideoRecord"); //统计点击录屏的情况
        if (this.state.recording) {
            console.log("录屏结束-------------");
            this._stopRecord();
        } else {
            this.props.onCheckPermissionStatusChange&&this.props.onCheckPermissionStatusChange(true);
            IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, (status2) => {
                if (status2 === 0) {
                    time=moment(new Date().getTime()).format('yyyyMMDD')+"_"+new Date().getTime();
                    let pathUrl=VEDIO_RECORD_PATH;
                    if (isIos()){
                        pathUrl=`${TMP_SNAPSHOT_PATH_PREFIX}VIDEO_IMI_${time}.mp4`;//同步IOS名称
                    }
                    console.log("排查录屏卡死问题------录屏-----开始录屏",pathUrl);
                    this.IMIVideoView.startRecord(pathUrl).then(_ => {
                        console.log("排查录屏卡死问题------录屏-----开始录屏");
                        GlobalUtil.isRecording = true;
                        this.setState({recording: true, recordDuration: 0});
                        this.props.onGetRecordStatus&&this.props.onGetRecordStatus(true);
                    }).catch(error => {
                        console.log("record error:",error);
                    });
                    this.props.onCheckPermissionStatusChange&&this.props.onCheckPermissionStatusChange(false);
                } else if (status2 === -1) {
                    showToast(stringsTo('storage_permission_denied'));
                    this.props.onCheckPermissionStatusChange&&this.props.onCheckPermissionStatusChange(false);
                }
            })
        }
    };

    //停止录像并保存在相册
    _stopRecord(forSave=true){
        console.log("排查录屏卡死问题------录屏-----停止录制",this.state.recordDuration);
        if(this.state.recordDuration<6){
            IMIToast.showToast(stringsTo('save_system_album_failed'), IMIToast.TYPE.BOTTOM);
            forSave = false;
        }
        this.IMIVideoView.stopRecord().then(_ => { //停止录制
            console.log("排查录屏卡死问题------录屏-----停止录制成功");

            if(!forSave){ //只停止，不保存
                console.log("停止录制-------保存失败");//save_system_album_failed
                // IMIToast.showToast(stringsTo('save_system_album_failed'), IMIToast.TYPE.BOTTOM);
                return;
            }

            let pathUrl=VEDIO_RECORD_PATH;
            if (isIos()){
                pathUrl=`${TMP_SNAPSHOT_PATH_PREFIX}VIDEO_IMI_${time}.mp4`;//同步IOS名称
            }
            IMIFile.saveVideoToPhotosAlbum(pathUrl, this.props.albumName).then(_ => { //转存视频
                this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
                console.log("排查录屏卡死问题------录屏-----停止录制成功后保存录像");
                this.setState({screenShotPath: this.tempSnapShotPath, snapshotVisible: true,snapImgType:2});
                Toast.show(stringsTo("saved_system_album"),{
                    duration:1500,
                    position:Toast.positions.CENTER
                });
                this.snapshotTimeout = setTimeout(() => { //3秒后截屏缩略图自动隐藏
                    this.setState({snapshotVisible: false});
                }, 3000);
            }).catch(error => {
                console.log('保存报错了',error);
                IMIToast.showToast(stringsTo('save_album_failed'), IMIToast.TYPE.BOTTOM);
            });

        }).catch(error => {
            console.log('停止录像报错了');
            IMIToast.showToast(stringsTo('save_album_failed'), IMIToast.TYPE.BOTTOM);
        });
        GlobalUtil.isRecording = false;
        this.setState({recording: false, recordDuration: 0});
        this.props.onGetRecordStatus&&this.props.onGetRecordStatus(false);

    }

    //IOS在视频流暂停时,将录制的视频保存到相册
    _saveVideoToPhotosAlbum() {
        console.log("排查页面卡死问题------录屏因为实时流停止------",this.state.recordDuration,this.state.isPlaying);
        if (this.state.recordDuration < 6) { //Android因为直播流停止recordDuration会直接边为0
            if (this.state.recordDuration==-1){
                IMIToast.showToast(stringsTo('save_album_failed'), IMIToast.TYPE.BOTTOM);
            }else {
                IMIToast.showToast(stringsTo('save_system_album_failed'), IMIToast.TYPE.BOTTOM);
            }
            GlobalUtil.isRecording = false;
            this.setState({recording: false, recordDuration: 0});
            this.props.onGetRecordStatus&&this.props.onGetRecordStatus(false);
            //ios不管错误与否，都要调用stopRecord，不然下次无法录制
            this.IMIVideoView.stopRecord().then(_=>{}).catch(error => {});
            return;
        }
        let pathUrl=VEDIO_RECORD_PATH;
        if (isIos()){
            pathUrl=`${TMP_SNAPSHOT_PATH_PREFIX}VIDEO_IMI_${time}.mp4`;//同步IOS名称
        }
        IMIFile.saveVideoToPhotosAlbum(pathUrl, this.props.albumName).then(_ => { //转存视频
            this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
            this.setState({screenShotPath: this.tempSnapShotPath, snapshotVisible: true,snapImgType:2});
            Toast.show(stringsTo("saved_system_album"),{
                duration:1500,
                position:Toast.positions.CENTER
            });
            this.snapshotTimeout = setTimeout(() => { //3秒后截屏缩略图自动隐藏
                this.setState({snapshotVisible: false});
            }, 3000);
            this.IMIVideoView.stopRecord().then(_=>{}).catch(error => {});
            // IMIToast.showToast(stringsTo('saved_system_album'), IMIToast.TYPE.BOTTOM);
        }).catch(error => {
            console.log('保存报错了',error);
            IMIToast.showToast(stringsTo('save_system_album_failed'), IMIToast.TYPE.BOTTOM);
            this.IMIVideoView.stopRecord().then(_=>{}).catch(error => {});
        });
        GlobalUtil.isRecording = false;
        this.setState({recording: false, recordDuration: 0});
        this.props.onGetRecordStatus&&this.props.onGetRecordStatus(false);
    }

    _onPressInStartSpeak = () => {
        this.IMIVideoView.startSpeak()
    }

    _onPressOutStopSpeak = () => {
        this.IMIVideoView.stopSpeak();
    }

    _onPressQuality(index, selectIndex) {
        console.log('选择的清晰度---',index,selectIndex);
        if (this.state.qualityIndex === selectIndex) return;
        if (LetDevice.model =="a1Godgpvr3D"||LetDevice.model == 'a1zcQKoHQ83'){
            LetDevice.setPropertyCloud(JSON.stringify({"StreamVideoQuality": index})).then(data => {
                this.setState({qualityIndex: selectIndex});
                this.props.onGetQualityIndex&&this.props.onGetQualityIndex(index);
            }).catch(error => {
                console.log(JSON.stringify(error))
            });
        }else {
            IMILogUtil.setPropertyCloudWithUploadLog(JSON.stringify({"StreamVideoQuality": index})).then(data => {
                this.setState({qualityIndex: selectIndex});
                this.props.onGetQualityIndex&&this.props.onGetQualityIndex(index);
            }).catch(error => {
                console.log(JSON.stringify(error))
            });
        }
    }

    /*优化网速值有较大跳变的问题，取平均值*/
    _getAverageBps(currentBps){
        let average = 0;
        if(bpsArray.length<5){
            bpsArray.push(currentBps);
        }else{
            bpsArray.shift();
            bpsArray.push(currentBps);
        }
        let total = bpsArray.reduce((a,b)=>{
            return a+b;
        });
        return  parseInt(total/bpsArray.length);
    }

    /**
     * 首页实时流进入，会出黑屏情况，从现象观看设备在线
     * 能想到的情况是插件进入，设备
     */
    _isBpsAlways0(){
        if (bpsArray.length<5){
            return false;
        }
        let total = bpsArray.reduce((a,b)=>{
            return a+b;
        });

        return  total === 0;
    }

    _onEventChange = (event) => {

        if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_VIDEO_INFO) {
            // console.log("直播流----_onEventChange,回调网速值",event.extra.bps);
            // console.log('直播流代码---',event.code);

            this.setState({bps: this._getAverageBps(event.extra.bps)});
            this.connectType = event.streamConnectType?event.streamConnectType:'';
            if ( event.extra.bps>0&&this.state.isLoading&&!this.state.isPlaying){ //loading消失而实时流不是playing状态
                this.setState({isLoading:false,isPlaying:true,showPauseView: false})
                this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.PLAYING);
            }
            if (event.extra.bps>0&&this.state.isPlaying&&this.state.showPauseView){
                this.setState({ showPauseView: false}); //视频在播放,但显示为暂停
                this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.PLAYING);
            }
            //cameraPlayerPage 播放器准备时显示加载动画，这里某些时候实时流在走，loading不消失
            if (event.extra.bps>0&&this.state.isPlaying && this.state.isLoading){
                this.setState({ isLoading: false}); //视频在播放,但显示为暂停
                this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.PLAYING);
            }
            // event.extra.bps>0&&this.state.isLoading&&!this.state.isPlaying&&this.setState({isLoading:false,isPlaying:true});//此句为了规避正常播放时突然出现loading框的问题
            //10005时3次连接问题 如果大于0kb就清除
            if ( event.extra.bps>0){
                timeErr10005=0;
            }

            //实时流会偶现直播黑屏，网速值为0，复现不好复现，这里增加对网络bps的检测
            //如果五次网速值都为0，并且是播放状态，可能就出现问题了
            // if (this._isBpsAlways0() && this.state.isPlaying){
            //     this.setState({showPauseView:true})
            // }else {
            //     this.setState({showPauseView:false})
            // }
        } else if (event.code == IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_BUFFERING) { //调用video.start()后的第一个回调
            console.log("直播流----_onEventChange,开始启用");
            //  IMILog.logD("王 实时流 _onEventChange >LOADING ",event.toString())
            this.setState({isLoading: true, isPlaying: false, showPauseView: false, showErrorView: false});
            this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.LOADING);
            if(this.state.recording&&isIos()){ //Android因为跳转其他页面、进入后台导致直播流暂停时，因为setState的时延isPlaying此时为true，需要手动停止录像
                this._stopRecord();
            }
        } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_PREVIEW) {
            console.log("直播流----_onEventChange,出现关键帧");
            this.setState({isLoading: false, isPlaying: true});
            this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.PLAYING)

        } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_STOP) {
            //  IMILog.logD("王 实时流 _onEventChange >PAUSE  ",event.toString())
            console.log("直播流----_onEventChange,停止播放",event.code);//每次进页面都会来这一出，是什么鬼,目前只有根据当前的播放状态来设置showPauseView
            console.log("直播流----_onEventChange,停止播放",event.code,this.props.isDataUsage,this.props.netConnected);
            if (this.liveVideoStartTime > 0){
                //多次，只记录一次
                GlobalUtil.liveVideoTime = GlobalUtil.liveVideoTime+new Date().getTime()-this.liveVideoStartTime;
                this.liveVideoStartTime = 0;
            }
            //流量保护时，这个判断会使得不展示暂停按钮
            //或者流量保护的时候，也是需要暂停按钮的
            if (!this.props.netConnected || this.props.isDataUsage){
                //如果没有网络,在执行到停止播放的时候，需要显示暂停
                this.setState({isLoading: false, isPlaying: false, showErrorView: false, showPauseView: true}); //Android会调用两次，所以停止了也没有暂停按钮
            }else {
                this.setState({isLoading: false, isPlaying: false,showPauseView: false}); //Android会调用两次，所以停止了也没有暂停按钮
            }
            this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.PAUSE);
            if(this.state.recording){ //Android因为跳转其他页面、进入后台导致直播流暂停时，因为setState的时延isPlaying此时为true，需要手动停止录像
                if(CONST.isAndroid&&this.state.isPlaying){
                    this._stopRecord();
                }else{ //录像停止IOS会自动停止视频录制，所以直接转存即可。Android因为直播流异常也只需直接转存视频即可
                    this._saveVideoToPhotosAlbum();
                }
            }

        } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_START) {
            console.log("直播流----_onEventChange,开始播放");
            //this.setState({isLoading:false});
        }else if (event.code ===104) {
            if(CONST.isAndroid&&event.extra.mode_value_key==false){
                this.IMIVideoView.clearRecord();
            }
        }
    };

    _onRecordTimeChange = (event) => {
        console.log("排查页面卡死问题------录屏-----时间回调",event.extra);
        this.setState({recordDuration: event.extra},callback=>{
            if (this.state.recordDuration == 1){
                // 临时截图
                let currentSnapshotPath = `${TMP_SNAPSHOT_PATH_PREFIX}IMG_${new Date().getTime()}.jpg`;
                this.IMIVideoView.screenShot(currentSnapshotPath).then(_ => {
                    this.tempSnapShotPath = currentSnapshotPath;
                    // console.log('临时路径---',this.tempSnapShotPath);
                });
            }
        })
    }

    /**
     * 竖屏状态视屏区域填充UI
     * @returns {Element}
     * @private
     */
    _renderPortraitScreenVideoViewArea() {
        return (
            this.props.navBar ? this.props.navBar(this.state.bps, this.state.isFullScreen) : (
                <NavigationBar
                    type={NavigationBar.TYPE.DARK} backgroundColor={"transparent"}
                    title={LetDevice.devNickName}
                    subtitle={this.state.bps >= 0 ? `${this.state.bps}KB/S` : " "}
                    left={[{key: NavigationBar.ICON.BACK, onPress: this._onPressBack}]}
                    right={this.props.navBarRight ? this.props.navBarRight : []}/>)
        );
    }

    /**
     * 清晰度选择器
     * @returns {Element}
     * @private
     */
    _renderLandscapeScreenQualityPopView() {
        return (
            <ModalView visible={this.state.qualityVisible}
                       onClose={_ => this.setState({qualityVisible: false})}>
                <View style={{flex: 1, flexDirection: "column", justifyContent: "center", alignItems: "center"}}>
                    {
                        this.props.qualityData.map((item, index) => {
                            let qualityData = this.props.qualityData[index];
                            return (
                                <TouchableOpacityText
                                    key={`qualityItem_${index}`}
                                    title={qualityData.title}
                                    accessibilityLabel={qualityData.accessibilityLabel}
                                    style={{
                                        width: 120,
                                        height: 40,
                                        borderRadius: 20,
                                        backgroundColor: index === this.state.qualityIndex ? "#4A70A5" : "rgba(255,255,255,0.3)",
                                        justifyContent: "center",
                                        alignItems: "center",
                                        marginVertical: 10
                                    }}
                                    textStyle={{color: 'rgba(255,255,255,0.9)', fontSize: 15, fontWeight: "bold"}}
                                    onPress={_ => {
                                        this.setState({qualityVisible: false});
                                        console.log('清晰度选择的Index',qualityData.index,index);
                                        this._onPressQuality(qualityData.index,index);
                                    }}
                                />
                            );
                        })
                    }
                </View>
            </ModalView>
        );
    }

    /**
     * 全屏状态videoView区域填充UI
     * @returns {Element}
     * @private
     */
    _renderLandscapeScreenVideoViewArea() {
        let qualityData = this.props.qualityData[this.state.qualityIndex];
        let languageStr = locales[0]?.languageCode;
        let tempStr = "zh";
        if ((languageStr.indexOf(tempStr) != -1) && LetDevice.model == "a1Ikkj5vsiK"){
            let tempQualityData = [{title:"标清",index:1,accessibilityLabel:"home_page_clarity_show_720"},
                {title:"高清",index:2,accessibilityLabel:"home_page_clarity_show_1080"},
                {title:"超清",index:3,accessibilityLabel:"home_page_clarity_show_25"}];
            qualityData = tempQualityData[this.state.qualityIndex];
        }
        return (
            this.state.showFullScreenTools ? (
                <LivePlayerFullScreenToolBarView
                    // style={{position: "absolute"}}
                    exitPress={this._exitFullScreen}
                    qualityTitle={qualityData.title}
                    qualityPress={_ =>{
                        if(!this._canStepInWithNoToast())   return;
                        this.setState({qualityVisible: true})
                    }}
                    qualityDisabled={this.state.recording || this.props.isSleepStatus}
                    mutePress={this._onPressMute}
                    mute={this.state.mute}
                    muteDisabled={this.props.isCallingStatue || this.props.isSleepStatus}
                    screenshotPress={this._onPressScreenShot}
                    screenshotDisabled={this.props.isSleepStatus}
                    recordPress={this._onPressRecord}
                    recording={this.state.recording}
                    recordDisabled={this.props.isCallingStatue || this.props.isSleepStatus}
                    moreItems={this.props.fullScreenToolBarMoreItems}
                    accessibilityLabel={qualityData.accessibilityLabel}
                    showGreyDisableIcon={true}
                />
            ) : null
        );
    }


    /**
     * 竖屏状态下半屏区域填充UI
     * @returns {Element}
     * @private
     */
    // 新版视频工具栏
    _renderPortraitToolBar(){
        let qualityData = this.props.qualityData[this.state.qualityIndex];
        let topValue = StatusBarHeight+Screen_Width*9/16-60;
        //position:'absolute'布局下，不设置height，子控件flex是无效的
        return (
            <View style={{flex:1,flexDirection:"column",position:'absolute',top:StatusBarHeight+Screen_Width*9/16-60,height:Screen_Height-topValue}}>
                {this.state.showPlayToolBar?<View style={{flexDirection: "row",}}>
                    <LivePlayerNewToolBarView
                        qualityTitle={qualityData.title}
                        accessibilityLabel={qualityData.accessibilityLabel}
                        qualityPress={_ => {  //清晰度选择
                            if(!this._canStepIn())  return;
                            IMILogUtil.uploadClickEventForCount("QualityFuc");
                            this.setState({qualityVisible: true})
                        }}
                        sleepDisabled={this.props.isCallingStatue}
                        qualityDisabled={this.state.recording||this.props.isSleepStatus||this.props.isCallingStatue} //是否是059
                        muteDisabled={this.props.isCallingStatue} //是否是059
                        fullscreenDisabled={this.props.isCallingStatue} //是否是059
                        isCallingStatue={this.props.isSleepStatus}
                        fullscreenPress={this._onPressFullScreen}  //全屏
                        mutePress={this._onPressMute}  //音量
                        mute={this.props.isSleepStatus?true:this.state.mute}
                        sleepPress={this._onSleepClick}//休眠
                        moreItems={this.props.toolBarMoreItems}
                    />
                </View> :
                    <TouchableWithoutFeedback
                        onPress={()=>{
                            console.log('导航栏点击');
                            !this.state.isFullScreen && this.state.showPlayToolBar ? this._onClosePlayToolBar() : this._onPressPlayToolBar();
                            this.props.onVideoClick && this.props.onVideoClick();
                        }}
                    >
                        <View style={{backgroundColor:"transparent",height:60,}}>
                        </View>
                    </TouchableWithoutFeedback>
                    }
                    {this.props.children}
            </View>
        );
    }

    _renderRecordingView() {
        let duration = this.state.recordDuration > 0 ? `${timeFilter(this.state.recordDuration)}` : "00:00";
        return (
            <View style={{
                position:'absolute',
                width: 64,
                height: 26,
                backgroundColor: 'rgba(0,0,0,0.6)',
                borderRadius: 4,
                flexDirection: 'row',
                justifyContent: "center",
                alignItems: "center",
                marginTop: 10 + (this.state.isFullScreen ? 50 : StatusBarHeight+10),
            }}>
                <View style={{backgroundColor: "#E74D4D", opacity: 0.9, width: 6, height: 6, borderRadius: 3}}/>
                <Text style={[{marginLeft: 6, fontSize: 12, color: 'rgba(255,255,255,0.9)'},Typography.textFix]}>{duration}</Text>
            </View>
        );
    }

    _loadingView() {
        if (!this.props.isOnLine)return;
        if (this.props.isSleepStatus) return;
        if (!this.state.isLoading) return;
        return (<View
                       style={{
                           position: "absolute",
                           width: "100%",
                           top:this.state.isFullScreen? 0:this.state.zoomScale>=1.03 ? 0:StatusBarHeight,
                           height:this.state.isFullScreen? "100%":(this.state.zoomScale>=1.03?StatusBarHeight:0)+Screen_Width*9/16,
                           display: "flex",
                           justifyContent: "center",
                           alignItems: "center",
                           backgroundColor:'black',
                       }}
        >
            <ActivityIndicator
                style={{width: 54, height: 54,}}
                color={"#ffffff"}
                size={"large"}
            />
            <Text
                style={{
                    textAlign: 'center',
                    color: colors.white,
                    fontSize: 15,
                }}>
                {stringsTo("commLoadingText")}
            </Text>
        </View>);
    }

    _errorView() {
        if (!this.props.isOnLine)return;
        if (!this.props.netConnected)return;//是否断网 断网会进入暂停
        if (this.props.isSleepStatus) return;
        if (!this.state.showErrorView) return;
        if (this.state.isLoading) return;
        return (
            <TouchableWithoutFeedback onPress={_=>{
                this._doToolsShowHide();
            }}>
            <View
                   style={{
                       position: "absolute",
                       width: "100%",
                       // height: "100%",
                       top:this.state.isFullScreen? 0:this.state.zoomScale>=1.03 ? 0:StatusBarHeight,
                       height:this.state.isFullScreen? "100%":(this.state.zoomScale>=1.03?StatusBarHeight:0)+Screen_Width*9/16,
                       display: "flex",
                       justifyContent: "center",
                       alignItems: "center",
                       backgroundColor:'black',
                   }}
            >
                <XText style={{
                    textAlign: 'center',
                    color: colors.white,
                    fontSize: 15,
                }}
                       text={I18n.t('on_live_play_error', {code: this.state.errorCode})}
                />
                <View style={{width: "100%",justifyContent:'space-evenly',flexDirection:'row'}}>
                    <RoundedButtonView buttonText={stringsTo('error_code_common_retry')}
                                       buttonStyle={{
                                           marginTop: 30,
                                           paddingHorizontal:20,
                                           height: 40,
                                           borderRadius:7
                                       }}
                                       buttonTextStyle={{textAlign:'center'}}
                                       onPress={() => {
                                           this.IMIVideoView&&this.IMIVideoView.start();
                                           this.setState({
                                               showErrorView:false,
                                               isLoading:true
                                           });
                                           this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.LOADING);
                                       }}
                                       accessibilityLabel={"error_code_common_retry"}
                    />
                    <RoundedButtonView buttonText={stringsTo('error_help')}
                                       buttonStyle={{
                                           marginTop: 30,
                                           paddingHorizontal:20,
                                           height: 40,
                                           borderRadius:7
                                       }}
                                       buttonTextStyle={{textAlign:'center'}}
                                       onPress={() => {
                                           //播放器发生错误时，点击查看帮助
                                           this.setState({isShowErrorHelp:true});
                                       }}
                                       accessibilityLabel={"error_help"}
                    />
                </View>
            </View>
            </TouchableWithoutFeedback>
        );
    }

    _pauseView() {
        if (!this.props.isOnLine)return null;
        if (this.props.isSleepStatus) return null;
        if (!this.state.showPauseView) return null;
        if (this.props.netConnected){
            if (this.state.showErrorView) return  null; //IOS上会先ERROR后PAUSE，如果已经显示errorView，则不显示pauseView
        }
        if (this.state.isLoading) return null;
        return (
            <TouchableWithoutFeedback onPress={_=>{
                this._doToolsShowHide();
            }}>
                <View
                    style={{
                        position: "absolute",
                        width: "100%",
                        // height: "100%",
                        top:this.state.isFullScreen? 0:this.state.zoomScale>=1.03 ? 0:StatusBarHeight,
                        height:this.state.isFullScreen? "100%":(this.state.zoomScale>=1.03?StatusBarHeight:0)+Screen_Width*9/16,
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        backgroundColor:'black',
                    }}
                >
                    <ImageButton
                        style={{width: 52, height: 52}}
                        source={require("./res/icon_play.png")}
                        highlightedSource={require("./res/icon_play_p.png")}
                        onPress={() => {
                            if (!this.props.netConnected){
                                showToast(stringsTo("network_not_connected"))
                                return;
                            }
                            // this.IMIVideoView&&this.IMIVideoView.start();
                            if (this.isPrepare){
                                this.IMIVideoView&&this.IMIVideoView.start();
                            }else {
                                this.IMIVideoView&&this.IMIVideoView.prepare();
                            }
                            this.setState({
                                showPauseView:false,
                                isLoading:true
                            });
                            this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.LOADING);
                        }}
                        accessibilityLabel={"home_page_icon_play"}
                    />
                </View>
            </TouchableWithoutFeedback>);
    }

    _deviceOffLineView() {
        if (this.props.isOnLine) return null;
        if (HomePageNewLivePlayerComponent.LIVE_PLAYER_STATUS.PLAYING==this.state.currentStatus) return null;
        return (
            <TouchableWithoutFeedback onPress={_=>{
                this._doToolsShowHide();
            }}>
                <View
                    style={{
                        position: "absolute",
                        width: "100%",
                        // height: "100%",
                        top:this.state.isFullScreen? 0:this.state.zoomScale>=1.03 ? 0:StatusBarHeight,
                        height:this.state.isFullScreen? "100%":(this.state.zoomScale>=1.03?StatusBarHeight:0)+Screen_Width*9/16,
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        backgroundColor:'black',
                    }}
                >
                    <View style={{alignItems:'center', justifyContent:'center'}}>
                        <XText raw={true} style={{
                            textAlign: 'center',
                            color: "#FFFFFF",
                            fontSize: 15,
                            height:20,
                            // backgroundColor:'red',
                        }}
                               text={stringsTo("device_offline")}
                        />

                        {
                            //目前无法获取设备的离线时间点，云端不支持
                            /*<XText raw={true} style={{
                            marginTop:5,
                            textAlign: 'center',
                            color: "#FFFFFF",
                            fontSize: 15,
                            height:20,
                            // backgroundColor:'blue'
                        }}
                               text={`${stringsTo('offlineTime')}2022-01-01 00:00:00`}
                        />*/
                        }
                        <View style={{
                            marginTop: 5,
                            borderRadius: 7,
                            minWidth:100,
                            backgroundColor: '#4A70A5',
                            fontSize: 15,
                            color: 'white',
                            textAlign: 'center',
                            textAlignVertical: 'center',
                        }}>
                            <Text
                                raw={true}
                                text={stringsTo("error_help")}
                                style={{
                                    minWidth:100,
                                    lineHeight:40,
                                    fontSize: 15,
                                    color: 'white',
                                    textAlign: 'center',
                                    textAlignVertical: 'center',
                                }}
                                accessibilityLabel={"error_help"}
                                onPress={() => {
                                    this.setState({isShowOffLineHelp:true})
                                }}>{stringsTo("error_help")}</Text>
                        </View>

                    </View>
                </View>
            </TouchableWithoutFeedback>
        );
    }
    //连接失败的帮助弹框
    showConnectErrorDialog() {

        return (
            <Modal
                supportedOrientations={["portrait", "landscape"]}
                animationType="none"
                transparent={true}
                visible={this.state.isShowErrorHelp}
                onRequestClose={() => {
                    console.log("onRequestClose is do")
                    this.setState({ isShowErrorHelp: false });
                }}>
                <ScrollView contentContainerStyle={{flexGrow: 1}} showsVerticalScrollIndicator={false}>
                    <View style={{
                        flexDirection: 'column',
                        flex: 1,
                        justifyContent: 'center',
                        backgroundColor: '#00000099',
                        alignItems:'center'
                    }}>

                        <View style={{
                            flexDirection: 'column',
                            borderRadius:10,
                            marginLeft:40,
                            marginRight:40,
                            width:Screen_Width-80,
                            flexWrap:"wrap",
                            backgroundColor:'#FFFFFF',
                            zIndex: 1
                        }}>

                            <Text accessibilityLabel={"connect_err"}
                                  numberOfLines={1}
                                  style={{
                                      marginLeft: 20,
                                      marginRight: 20,
                                      width: Screen_Width - 120,
                                      lineHeight: 60,
                                      textAlign: 'center',
                                      textAlignVertical: 'center',
                                      color: '#333333',
                                      fontSize: 17,
                                      fontWeight: 'bold',
                                  }}>{stringsTo("connect_err")}</Text>

                            <Text accessibilityLabel={"tit_one"}
                                  numberOfLines={10}
                                  style={{
                                      marginLeft: 20,
                                      marginRight: 20,
                                      width: Screen_Width - 120,
                                      textAlign: 'left',
                                      color:'#333333',
                                      lineHeight:18,
                                      fontSize: 12,
                                  }}>{stringsTo("connect_error_help_one")}

                            </Text>
                            <Text accessibilityLabel={"tit_two"}
                                  numberOfLines={10}
                                  style={{
                                      marginLeft: 20,
                                      marginRight: 20,
                                      width: Screen_Width - 120,
                                      marginTop: 5,
                                      textAlign: 'left',
                                      color:'#333333',
                                      lineHeight:18,
                                      fontSize: 12,
                                  }}>{stringsTo("connect_error_help_two")}
                                <Text style={{ color: '#4A70A5' }}
                                      accessibilityLabel={"offline_help_reconnection"}
                                      onPress={() => {
                                          this.setState({ isShowErrorHelp: false });
                                          IMIGotoPage.startNativePageWithRouterCommon(LetDevice.deviceID,COMMON_REBIND_URL,{mac:this.macAddress});
                                      }}>{stringsTo("offline_help_tip_reconect")}</Text>
                                <Text>{stringsTo('dot_for_modal')}</Text>
                            </Text>

                            <Text accessibilityLabel={"tit_the"}
                                  numberOfLines={10}
                                  style={{
                                      marginLeft: 20,
                                      marginRight: 20,
                                      width: Screen_Width - 120,
                                      marginTop: 5,
                                      textAlign: 'left',
                                      color:'#333333',
                                      lineHeight:18,
                                      fontSize: 12,
                                  }}>{stringsTo("connect_error_help_three")}
                                <Text style={{ color: '#4A70A5' }} accessibilityLabel={"offline_help_tip_feed_question"} onPress={()=>{
                                    this.setState({ isShowErrorHelp: false });
                                    IMIGotoPage.startNativeDevPageWithRouter(LetDevice.deviceID,COMMON_HELP_URL);
                                }}>{stringsTo("offline_help_tip_feed_question")}</Text>
                                <Text>{stringsTo('dot_for_modal')}</Text>
                            </Text>

                            <View style={{backgroundColor:"#CCCCCC",width:Screen_Width-80,height:1,marginTop:19}}>
                            </View>
                            <TouchableWithoutFeedback
                                accessibilityLabel={"confirm_btn"}
                                onPress={() => {
                                    this.setState({ isShowErrorHelp: false });
                                }}>
                                <View style={{
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    width:Screen_Width-80,
                                    height: 50,
                                    borderBottomLeftRadius:10,
                                    borderBottomRightRadius:10,
                                }}>
                                    <Text style={{
                                        width: '100%',
                                        height: '100%',
                                        lineHeight: 50,
                                        textAlign: 'center',
                                        textAlignVertical: 'center',
                                        color: '#4A70A5',
                                        fontSize: 15,
                                        fontWeight: 'bold'
                                    }}>{stringsTo("know_button")}</Text>
                                </View>
                            </TouchableWithoutFeedback>
                        </View>
                    </View>
                </ScrollView>
            </Modal>
        );
    }

    showDeviceOfflineDialog() {

        return (
            <Modal
                supportedOrientations={["portrait", "landscape"]}
                animationType="none"
                transparent={true}
                visible={this.state.isShowOffLineHelp}
                onRequestClose={() => {
                    this.setState({ isShowOffLineHelp: false });
                }}
                onDismiss={() => {
                    this.setState({ isShowOffLineHelp: false });
                }}>
                <ScrollView contentContainerStyle={{flexGrow: 1}} showsVerticalScrollIndicator={false}>
                    <View style={{
                        flexDirection: 'column',
                        flex: 1,
                        justifyContent: 'center',
                        backgroundColor: '#00000099',
                        alignItems:'center'
                    }}>

                        <View style={{
                            flexDirection: 'column',
                            borderRadius:10,
                            marginLeft:40,
                            marginRight:40,
                            width:Screen_Width-80,
                            flexWrap:"wrap",
                            backgroundColor:'#FFFFFF',
                            zIndex: 1
                        }}>

                            <Text accessibilityLabel={"only_tit"}
                                  numberOfLines={1}
                                  style={{
                                      marginLeft: 20,
                                      marginRight: 20,
                                      width: Screen_Width - 120,
                                      lineHeight: 60,
                                      textAlign: 'center',
                                      textAlignVertical: 'center',
                                      color: '#333333',
                                      fontSize: 17,
                                      fontWeight: 'bold',
                                  }}>{stringsTo("device_offline")}</Text>

                            <Text accessibilityLabel={"tit_one"}
                                  style={{
                                      marginLeft: 20,
                                      marginRight: 20,
                                      width: Screen_Width - 120,
                                      textAlign: 'left',
                                      color:'#333333',
                                      lineHeight: 18,
                                      fontSize: 12,
                                  }}>{stringsTo("offline_help_tip_one")}
                            </Text>
                            <Text accessibilityLabel={"tit_two"}
                                  style={{
                                      marginLeft: 20,
                                      marginRight: 20,
                                      width: Screen_Width - 120,
                                      marginTop: 5,
                                      textAlign: 'left',
                                      color:'#333333',
                                      lineHeight: 18,
                                      fontSize: 12,
                                  }}>{stringsTo("connect_error_help_two")}
                                <Text style={{ color: '#4A70A5' }}
                                      onPress={()=>{
                                          this.setState({ isShowOffLineHelp: false });
                                          IMIGotoPage.startNativePageWithRouterCommon(LetDevice.deviceID,COMMON_REBIND_URL,{mac:this.macAddress});
                                      }}
                                >{stringsTo("offline_help_tip_reconect")}</Text>
                                <Text>{stringsTo('dot_for_modal')}</Text>
                            </Text>

                            <Text accessibilityLabel={"tit_the"}
                                  style={{
                                      marginLeft: 20,
                                      marginRight: 20,
                                      width: Screen_Width - 120,
                                      marginTop: 5,
                                      textAlign: 'left',
                                      color:'#333333',
                                      lineHeight: 18,
                                      fontSize: 12,
                                  }}>{stringsTo("offline_help_tip_the")}
                            </Text>


                            <Text accessibilityLabel={"tit_five"}
                                  style={{
                                      marginLeft: 30,
                                      marginRight: 20,
                                      width: Screen_Width - 130,
                                      marginTop: 5,
                                      textAlign: 'left',
                                      color:'#333333',
                                      lineHeight: 18,
                                      fontSize: 12,
                                  }}>{stringsTo("offline_help_tip_the_first")}
                                <Text style={{ color: '#4A70A5' }}
                                      onPress={()=>{
                                          this.setState({ isShowOffLineHelp: false });
                                          this.props.navigation.push('CustomerServicePage');
                                      }}
                                >{stringsTo("offline_help_tip_connect_service")}</Text>
                                <Text>{stringsTo('offline_help_tip_the_thd')}</Text>
                            </Text>

                            <Text accessibilityLabel={"tit_five"}
                                  style={{
                                      marginLeft: 30,
                                      marginRight: 20,
                                      width: Screen_Width - 130,
                                      marginTop: 5,
                                      textAlign: 'left',
                                      color:'#333333',
                                      lineHeight: 18,
                                      fontSize: 12,
                                  }}>{stringsTo("offline_help_tip_the_sec")}
                                <Text style={{ color: '#4A70A5' }}
                                      onPress={()=>{
                                          this.setState({ isShowOffLineHelp: false });
                                          IMIGotoPage.startNativePageWithRouterCommon(LetDevice.deviceID,COMMON_REBIND_URL,{mac:this.macAddress});

                                          // IMIGotoPage.startNativeDevPageWithRouter(LetDevice.deviceID,COMMON_REBIND_URL);
                                      }}
                                >{stringsTo("offline_help_tip_reconect")}</Text>
                                <Text>{stringsTo('dot_for_modal')}</Text>
                            </Text>

                            <Text accessibilityLabel={"tit_five"}
                                  style={{
                                      marginLeft: 20,
                                      marginRight: 20,
                                      width: Screen_Width - 120,
                                      marginTop: 5,
                                      textAlign: 'left',
                                      color:'#333333',
                                      lineHeight: 18,
                                      fontSize: 12,
                                  }}>{stringsTo("offline_help_tip_forth")}
                                <Text style={{ color: '#4A70A5' }} accessibilityLabel={"tit_eight_press"} onPress={()=>{
                                    this.setState({ isShowOffLineHelp: false });
                                    this.props.navigation.push('NetworkInfoPage');
                                }}>{stringsTo("offline_help_tip_rssi")}</Text>
                                <Text style={{ color: '#333333' }}>{stringsTo("offline_help_tip_forth_first")}</Text>
                            </Text>

                            <Text accessibilityLabel={"tit_ten"}
                                  style={{
                                      marginLeft: 20,
                                      marginRight: 20,
                                      width: Screen_Width - 120,
                                      marginTop: 5,
                                      textAlign: 'left',
                                      color:'#333333',
                                      lineHeight:18,
                                      fontSize: 12,
                                      // backgroundColor:'green'
                                  }}>{stringsTo("offline_help_tip_fth")}
                                <Text style={{ color: '#4A70A5'}} accessibilityLabel={"tit_ten_press"}
                                      onPress={() => {
                                          this.setState({ isShowOffLineHelp: false });
                                          IMIGotoPage.startNativeDevPageWithRouter(LetDevice.deviceID,COMMON_HELP_URL);
                                      }}>{stringsTo("offline_help_tip_feed_question")}</Text>
                                <Text>{stringsTo('dot_for_modal')}</Text>
                            </Text>


                            <View style={{backgroundColor:"#CCCCCC",width:Screen_Width-80,height:1,marginTop:19}}>
                            </View>
                            <TouchableWithoutFeedback
                                accessibilityLabel={"confirm_btn"}
                                onPress={() => {
                                    this.setState({ isShowOffLineHelp: false });
                                }}>
                                <View style={{
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    width:Screen_Width-80,
                                    height: 50,
                                    borderBottomLeftRadius:10,
                                    borderBottomRightRadius:10,
                                }}>
                                    <Text style={{
                                        width: '100%',
                                        height: '100%',
                                        lineHeight: 50,
                                        textAlign: 'center',
                                        textAlignVertical: 'center',
                                        color: '#4A70A5',
                                        fontSize: 15,
                                        fontWeight: 'bold'
                                    }}>{stringsTo("know_button")}</Text>
                                </View>
                            </TouchableWithoutFeedback>
                        </View>
                    </View>
                </ScrollView>
            </Modal>
        );
    }

      // 缩放比例view
    renderZoomScaleView() {
        if (!this.state.isShowZoomScale) {
            return null;
        }
        return (
            <View style={{
                backgroundColor: "#00000099",
                borderRadius:4,
                position: "absolute",
                top:this.state.isFullScreen ? 60 : StatusBarHeight+15,
                // bottom: this.state.isFullScreen ? 206 : 0,
                // left: this.state.isFullScreen && isIphoneXSeries() ? 50 : 14,
                left: this.state.isFullScreen ? 65 : 20,
                width: 50,
                height: 30,
                zIndex: 999,
            }}>
                <Text
                    accessibilityLabel={"zoom"}
                    numberOfLines={1}
                    ellipsizeMode={"tail"}
                    style={{lineHeight:29,fontSize:15,color:"#FFFFFF",textAlignVertical:'center',textAlign:'center',}}
                >
                    {`x${this.state.zoomScale}`}
                </Text>
                </View>)
    }

    _renderSnapshotView() {
        if (!this.state.snapshotVisible) {
            return null;
        }
        console.log("截屏路径------------",this.state.screenShotPath);
        return (
            <View style={{
                backgroundColor: 'transparent',
                position: "absolute",
                top:this.state.isFullScreen ? 60 : StatusBarHeight+30,
                // bottom: this.state.isFullScreen ? 206 : 0,
                // left: this.state.isFullScreen && isIphoneXSeries() ? 50 : 14,
                left: this.state.isFullScreen ? 65 : 14,
                width: 140,
                height: 80,
                zIndex: 999,
            }}>
                <ImageButton
                    style={{
                        width: "100%",
                        height: "100%",
                        borderWidth: 2,
                        borderColor: 'white',
                        borderRadius: 10
                    }}
                    source={{uri: 'file://' + this.state.screenShotPath}}
                    onPress={(_) => {

                        if(LetDevice.model == "a1MZkl613tF"||LetDevice.model == "a1B1tBn2SdK" ||LetDevice.model == "a1EVaCvJ43g" ){
                            return;
                        }
                        if (this.state.recording) {
                            showToast(I18n.t('screen_recording'));
                            return;
                        }
                        /* if (this.state.calling) {
                             showToast(I18n.t('camera_calling'));
                             return;
                         }*/
                        if(!this._canIsCalling())  return;
                        this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
                        this.setState({snapshotVisible:false});
                        if(this.state.isFullScreen){ //横屏则退出全屏
                            // Orientation.lockToPortrait();
                            // this.setState({isFullScreen:false})
                            // NavigationBar.setStatusBarHidden(false);
                            // this.props.navigation.setOptions({tabBarVisible: true});
                            // this._onCloseFullScreenTools();

                            Orientation.lockToPortrait();
                            this.props.navigation.setOptions({tabBarVisible: true});
                            this._onCloseFullScreenTools();
                            NavigationBar.setStatusBarHidden(false);

                            this.setState({isFullScreen: false},()=>{
                                // IMIGotoPage.startAlbumPage(this.props.albumName);
                                this.goToAlbum();
                            });
                            return;
                        }

                        this.goToAlbum();
                        // IMIGotoPage.startAlbumPage(this.props.albumName);
                    }}
                    accessibilityLabel={"home_page_picture_show"}

                />
            </View>
        )
    }

    //进入相册
    goToAlbum() {
        let {showRNAlbum} = DeviceTemplatesUtils.getConfigProject(LetDevice.category, LetDevice.model);
        if (showRNAlbum) {
            if (IMIPackage.minApiLevel < 10007) {
                IMIGotoPage.startAlbumPage(LetDevice.deviceID);
            } else {
                // 进入详情页面
                this.getImgData();
            }
        } else {
            IMIGotoPage.startAlbumPage(LetDevice.deviceID);
        }

    }

    // 获取所有图片数据
    getImgData(){
        IMIFile.getSourceFromPhotosDidAlbum(LetDevice.deviceID).then(r => {
            console.log("getSourceFromPhotosDidAlbum",r);
            let imgArr = isIos() ? r : r.data;
            //排下序，让新的文件在前面
            if (imgArr.length>1){
                imgArr.sort((a,b) =>{
                    return b.modificationDate - a.modificationDate;
                })
            }
            console.log('获取所有图片对象---',imgArr,this.state.snapImgType);
            for (let i= 0;i<imgArr.length;i++){
                let item = imgArr[i];
                if (this.state.snapImgType == 1 &&  item.mediaType == 1){
                    // 进入截图详情
                    this.props.navigation.push('ImagePreView',{mediaData:item,hideDelete:false});
                    break;
                }else if (this.state.snapImgType == 2 && item.mediaType == 2){
                    // 进入录屏详情
                    this.props.navigation.push('VideoPreView',{mediaData:item,hideDelete:false});
                    break;
                }
            }
        }).catch(error => {
            console.log("_saveVideoToLocal error",JSON.stringify(error))
        });
    }
    /**
     * 通过时间戳获取日期
     * @param timestamp
     * @private
     */
    _getYearMonthDay(timestamp){
        let date = new Date(timestamp * 1000);
        let month = (date.getMonth() + 1 < 10 ? "0": "") + (date.getMonth() + 1);
        let year = date.getFullYear();
        let day=   (date.getDate() < 10 ? "0":"") + date.getDate();
        return year + '-' + month + '-'+day;
    }


    // 设置定时器
    videoScaleTimer = null;
    // 缩放比例系数变化
    videoScaleChanged(data){
        let scale = data.scaleRatio;
        let newScale = scale.toFixed(1);
        let zoomScale = this.state.zoomScale;

        if (Math.abs(zoomScale-newScale)<0.1){
            return;
        }

        if(newScale){
            //保存缩放比例，在退出时保存，可能无法保存成功
            IMIStorage.save({
                key: LetDevice.deviceID+'zoomScale',
                data: {
                    zoomScale: newScale
                },
                expires: null,
            });

            clearTimeout(this.videoScaleTimer);
            this.videoScaleTimer = setTimeout(() => {
                console.log("tick"+ newScale);
                this._updateScale(newScale); // 更新倍数
            }, 0);
        }
        // 进行节流操作
        let endTime = Date.now();
        if ((endTime - this.startScaleTime) < 50) {
            console.log('_onVideoScaleChanged', scale)
            return;
        }
        this.startScaleTime = endTime;
        this._updateScale(newScale);
    }

    _updateScale(scale) {
        if (scale) {
            if (this.angleViewTimeout) {// 隔一段时间就需要隐藏
                clearTimeout(this.angleViewTimeout);
                this.angleViewTimeout = null;
            }
            this.angleViewTimeout = setTimeout(() => {
                this.setState({ isShowZoomScale: false});
            }, 3000);
            // if (!this.state.fullScreen) {
            //     this.videoPortraitScale = scale;// 保存竖屏下的videoScale
            // }
            this.props.onGetZoomScale&&this.props.onGetZoomScale(scale);
            this.setState({ zoomScale: scale, isShowZoomScale: true, showPlayToolBar: scale > 1.03 ? false : true });
        }
    }


    render() {
        // console.log('导航栏高度---',StatusBarHeight);
        // if (isAndroid()){
        //     let temphight = parseInt(StatusBar.currentHeight)+50;
        //     console.log('未转化导航栏高度---',StatusBarHeight);
        // }
        return (
            <View ref={(ref) => this.componentContainer = ref} style={{flex: 1, backgroundColor: this.props.pageBackgroundColor?this.props.pageBackgroundColor:"#F7F7F7", flexDirection: "column"}}>
                <View  style={{flex: 1}}>
                    <IMICameraVideoView
                        style={{
                            height:this.state.isFullScreen ? "100%":StatusBarHeight*2+Screen_Width*9/16,
                            backgroundColor:this.state.isFullScreen ? "#000000":"#FFFFFF"
                        }} /*//this.state.isFullScreen?styles.cameraViewStylesNew:*/
                        ref={ref => {
                            this.IMIVideoView = ref;
                            this.props.videoRef && this.props.videoRef(this.IMIVideoView);
                        }}
                        //mute={this.state.mute}
                        //playerClass={IMICameraVideoView.PlayerClass.LIVE}
                        dataSource={this.props.dataSource}
                        // scaleRatio={1}
                        scaleRatio={parseFloat(this.state.saveScale)}
                        onPrepared={(value) => {
                            console.log("直播流-----onPrepared------",value);
                            this.isPrepare = true;
                            value = typeof (value) === 'string'?JSON.parse(value):value;
                            //streamConnectType为直播的模式，具体模式参考LIVE_NETWORK_MODE
                            if (value.streamConnectType && this.liveNetMode !== value.streamConnectType
                            && value.streamConnectType != "FAILD"){
                                IMIStorage.save({
                                    key: LetDevice.deviceID+'liveNetMode',
                                    data: {
                                        liveNetMode: value.streamConnectType
                                    },
                                    expires: null,
                                });
                            }
                            //缓存好开始播放的时间点，用于打点实时视频观看时长
                            this.liveVideoStartTime = new Date().getTime();
                            this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.PREPARED);
                            //this.IMIVideoView.start();
                        }}
                        onEventChange={this._onEventChange}

                        onErrorChange={(event) => {
                            console.log("直播流-----onErrorChange------   event.code " + event.code, event);
                            // IMILog.logD("王 实时流 onErrorChange : ",event.toString())
                            if (this.liveVideoStartTime > 0){
                                //多次触发错误，只记录一次
                                GlobalUtil.liveVideoTime = GlobalUtil.liveVideoTime+new Date().getTime()-this.liveVideoStartTime;
                                this.liveVideoStartTime = 0;
                            }
                            // //通话意外报错关闭 - 如果是通话意外报错关闭，则直接提示
                            this.props.onLiveStatusErrorChange&&this.props.onLiveStatusErrorChange();
                            do {
                                //判断如果是通话报错则此处进行判断是否为占线
                                /*if (event.code !== PLAYER_EVENT_CODE.ERROR_EVENT_STREAM_CLOSED_UNEXPECTEDLY) {
                                    alert("量产5-12——直播流Error-------- 不是流意外关闭");
                                    continue
                                }*/

                                // if (event.code == 1009){
                                //     // 设备不在线
                                //     this.props.onDeviceStatusChange&&this.props.onDeviceStatusChange(false); //通知外部调用者设备离线，需要更新相关的UI
                                //     return;
                                // }

                                if(isIos()){
                                    // alert('通话错误码'+event.code);
                                    console.log('xy--通话错误码'+event.code);
                                    if(event.code==12||event.code==15||event.code==16||event.code==19||event.code==-88002||event.code==14){ //IOS的通话异常
                                        // alert("量产5-12——直播流Error-------- IOS其他设备正在通话中，请稍后重试");
                                        if(LetDevice.model == "a1Od0SjKPGt"){
                                            showToast(stringsTo('call_busy_tips'));
                                            this.props.onVoiceCallErrorOccurred&&this.props.onVoiceCallErrorOccurred(12); //通知外部调用者通话异常中断，需要更新通话相关的UI
                                        }else  if(LetDevice.model == "a1Godgpvr3D"||LetDevice.model == 'a1zcQKoHQ83'){
                                            if (event.code==12){
                                                showToast(stringsTo('call_busy_tips'))
                                            }else {
                                                showToast(stringsTo('call_connect_error'));
                                            }
                                            this.props.onVoiceCallErrorOccurred&&this.props.onVoiceCallErrorOccurred(event.code); //通知外部调用者通话异常中断，需要更新通话相关的UI
                                            // this.props.onVoiceCallErrorOccurred&&this.props.onVoiceCallErrorOccurred(12); //通知外部调用者通话异常中断，需要更新通话相关的UI
                                        }else {
                                            if (event.code == 12){
                                                showToast(stringsTo('call_busy_tips'))
                                            }
                                            //event.code==12?showToast(stringsTo('call_busy_tips')):showToast(stringsTo("action_fail"));
                                            event.code==12?this.props.onVoiceCallErrorOccurred&&this.props.onVoiceCallErrorOccurred(event.code):
                                                this.props.onVoiceCallErrorOccurred&&this.props.onVoiceCallErrorOccurred(event.code); //通知外部调用者通话异常中断，需要更新通话相关的UI
                                        }

                                        return;
                                    }else{ //不是通话异常，则是正常的实时流异常，走do-while之后的逻辑
                                        //解决ios网络异常通话状态不挂断问题
                                        if (this.props.onVoiceCallErrorOccurred){
                                            this.props.onVoiceCallErrorOccurred(12);
                                        }
                                        continue;
                                    }

                                }
                                if (isAndroid()) {  //休眠第一次10005时  播放,连接两次后自动取消连接
                                    if (event.code == 1005) {
                                        timeErr10005 = timeErr10005 + 1;
                                        if (timeErr10005 < 3) {
                                            this.IMIVideoView&&this.IMIVideoView.start();
                                            return;
                                        }

                                    }
                                }
                                //判断是否为通话模式(Android)
                                if (event.extra.arg1 !== CAMERA_PLAYER_MODE.MODE_SPEAK) {

                                    continue
                                }
                                //暂时阿里SDK无法返回指定错误码，此处暂时使用info 对比
                                if (event.extra.arg2 === "voice intercom existed") {
                                    console.log("量产5-12——直播流Error-------- 其他设备正在通话中，请稍后重试");
                                    // showToast(stringsTo('call_busy_tips'));
                                    // this.props.onVoiceCallErrorOccurred&&this.props.onVoiceCallErrorOccurred()
                                    if(LetDevice.model == "a1Od0SjKPGt"){
                                        let code = 12;
                                        showToast(stringsTo('call_busy_tips'));
                                        this.props.onVoiceCallErrorOccurred&&this.props.onVoiceCallErrorOccurred(code); //通知外部调用者通话异常中断，需要更新通话相关的UI
                                    }else if (LetDevice.model == "a1Godgpvr3D"||LetDevice.model == 'a1zcQKoHQ83'||LetDevice.model == 'a1QRbHvcYBd'||LetDevice.model == 'a1FKrifIRwH'|| LetDevice.model == "a1Ikkj5vsiK"||LetDevice.model == "a1znn6t1et8"){
                                        showToast(stringsTo('call_busy_tips'));
                                        let code = 12;
                                        this.props.onVoiceCallErrorOccurred&&this.props.onVoiceCallErrorOccurred(code); //通知外部调用者通话异常中断，需要更新通话相关的UI
                                    }else {
                                        showToast(stringsTo('call_busy_tips'));
                                        this.props.onVoiceCallErrorOccurred&&this.props.onVoiceCallErrorOccurred(); //通知外部调用者通话异常中断，需要更新通话相关的UI
                                    }

                                } else {
                                    console.log("量产5-12——直播流Error-------- 网络异常，通话中断");
                                    // showToast(stringsTo('call_connect_error'));
                                    // this.props.onVoiceCallErrorOccurred&&this.props.onVoiceCallErrorOccurred()
                                    if(LetDevice.model == "a1Od0SjKPGt"){
                                        let code = 12;
                                        this.props.onVoiceCallErrorOccurred&&this.props.onVoiceCallErrorOccurred(code); //通知外部调用者通话异常中断，需要更新通话相关的UI
                                    }else if(LetDevice.model == "a1Godgpvr3D"||LetDevice.model == 'a1zcQKoHQ83'){
                                        showToast(stringsTo('net_connect_error'));
                                        console.log("量产5-12——直播流Error-------- 网络异常，通话中断",event.code);
                                        this.props.onVoiceCallErrorOccurred&&this.props.onVoiceCallErrorOccurred(event.code); //code通知外部调用者通话异常中断，需要更新通话相关的UI
                                    }else {
                                        showToast(stringsTo('call_connect_error'));
                                        this.props.onVoiceCallErrorOccurred&&this.props.onVoiceCallErrorOccurred(); //通知外部调用者通话异常中断，需要更新通话相关的UI
                                    }
                                }


                                return
                            } while (false);
                            if(this.props.netConnected) {//todo 断网进入暂停状态，不进入报错显示
                                this.setState({
                                    isPlaying: false,
                                    showErrorView: true,
                                    showPauseView: false,
                                    errorCode: event.code,
                                    showPlayToolBar: false,
                                });
                                this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.ERROR);
                            }
                        }}
                        onRecordTimeChange={this._onRecordTimeChange}
                        //lensCorrect={this.props.lensCorrect}
                        onVideoViewClick={()=>{
                            this.state.isFullScreen && this.state.showFullScreenTools ? this._onCloseFullScreenTools() : this._onPressFullScreenTools();
                            !this.state.isFullScreen && this.state.showPlayToolBar ? this._onClosePlayToolBar() : this._onPressPlayToolBar();
                            this.props.onVideoClick && this.props.onVideoClick();
                        }}
                        onVideoZoomScale={(data)=>{
                            console.log('xy当前缩放比例系数',data);
                            console.log('当前退出全屏路径',this.isShowFullScale,this.fullScale,this.state.isShowFullScale);
                            // if (this.isShowFullScale){
                            //     this.props.onGetZoomScale&&this.props.onGetZoomScale(this.fullScale);
                            //     this.isShowFullScale = false;
                            //     this.setState({saveScale:this.fullScale,zoomScale:this.fullScale},callback=>{
                            //         this.setState({isShowFullScale:false})
                            //         console.log('全屏退出缩放走这里2');
                            //     })
                            //     console.log('全屏退出缩放走这里1');
                            // }else {
                            //     if (data){
                            //         console.log('缩放有值走这里');
                            //         this.videoScaleChanged(data);
                            //     }else {
                            //         console.log('缩放无值走这里');
                            //         this.props.onGetZoomScale&&this.props.onGetZoomScale(1.0);
                            //     }
                            // }

                            if (data){
                                console.log('缩放有值走这里');
                                this.videoScaleChanged(data);
                            }else {
                                console.log('缩放无值走这里');
                                this.props.onGetZoomScale&&this.props.onGetZoomScale(1.0);
                            }


                            // let scale = data.scaleRatio;
                            // let newScale = scale.toFixed(1);
                            // this.setState({zoomScale:newScale})
                            // this.props.onGetZoomScale&&this.props.onGetZoomScale(newScale);
                            // console.log('显示缩放比例系数',scale,newScale);
                        }}
                    />
                    {/*全屏?横屏UI:竖屏UI(navBar)*/}
                    <View pointerEvents="box-none" style={{position: "absolute", width: "100%", height: "100%", flexDirection: "column", alignItems: "center"}}>
                        {
                            this.props.videoSubView ? this.props.videoSubView(this.state.isFullScreen,this.state.showFullScreenTools) : null
                        }
                        {
                            this.props.loadingView ? this.props.loadingView(this.state.isLoading) : this._loadingView()
                        }
                        {
                            this.props.sleepView ? this.props.sleepView(this.state.isFullScreen,this.state.zoomScale):null
                        }
                        {
                            this.props.errorView ? this.props.errorView(this.state.showErrorView) : this._errorView()
                        }

                        {
                            this.props.pauseView ? this.props.pauseView(this.state.showPauseView) : this._pauseView()
                        }
                        {this._deviceOffLineView()}

                        {
                            this._renderSnapshotView()
                        }
                        {
                            this.state.isFullScreen ? this._renderLandscapeScreenVideoViewArea() : this._renderPortraitScreenVideoViewArea()
                        }
                        {
                            this.state.isFullScreen ? null : this._renderPortraitToolBar()
                        }
                        {
                            this.state.recording&&this.state.isPlaying ? this._renderRecordingView() : null

                        }
                        {
                            this.renderZoomScaleView()
                        }
                    </View>

                </View>

                {/*渲染清晰度选择器，不占UI空间的*/}
                {this._renderLandscapeScreenQualityPopView()}
                {this.showConnectErrorDialog()}
                {this.showDeviceOfflineDialog()}
            </View>
        );
    }
}
let styles = StyleSheet.create({
    cameraViewStyles: {
        width:getScreenWidth()*16/9,         //(getScreenWidth())*16/9,  //等比播放 防止拉伸
        height:getScreenWidth(),
    },
    cameraViewStylesNew: {
        width:getScreenWidth()*16/9,//20220211@byh width改回16/9 高度百分百，宽度100%时会出现拉升
        height:"100%",
    },
    cameraViewFullStyles: {
        flex:1,
    },
    containFull:{flex: 1, backgroundColor: "#000000",
        justifyContent:"center",
        alignItems:"center",
    },
});
