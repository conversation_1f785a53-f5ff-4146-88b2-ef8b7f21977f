/**
 * LivePlayerToolBarView.js 直播的toolbar
 *
 * @property {string} qualityTitle - 清晰度文案
 * @property {func} qualityPress - 清晰度点击事件
 * @property {boolean} qualityDisabled - 清晰度是否可点击
 *
 * @property {func} mutePress - 静音点击事件
 * @property {boolean} muteDisabled - 静音是否可点击
 * @property {boolean} mute - 是否静音
 *
 * @property {func} screenshotPress - 截图点击事件
 * @property {boolean} screenshotDisabled - 是否可点击
 *
 * @property {func} recordPress - 录像点击事件
 * @property {boolean} recordDisabled - 是否可点击
 * @property {array} recording - 是否录像中
 *
 @Deprecated @property {func} warningPress - 报警点击事件
 * @Deprecated @property {boolean} warningDisabled - 是否可点击
 *
 * @property {func} fullscreenPress - 全屏点击事件
 * @property {boolean} fullscreenDisabled - 是否可点击
 *
 * @property {array} moreItems - 更多自定义按钮 参考[{item:{isText: true, data: ["流畅"], onPress: null, disabled: true,dataIndex:0},insertIndex:0},]
 *
 * @property {showBottomTitle} 工具栏icon底部显示文案标注功能，仅IPC062客户提的特殊需求
 *
 * 示例：
 * <LivePlayerToolBarView
 *  qualityData={this.state.qualityData}
 *  qualityIndex={this.state.qualityIndex}
 *  fullscreenPress={this._onPressFullScreen}
 *  screenshotPress={this._onPressScreenshot}
 *  recordPress={this._onPressRecord}
 *  recording={this.state.recording} />
 *
 * <AUTHOR>
 * @date 2020/11/24
 */

import React, {Component} from 'react';
import PlayerToolBarView from './PlayerToolBarView';
import PropTypes from 'prop-types';
import {stringsTo} from '../../globalization/Localize';

export default class LivePlayerToolBarView extends Component {
  static propTypes = {
    qualityTitle: PropTypes.string,
    qualityPress: PropTypes.func,
    qualityDisabled: PropTypes.bool,
    accessibilityLabel: PropTypes.string,
    mutePress: PropTypes.func,
    muteDisabled: PropTypes.bool,
    mute: PropTypes.bool,

    screenshotPress: PropTypes.func,
    screenshotDisabled: PropTypes.bool,

    recordPress: PropTypes.func,
    recordDisabled: PropTypes.bool,
    recording: PropTypes.bool,

    /* warningPress: PropTypes.func,
     warningDisabled: PropTypes.bool,*/

    fullscreenPress: PropTypes.func,
    fullscreenDisabled: PropTypes.bool,

    moreItems: PropTypes.array,
    isShowPanoramicView: PropTypes.bool, //圆盘还是通话显示,056/059
    isCallingStatue: PropTypes.bool, //是否点击通话

    showBottomTitle: PropTypes.bool,

    bottomBarMoreItems: PropTypes.array,
  };

  static defaultProps = {
    qualityTitle: '流畅',
    accessibilityLabel: 'home_page_clarity_show_low',
    qualityPress: null,
    qualityDisabled: false,

    mutePress: null,
    muteDisabled: false,
    mute: true,

    screenshotPress: null,
    screenshotDisabled: false,

    recordPress: null,
    recordDisabled: false,
    recording: false,

    /* warningPress: null,
     warningDisabled: false,*/

    fullscreenPress: null,
    fullscreenDisabled: false,

    moreItems: [],
    isCallingStatue: false, //是否点击通话

    showBottomTitle: false,

    bottomBarMoreItems: [],
  };

  constructor(props, context) {
    super(props, context);
    this.state = {};
  }

  getBtnByName(name) {
    switch (name) {
      case 'callBtn':
        return {
          isText: false,
          data: [require('./res/icon_call_start.png'), require('./res/icon_call_ing.png')],
          onPress: this.props?.onStartCall,
          disabled: this.props.recordDisabled,
          dataIndex: this.props.isCalling ? 1 : 0,
          isCallingStatue: this.props.isCallingStatue,
          accessibilityLabel: ['home_page_screenshots'],
          bottomTitle: null,
        };
      case 'voiceBtn':
        return {
          isText: false,
          data: [require('./res/player_toolbar_mute.png'), require('./res/player_toolbar_volume.png')],
          onPress: this.props.mutePress,
          disabled: this.props.muteDisabled,
          dataIndex: this.props.mute ? 0 : 1,
          isCallingStatue: this.props.isCallingStatue,
          accessibilityLabel: ['home_page_voice_off', 'home_page_voice_on'],
          bottomTitle: this.props.showBottomTitle ? stringsTo('toolbar_text_sound') : null,
        };
      case 'screenshotBtn':
        return {
          isText: false,
          data: [require('./res/player_toolbar_screenshot.png')],
          onPress: this.props.screenshotPress,
          disabled: this.props.screenshotDisabled,
          dataIndex: 0,
          isCallingStatue: this.props.isCallingStatue,
          accessibilityLabel: ['home_page_screenshots'],
          bottomTitle: this.props.showBottomTitle ? stringsTo('toolbar_text_snap') : null,
        };
      case 'recordBtn':
        return {
          isText: false,
          data: [require('./res/player_toolbar_recording.png'), require('./res/player_toolbar_record1.png')],
          onPress: this.props.recordPress,
          disabled: this.props.recordDisabled,
          dataIndex: this.props.recording ? 0 : 1,
          isCallingStatue: this.props.isCallingStatue,
          accessibilityLabel: ['home_page_record_screen_on', 'home_page_record_screen_off'],
          bottomTitle: this.props.showBottomTitle ? stringsTo('toolbar_text_record') : null,
        };
      case 'fullScreenBtn':
        return {
          isText: false,
          data: [require('./res/player_toolbar_fullscreen.png')],
          onPress: this.props.fullscreenPress,
          disabled: this.props.fullscreenDisabled,
          dataIndex: 0,
          isCallingStatue: this.props.isCallingStatue,
          accessibilityLabel: ['home_page_full_screen'],
          bottomTitle: this.props.showBottomTitle ? stringsTo('toolbar_text_fullscreen') : null,
        };
      default:
        return null;
    }
  }

  render() {
    let items = [];
    if (this.props.bottomBarMoreItems.length > 0) {
      this.props.bottomBarMoreItems.forEach((item, index) => {
        let btn;
        if (item.name && this.getBtnByName(item.name)) {
          btn = this.getBtnByName(item.name);
          btn.insertIndex = item.insertIndex;
        } else {
          btn = item.item;
        }
        if (item.insertIndex < items.length) {
          items.splice(btn.insertIndex, 0, btn.item);
        } else {
          items.push(btn);
        }
      });
    }

    return <PlayerToolBarView items={items} />;
  }
}
